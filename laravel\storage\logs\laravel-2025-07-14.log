[2025-07-14 04:46:24] local.INFO: header  
[2025-07-14 04:46:24] local.CRITICAL: ****************************1  
[2025-07-14 04:46:24] local.ALERT: reach here  
[2025-07-14 04:46:24] local.ALERT: array (
  'AMT' => 2400.0,
  'CType' => 0,
  'FID' => 85,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '101733159',
  'State' => 0,
  'lateflog' => '573648',
)  
[2025-07-14 04:46:24] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2025-07-14 04:46:24] local.ERROR: المبلغ  
[2025-07-14 04:46:24] local.ERROR: 2400.00  
[2025-07-14 04:46:24] local.ERROR: مبلغ وقدرة  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 3  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 3  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 3  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 3  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 3  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 3  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 3  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 3  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 3  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 2  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 2  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 2  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 2  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 2  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 2  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 2  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 1  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 1  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 1  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 1  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 1  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 2  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 10013  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 10013  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 10013  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 10013  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 1  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 3  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 3  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 40  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 40  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 40  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 40  
[2025-07-14 04:46:24] local.WARNING: 1  
[2025-07-14 04:46:24] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 40  
[2025-07-14 04:46:24] local.ALERT: 200  
[2025-07-14 04:46:24] local.ALERT: 10013  
[2025-07-14 04:46:24] local.CRITICAL: ****************************  
[2025-07-14 04:46:24] local.CRITICAL:   
[2025-07-14 04:46:24] local.CRITICAL: ****************************  
[2025-07-14 04:46:24] local.CRITICAL: ****************************2  
[2025-07-14 04:46:24] local.INFO: checkUser 1  
[2025-07-14 04:46:26] local.INFO: {
  "ClientBalanceResult": "22442.6463"
}  
[2025-07-14 04:46:26] local.INFO: array (
  'ClientBalanceResult' => '22442.6463',
)  
[2025-07-14 04:46:26] local.DEBUG: lattttef  
[2025-07-14 04:46:26] local.DEBUG: array (
  'ClientBalanceResult' => '22442.6463',
)  
[2025-07-14 04:46:26] local.INFO: transaction14  
[2025-07-14 04:46:26] local.INFO: first inquery phone = 101733159  
[2025-07-14 04:46:26] local.DEBUG: response querySubBalance  
[2025-07-14 04:46:26] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-14 04:46:26] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-14 04:46:26] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-14 04:46:26] local.DEBUG: print  before faction by provider price  
[2025-07-14 04:46:26] local.DEBUG: print  after faction by provider price  
[2025-07-14 04:46:26] local.INFO: thisAttempt to read property "Name" on null  
