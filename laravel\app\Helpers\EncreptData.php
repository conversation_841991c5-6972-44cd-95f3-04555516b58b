<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Http;

class EncreptData{
    public static function RestApiRakan($userToken,$operation_token_encrypt,$userSanaOrAden){

        // $user='776529802';  //chenge Data  USER ID
        $user = ($userSanaOrAden==1 ? 'nawafeth' : '777770954') ;


        $response = Http::timeout(60)->withHeaders([
            'SOAPAction' => "http://tempuri.org/IService/DoOperation"
        ])->accept('text/xml; charset=utf-8')

            ->withBody(
                <<<XML
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
          <soapenv:Header/>
                <soapenv:Body>
                   <tem:DoOperation>
                     <tem:UserID>$user</tem:UserID>
                     <tem:UserToken>$userToken</tem:UserToken>
                     <tem:OperationToken>$operation_token_encrypt</tem:OperationToken>
                 </tem:DoOperation>
              </soapenv:Body>
         </soapenv:Envelope>
        XML,
                'text/xml',
            )->post('http://aboosama.netcoinyemen.com:8020/AgentsService.svc');

            return $response;

    }

    public static function encryptDataRakan($plainText,$userSanaOrAden)
    {
        $key = ($userSanaOrAden==1 ? '1mfOfaCUa7BsgpxX' : 'XZI2HqtSO8M7wglt');
        $byte = mb_convert_encoding($key, 'ASCII');
        $desIv =  substr($byte, 0, 8);
        $ciphertext = openssl_encrypt($plainText, 'des-ede-cbc', $key, OPENSSL_RAW_DATA, $desIv);

        return $ciphertext = base64_encode($ciphertext);
    }

}
