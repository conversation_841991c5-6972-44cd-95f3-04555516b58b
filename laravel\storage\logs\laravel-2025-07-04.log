[2025-07-04 00:54:33] local.INFO: header  
[2025-07-04 00:54:33] local.INFO: header  
[2025-07-04 00:54:33] local.INFO: header  
[2025-07-04 00:54:35] local.CRITICAL: ****************************1  
[2025-07-04 00:54:35] local.ALERT: reach here  
iter  
[2025-07-04 00:54:35] local.INFO: Body  after fliter  
[2025-07-04 00:54:35] local.INFO: array (
)  
[2025-07-04 00:54:35] local.ALERT: array (
  'AMT' => 2400.0,
  'CType' => 0,
  'FID' => 85,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103336824',
  'State' => 0,
  'lateflog' => '584131',
)  
[2025-07-04 00:54:35] local.INFO: array (
)  
[2025-07-04 00:54:35] local.INFO: transaction14  
[2025-07-04 00:54:35] local.INFO: first inquery phone = 103336824  
[2025-07-04 00:54:35] local.INFO: first inquery phone = 103336824  
[2025-07-04 00:54:38] local.DEBUG: response querySubBalance  
[2025-07-04 00:54:38] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2025-07-04 00:54:38] local.DEBUG: response querySubBalance  
[2025-07-04 00:54:38] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-04 00:54:38] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-04 00:54:38] local.ERROR: 2400.00  
[2025-07-04 00:54:38] local.ERROR: مبلغ وقدرة  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 3  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 3  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 3  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 3  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 3  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 3  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 3  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 3  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 3  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 2  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 2  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 2  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 2  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 2  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 2  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 2  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 1  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 1  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 1  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 1  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 1  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 2  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 10013  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 10013  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 10013  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 10013  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 1  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 3  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 3  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 40  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 40  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 40  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 40  
[2025-07-04 00:54:38] local.WARNING: 1  
[2025-07-04 00:54:38] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 40  
[2025-07-04 00:54:38] local.ALERT: 200  
[2025-07-04 00:54:38] local.ALERT: 10013  
[2025-07-04 00:54:38] local.CRITICAL: ****************************  
[2025-07-04 00:54:38] local.CRITICAL:   
[2025-07-04 00:54:38] local.CRITICAL: ****************************  
[2025-07-04 00:54:38] local.CRITICAL: ****************************2  
[2025-07-04 00:54:38] local.INFO: checkUser 1  
[2025-07-04 00:54:38] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-04 00:54:38] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-04 00:54:38] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-04 00:54:38] local.DEBUG: print  before faction by provider price  
[2025-07-04 00:54:38] local.DEBUG: print  before faction by provider price  
[2025-07-04 00:54:38] local.INFO: {
  "ClientBalanceResult": "18294.0500"
}  
[2025-07-04 00:54:39] local.DEBUG: print  after faction by provider price  
[2025-07-04 00:54:39] local.DEBUG: print  after faction by provider price  
[2025-07-04 00:54:39] local.INFO: array (
  'ClientBalanceResult' => '18294.0500',
)  
[2025-07-04 00:54:39] local.DEBUG: lattttef  
[2025-07-04 00:54:39] local.DEBUG: array (
  'ClientBalanceResult' => '18294.0500',
)  
[2025-07-04 00:54:39] local.INFO: transaction14  
[2025-07-04 00:54:39] local.INFO: first inquery phone = 103336824  
[2025-07-04 00:54:39] local.DEBUG: response querySubBalance  
[2025-07-04 00:54:39] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-04 00:54:39] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-04 00:54:39] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-04 00:54:39] local.DEBUG: print  before faction by provider price  
[2025-07-04 00:54:39] local.DEBUG: print  after faction by provider price  
[2025-07-04 00:54:39] local.INFO: thisAttempt to read property "Name" on null  
[2025-07-04 01:38:54] local.INFO: header  
[2025-07-04 01:38:54] local.INFO: header after fliter  
[2025-07-04 01:38:54] local.INFO: Body  after fliter  
[2025-07-04 01:38:54] local.INFO: array (
  'Amount' => 260.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-04 01:38:54] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-04 01:38:54] local.ERROR: الكمية  
[2025-07-04 01:38:54] local.ERROR: سعر الوحدة  
[2025-07-04 01:38:54] local.ERROR: المبلغ  
[2025-07-04 01:38:54] local.ERROR: 1.210  
[2025-07-04 01:38:54] local.INFO: 214.88  
[2025-07-04 01:38:54] local.ERROR: مبلغ وقدرة  
[2025-07-04 01:38:54] local.INFO: الكمية  
[2025-07-04 01:38:54] local.INFO: سعر الوحدة  
[2025-07-04 01:38:54] local.INFO: المبلغ  
[2025-07-04 01:38:54] local.INFO: مبلغ وقدرة  
[2025-07-04 01:38:54] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-04 01:38:54] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '214.88',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '260.00',
  ),
)  
[2025-07-04 01:38:54] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '214.88',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '260.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-04 01:38:55] local.INFO: header  
[2025-07-04 01:38:55] local.CRITICAL: ****************************1  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 3  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 3  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 3  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 3  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 3  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 3  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 3  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 3  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 3  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 1  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 1  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 1  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 1  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 1  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 10013  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 10013  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 10013  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 10013  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 1  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 3  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 3  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 40  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 40  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 40  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 40  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 200  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 200  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 200  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 200  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 200  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 200  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 40  
[2025-07-04 01:38:55] local.ALERT: 2  
[2025-07-04 01:38:55] local.ALERT: 10013  
[2025-07-04 01:38:55] local.CRITICAL: ****************************  
[2025-07-04 01:38:55] local.CRITICAL:   
[2025-07-04 01:38:55] local.CRITICAL: ****************************  
[2025-07-04 01:38:55] local.CRITICAL: ****************************2  
[2025-07-04 01:38:55] local.INFO: checkUser 1  
[2025-07-04 01:38:57] local.INFO: {
  "ClientBalanceResult": "78611.4181"
}  
[2025-07-04 01:38:57] local.INFO: checkUser 2  
[2025-07-04 01:38:57] local.INFO: array (
  'ClientBalanceResult' => '78611.4181',
)  
[2025-07-04 01:38:57] local.INFO: 260  
[2025-07-04 01:38:57] local.ALERT: reach here  
[2025-07-04 01:38:57] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-04 01:38:57] local.ERROR: الكمية  
[2025-07-04 01:38:57] local.ERROR: سعر الوحدة  
[2025-07-04 01:38:57] local.ERROR: المبلغ  
[2025-07-04 01:38:57] local.ERROR: 1.210  
[2025-07-04 01:38:57] local.INFO: 1.210  
[2025-07-04 01:38:57] local.INFO: 214.876  
[2025-07-04 01:38:57] local.ERROR: 260.00  
[2025-07-04 01:38:57] local.INFO: checkUser 3  
[2025-07-04 01:38:57] local.INFO: checkUser 3#  
[2025-07-04 01:38:57] local.INFO: checkUser 4  
[2025-07-04 01:38:57] local.INFO: checkUser 4#  
[2025-07-04 01:38:57] local.INFO: checkUser 5  
[2025-07-04 01:38:57] local.DEBUG: lattttef  
[2025-07-04 01:38:57] local.DEBUG: array (
  'ClientBalanceResult' => '78611.4181',
)  
[2025-07-04 01:38:57] local.INFO: transaction1  
[2025-07-04 01:38:57] local.INFO: transaction2  
[2025-07-04 01:38:57] local.INFO: transaction3  
[2025-07-04 01:38:57] local.INFO: transaction4  
[2025-07-04 01:38:57] local.INFO: transaction4  
[2025-07-04 01:38:57] local.INFO: transaction5  
[2025-07-04 01:38:57] local.INFO: transaction6  
[2025-07-04 01:38:58] local.INFO: transaction7  
[2025-07-04 01:38:58] local.DEBUG: array (
  'AMT' => 260.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '214.876',
)  
[2025-07-04 01:38:58] local.INFO: transaction8  
[2025-07-04 01:39:36] local.INFO: transaction9  
[2025-07-04 01:39:36] local.INFO: 260  
[2025-07-04 01:39:36] local.INFO: transaction10  
[2025-07-04 01:39:36] local.INFO: 214.876  
[2025-07-04 01:39:36] local.INFO: 260.00  
[2025-07-04 01:39:36] local.INFO: transaction11  
[2025-07-04 01:39:36] local.INFO: 121  
[2025-07-04 01:39:36] local.INFO: topup1260.00  
[2025-07-04 01:39:36] local.INFO: topup21.210  
[2025-07-04 01:39:36] local.INFO: topup3260.00  
[2025-07-04 01:39:36] local.INFO: topup4260.00  
[2025-07-04 01:39:36] local.INFO: topup5260  
[2025-07-04 01:39:36] local.INFO: topup60  
[2025-07-04 01:39:36] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 260.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-04 01:39:36',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7316236,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-04 01:39:36',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '260.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '********013936',
  'Quantity' => '214.876',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '260.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '260.00',
  'TotalAmount' => 260.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 260.0,
  'ExCode' => NULL,
  'TransNumber' => '********013936',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#260#0',
  'ResponseTime' => '2025-07-04 01:39:36',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '36',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-04 01:39:36] local.INFO: transaction13  
[2025-07-04 01:39:36] local.INFO: transaction14  
[2025-07-04 01:39:36] local.INFO: transaction19  
[2025-07-04 01:39:36] local.INFO: transaction19#.  
[2025-07-04 01:39:36] local.INFO: transaction19#.  
[2025-07-04 01:39:36] local.INFO: transaction19#  
[2025-07-04 01:39:36] local.INFO: transaction19##  
[2025-07-04 01:39:36] local.INFO: transaction15  
[2025-07-04 01:39:38] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '260.00',
  'remainAmount' => ********,
  'mallrem' => -488458,
  'transid' => '7316236',
  'ref_id' => 92758715,
)  
[2025-07-04 03:25:44] local.INFO: header  
[2025-07-04 03:25:44] local.INFO: header after fliter  
[2025-07-04 03:25:44] local.INFO: Body  after fliter  
[2025-07-04 03:25:44] local.INFO: array (
  'Amount' => 450.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-04 03:25:44] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-04 03:25:44] local.ERROR: الكمية  
[2025-07-04 03:25:44] local.ERROR: سعر الوحدة  
[2025-07-04 03:25:44] local.ERROR: المبلغ  
[2025-07-04 03:25:44] local.ERROR: 1.210  
[2025-07-04 03:25:44] local.INFO: 371.90  
[2025-07-04 03:25:44] local.ERROR: مبلغ وقدرة  
[2025-07-04 03:25:44] local.INFO: الكمية  
[2025-07-04 03:25:44] local.INFO: سعر الوحدة  
[2025-07-04 03:25:44] local.INFO: المبلغ  
[2025-07-04 03:25:44] local.INFO: مبلغ وقدرة  
[2025-07-04 03:25:44] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-04 03:25:44] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '371.90',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '450.00',
  ),
)  
[2025-07-04 03:25:44] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '371.90',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '450.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-04 03:25:46] local.INFO: header  
[2025-07-04 03:25:46] local.CRITICAL: ****************************1  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 3  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 3  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 3  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 3  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 3  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 3  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 3  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 3  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 3  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 1  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 1  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 1  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 1  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 1  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 10013  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 10013  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 10013  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 10013  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 1  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 3  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 3  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 40  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 40  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 40  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 40  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 200  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 200  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 200  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 200  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 200  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 200  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 40  
[2025-07-04 03:25:46] local.ALERT: 2  
[2025-07-04 03:25:46] local.ALERT: 10013  
[2025-07-04 03:25:46] local.CRITICAL: ****************************  
[2025-07-04 03:25:46] local.CRITICAL:   
[2025-07-04 03:25:46] local.CRITICAL: ****************************  
[2025-07-04 03:25:46] local.CRITICAL: ****************************2  
[2025-07-04 03:25:46] local.INFO: checkUser 1  
[2025-07-04 03:25:47] local.INFO: {
  "ClientBalanceResult": "75689.4181"
}  
[2025-07-04 03:25:47] local.INFO: checkUser 2  
[2025-07-04 03:25:47] local.INFO: array (
  'ClientBalanceResult' => '75689.4181',
)  
[2025-07-04 03:25:47] local.INFO: 450  
[2025-07-04 03:25:47] local.ALERT: reach here  
[2025-07-04 03:25:47] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-04 03:25:47] local.ERROR: الكمية  
[2025-07-04 03:25:47] local.ERROR: سعر الوحدة  
[2025-07-04 03:25:47] local.ERROR: المبلغ  
[2025-07-04 03:25:47] local.ERROR: 1.210  
[2025-07-04 03:25:47] local.INFO: 1.210  
[2025-07-04 03:25:47] local.INFO: 371.901  
[2025-07-04 03:25:47] local.ERROR: 450.00  
[2025-07-04 03:25:47] local.INFO: checkUser 3  
[2025-07-04 03:25:47] local.INFO: checkUser 3#  
[2025-07-04 03:25:47] local.INFO: checkUser 4  
[2025-07-04 03:25:47] local.INFO: checkUser 4#  
[2025-07-04 03:25:47] local.INFO: checkUser 5  
[2025-07-04 03:25:47] local.DEBUG: lattttef  
[2025-07-04 03:25:47] local.DEBUG: array (
  'ClientBalanceResult' => '75689.4181',
)  
[2025-07-04 03:25:47] local.INFO: transaction1  
[2025-07-04 03:25:47] local.INFO: transaction2  
[2025-07-04 03:25:47] local.INFO: transaction3  
[2025-07-04 03:25:47] local.INFO: transaction4  
[2025-07-04 03:25:47] local.INFO: transaction4  
[2025-07-04 03:25:47] local.INFO: transaction5  
[2025-07-04 03:25:47] local.INFO: transaction6  
[2025-07-04 03:25:47] local.INFO: transaction7  
[2025-07-04 03:25:47] local.DEBUG: array (
  'AMT' => 450.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '371.901',
)  
[2025-07-04 03:25:47] local.INFO: transaction8  
[2025-07-04 03:25:47] local.INFO: transaction9  
[2025-07-04 03:25:47] local.INFO: 450  
[2025-07-04 03:25:47] local.INFO: transaction10  
[2025-07-04 03:25:47] local.INFO: 371.901  
[2025-07-04 03:25:47] local.INFO: 450.00  
[2025-07-04 03:25:47] local.INFO: transaction11  
[2025-07-04 03:25:47] local.INFO: 121  
[2025-07-04 03:25:47] local.INFO: topup1450.00  
[2025-07-04 03:25:47] local.INFO: topup21.210  
[2025-07-04 03:25:47] local.INFO: topup3450.00  
[2025-07-04 03:25:47] local.INFO: topup4450.00  
[2025-07-04 03:25:47] local.INFO: topup5450  
[2025-07-04 03:25:47] local.INFO: topup60  
[2025-07-04 03:25:47] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 450.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-04 03:25:47',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7316248,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-04 03:25:47',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '450.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '**************',
  'Quantity' => '371.901',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '450.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '450.00',
  'TotalAmount' => 450.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 450.0,
  'ExCode' => NULL,
  'TransNumber' => '**************',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#450#0',
  'ResponseTime' => '2025-07-04 03:25:47',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '47',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-04 03:25:47] local.INFO: transaction13  
[2025-07-04 03:25:47] local.INFO: transaction14  
[2025-07-04 03:25:47] local.INFO: transaction19  
[2025-07-04 03:25:47] local.INFO: transaction19#.  
[2025-07-04 03:25:47] local.INFO: transaction19#.  
[2025-07-04 03:25:47] local.INFO: transaction19#  
[2025-07-04 03:25:47] local.INFO: transaction19##  
[2025-07-04 03:25:47] local.INFO: transaction15  
[2025-07-04 03:25:49] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '450.00',
  'remainAmount' => 79507944,
  'mallrem' => -492056,
  'transid' => '7316248',
  'ref_id' => 92760117,
)  
[2025-07-04 17:41:53] local.INFO: header  
[2025-07-04 17:41:53] local.INFO: header after fliter  
[2025-07-04 17:41:53] local.INFO: Body  after fliter  
[2025-07-04 17:41:53] local.INFO: array (
)  
[2025-07-04 17:41:53] local.INFO: transaction14  
[2025-07-04 17:41:53] local.INFO: first inquery phone = 103325837  
[2025-07-04 17:41:54] local.DEBUG: response querySubBalance  
[2025-07-04 17:41:54] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-04 17:41:54] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-04 17:41:54] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-04 17:41:54] local.DEBUG: print  before faction by provider price  
[2025-07-04 17:41:54] local.DEBUG: print  after faction by provider price  
