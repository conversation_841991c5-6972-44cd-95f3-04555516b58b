[2025-07-02 00:52:01] local.INFO: header  
[2025-07-02 00:52:02] local.INFO: header after fliter  
[2025-07-02 00:52:02] local.INFO: Body  after fliter  
[2025-07-02 00:52:02] local.INFO: array (
  'Amount' => 170.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-02 00:52:03] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-02 00:52:03] local.ERROR: الكمية  
[2025-07-02 00:52:03] local.ERROR: سعر الوحدة  
[2025-07-02 00:52:03] local.ERROR: المبلغ  
[2025-07-02 00:52:03] local.ERROR: 1.210  
[2025-07-02 00:52:03] local.INFO: 140.50  
[2025-07-02 00:52:03] local.ERROR: مبلغ وقدرة  
[2025-07-02 00:52:03] local.INFO: الكمية  
[2025-07-02 00:52:03] local.INFO: سعر الوحدة  
[2025-07-02 00:52:03] local.INFO: المبلغ  
[2025-07-02 00:52:03] local.INFO: مبلغ وقدرة  
[2025-07-02 00:52:03] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-02 00:52:03] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '140.50',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '170.00',
  ),
)  
[2025-07-02 00:52:03] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '140.50',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '170.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-02 00:52:06] local.INFO: header  
[2025-07-02 00:52:07] local.CRITICAL: ****************************1  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 3  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 3  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 3  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 3  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 3  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 3  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 3  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 3  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 3  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 1  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 1  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 1  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 1  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 1  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 10013  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 10013  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 10013  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 10013  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 1  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 3  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 3  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 40  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 40  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 40  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 40  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 200  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 200  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 200  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 200  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 200  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 200  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 40  
[2025-07-02 00:52:07] local.ALERT: 2  
[2025-07-02 00:52:07] local.ALERT: 10013  
[2025-07-02 00:52:07] local.CRITICAL: ****************************  
[2025-07-02 00:52:07] local.CRITICAL:   
[2025-07-02 00:52:07] local.CRITICAL: ****************************  
[2025-07-02 00:52:07] local.CRITICAL: ****************************2  
[2025-07-02 00:52:07] local.INFO: checkUser 1  
[2025-07-02 00:52:08] local.INFO: {
  "ClientBalanceResult": "34630.5681"
}  
[2025-07-02 00:52:08] local.INFO: checkUser 2  
[2025-07-02 00:52:08] local.INFO: array (
  'ClientBalanceResult' => '34630.5681',
)  
[2025-07-02 00:52:08] local.INFO: 170  
[2025-07-02 00:52:08] local.ALERT: reach here  
[2025-07-02 00:52:08] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-02 00:52:08] local.ERROR: الكمية  
[2025-07-02 00:52:08] local.ERROR: سعر الوحدة  
[2025-07-02 00:52:08] local.ERROR: المبلغ  
[2025-07-02 00:52:08] local.ERROR: 1.210  
[2025-07-02 00:52:08] local.INFO: 1.210  
[2025-07-02 00:52:08] local.INFO: 140.496  
[2025-07-02 00:52:08] local.ERROR: 170.00  
[2025-07-02 00:52:08] local.INFO: checkUser 3  
[2025-07-02 00:52:08] local.INFO: checkUser 3#  
[2025-07-02 00:52:08] local.INFO: checkUser 4  
[2025-07-02 00:52:09] local.INFO: checkUser 4#  
[2025-07-02 00:52:09] local.INFO: checkUser 5  
[2025-07-02 00:52:09] local.DEBUG: lattttef  
[2025-07-02 00:52:09] local.DEBUG: array (
  'ClientBalanceResult' => '34630.5681',
)  
[2025-07-02 00:52:09] local.INFO: transaction1  
[2025-07-02 00:52:09] local.INFO: transaction2  
[2025-07-02 00:52:09] local.INFO: transaction3  
[2025-07-02 00:52:09] local.INFO: transaction4  
[2025-07-02 00:52:09] local.INFO: transaction4  
[2025-07-02 00:52:09] local.INFO: transaction5  
[2025-07-02 00:52:09] local.INFO: transaction6  
[2025-07-02 00:52:09] local.INFO: transaction7  
[2025-07-02 00:52:09] local.DEBUG: array (
  'AMT' => 170.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '140.496',
)  
[2025-07-02 00:52:09] local.INFO: transaction8  
[2025-07-02 00:52:09] local.INFO: transaction9  
[2025-07-02 00:52:09] local.INFO: 170  
[2025-07-02 00:52:09] local.INFO: transaction10  
[2025-07-02 00:52:09] local.INFO: 140.496  
[2025-07-02 00:52:09] local.INFO: 170.00  
[2025-07-02 00:52:09] local.INFO: transaction11  
[2025-07-02 00:52:09] local.INFO: 121  
[2025-07-02 00:52:09] local.INFO: topup1170.00  
[2025-07-02 00:52:09] local.INFO: topup21.210  
[2025-07-02 00:52:09] local.INFO: topup3170.00  
[2025-07-02 00:52:09] local.INFO: topup4170.00  
[2025-07-02 00:52:09] local.INFO: topup5170  
[2025-07-02 00:52:09] local.INFO: topup60  
[2025-07-02 00:52:09] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 170.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-02 00:52:09',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7314598,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-02 00:52:09',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '170.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '**************',
  'Quantity' => '140.496',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '170.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '170.00',
  'TotalAmount' => 170.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 170.0,
  'ExCode' => NULL,
  'TransNumber' => '**************',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#170#0',
  'ResponseTime' => '2025-07-02 00:52:09',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '09',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-02 00:52:09] local.INFO: transaction13  
[2025-07-02 00:52:10] local.INFO: transaction14  
[2025-07-02 00:52:10] local.INFO: transaction19  
[2025-07-02 00:52:10] local.INFO: transaction19#.  
[2025-07-02 00:52:10] local.INFO: transaction19#.  
[2025-07-02 00:52:10] local.INFO: transaction19#  
[2025-07-02 00:52:10] local.INFO: transaction19##  
[2025-07-02 00:52:10] local.INFO: transaction15  
[2025-07-02 00:52:11] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '170.00',
  'remainAmount' => ********,
  'mallrem' => -186292,
  'transid' => '7314598',
  'ref_id' => 92534661,
)  
[2025-07-02 00:55:05] local.INFO: header  
[2025-07-02 00:55:05] local.INFO: header after fliter  
[2025-07-02 00:55:05] local.INFO: Body  after fliter  
[2025-07-02 00:55:05] local.INFO: array (
  'Amount' => 450.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-02 00:55:05] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-02 00:55:05] local.ERROR: الكمية  
[2025-07-02 00:55:05] local.ERROR: سعر الوحدة  
[2025-07-02 00:55:05] local.ERROR: المبلغ  
[2025-07-02 00:55:05] local.ERROR: 1.210  
[2025-07-02 00:55:05] local.INFO: 371.90  
[2025-07-02 00:55:05] local.ERROR: مبلغ وقدرة  
[2025-07-02 00:55:05] local.INFO: الكمية  
[2025-07-02 00:55:05] local.INFO: سعر الوحدة  
[2025-07-02 00:55:05] local.INFO: المبلغ  
[2025-07-02 00:55:05] local.INFO: مبلغ وقدرة  
[2025-07-02 00:55:05] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-02 00:55:05] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '371.90',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '450.00',
  ),
)  
[2025-07-02 00:55:05] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '371.90',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '450.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-02 00:55:06] local.INFO: header  
[2025-07-02 00:55:06] local.CRITICAL: ****************************1  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 3  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 3  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 3  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 3  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 3  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 3  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 3  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 3  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 3  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 1  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 1  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 1  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 1  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 1  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 10013  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 10013  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 10013  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 10013  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 1  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 3  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 3  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 40  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 40  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 40  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 40  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 200  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 200  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 200  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 200  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 200  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 200  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 40  
[2025-07-02 00:55:06] local.ALERT: 2  
[2025-07-02 00:55:06] local.ALERT: 10013  
[2025-07-02 00:55:06] local.CRITICAL: ****************************  
[2025-07-02 00:55:06] local.CRITICAL:   
[2025-07-02 00:55:06] local.CRITICAL: ****************************  
[2025-07-02 00:55:06] local.CRITICAL: ****************************2  
[2025-07-02 00:55:06] local.INFO: checkUser 1  
[2025-07-02 00:55:08] local.INFO: {
  "ClientBalanceResult": "34460.5681"
}  
[2025-07-02 00:55:08] local.INFO: checkUser 2  
[2025-07-02 00:55:08] local.INFO: array (
  'ClientBalanceResult' => '34460.5681',
)  
[2025-07-02 00:55:08] local.INFO: 450  
[2025-07-02 00:55:08] local.ALERT: reach here  
[2025-07-02 00:55:08] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-02 00:55:08] local.ERROR: الكمية  
[2025-07-02 00:55:08] local.ERROR: سعر الوحدة  
[2025-07-02 00:55:08] local.ERROR: المبلغ  
[2025-07-02 00:55:08] local.ERROR: 1.210  
[2025-07-02 00:55:08] local.INFO: 1.210  
[2025-07-02 00:55:08] local.INFO: 371.901  
[2025-07-02 00:55:08] local.ERROR: 450.00  
[2025-07-02 00:55:08] local.INFO: checkUser 3  
[2025-07-02 00:55:08] local.INFO: checkUser 3#  
[2025-07-02 00:55:08] local.INFO: checkUser 4  
[2025-07-02 00:55:08] local.INFO: checkUser 4#  
[2025-07-02 00:55:08] local.INFO: checkUser 5  
[2025-07-02 00:55:08] local.DEBUG: lattttef  
[2025-07-02 00:55:08] local.DEBUG: array (
  'ClientBalanceResult' => '34460.5681',
)  
[2025-07-02 00:55:08] local.INFO: transaction1  
[2025-07-02 00:55:08] local.INFO: transaction2  
[2025-07-02 00:55:08] local.INFO: transaction3  
[2025-07-02 00:55:08] local.INFO: transaction4  
[2025-07-02 00:55:08] local.INFO: transaction4  
[2025-07-02 00:55:08] local.INFO: transaction5  
[2025-07-02 00:55:08] local.INFO: transaction6  
[2025-07-02 00:55:08] local.INFO: transaction7  
[2025-07-02 00:55:08] local.DEBUG: array (
  'AMT' => 450.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '371.901',
)  
[2025-07-02 00:55:08] local.INFO: transaction8  
[2025-07-02 00:55:08] local.INFO: transaction9  
[2025-07-02 00:55:08] local.INFO: 450  
[2025-07-02 00:55:08] local.INFO: transaction10  
[2025-07-02 00:55:08] local.INFO: 371.901  
[2025-07-02 00:55:08] local.INFO: 450.00  
[2025-07-02 00:55:08] local.INFO: transaction11  
[2025-07-02 00:55:08] local.INFO: 121  
[2025-07-02 00:55:08] local.INFO: topup1450.00  
[2025-07-02 00:55:08] local.INFO: topup21.210  
[2025-07-02 00:55:08] local.INFO: topup3450.00  
[2025-07-02 00:55:08] local.INFO: topup4450.00  
[2025-07-02 00:55:08] local.INFO: topup5450  
[2025-07-02 00:55:08] local.INFO: topup60  
[2025-07-02 00:55:08] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 450.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-02 00:55:08',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7314599,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-02 00:55:08',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '450.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '********005508',
  'Quantity' => '371.901',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '450.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '450.00',
  'TotalAmount' => 450.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 450.0,
  'ExCode' => NULL,
  'TransNumber' => '********005508',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#450#0',
  'ResponseTime' => '2025-07-02 00:55:08',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '08',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-02 00:55:08] local.INFO: transaction13  
[2025-07-02 00:55:08] local.INFO: transaction14  
[2025-07-02 00:55:08] local.INFO: transaction19  
[2025-07-02 00:55:08] local.INFO: transaction19#.  
[2025-07-02 00:55:08] local.INFO: transaction19#.  
[2025-07-02 00:55:08] local.INFO: transaction19#  
[2025-07-02 00:55:08] local.INFO: transaction19##  
[2025-07-02 00:55:08] local.INFO: transaction15  
[2025-07-02 00:55:10] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '450.00',
  'remainAmount' => ********,
  'mallrem' => -186742,
  'transid' => '7314599',
  'ref_id' => 92534777,
)  
[2025-07-02 16:40:34] local.INFO: header  
[2025-07-02 16:40:35] local.INFO: header after fliter  
[2025-07-02 16:40:35] local.INFO: Body  after fliter  
[2025-07-02 16:40:35] local.INFO: array (
  'Amount' => 400.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-02 16:40:37] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-02 16:40:37] local.ERROR: الكمية  
[2025-07-02 16:40:37] local.ERROR: سعر الوحدة  
[2025-07-02 16:40:37] local.ERROR: المبلغ  
[2025-07-02 16:40:37] local.ERROR: 1.210  
[2025-07-02 16:40:37] local.INFO: 330.58  
[2025-07-02 16:40:37] local.ERROR: مبلغ وقدرة  
[2025-07-02 16:40:37] local.INFO: الكمية  
[2025-07-02 16:40:37] local.INFO: سعر الوحدة  
[2025-07-02 16:40:37] local.INFO: المبلغ  
[2025-07-02 16:40:37] local.INFO: مبلغ وقدرة  
[2025-07-02 16:40:37] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-02 16:40:37] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '330.58',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '400.00',
  ),
)  
[2025-07-02 16:40:37] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '330.58',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '400.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-02 16:40:42] local.INFO: header  
[2025-07-02 16:40:42] local.CRITICAL: ****************************1  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 3  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 3  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 3  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 3  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 3  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 3  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 3  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 3  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 3  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 1  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 1  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 1  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 1  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 1  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 10013  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 10013  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 10013  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 10013  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 1  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 3  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 3  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 40  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 40  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 40  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 40  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 200  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 200  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 200  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 200  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 200  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 200  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 40  
[2025-07-02 16:40:42] local.ALERT: 2  
[2025-07-02 16:40:42] local.ALERT: 10013  
[2025-07-02 16:40:42] local.CRITICAL: ****************************  
[2025-07-02 16:40:42] local.CRITICAL:   
[2025-07-02 16:40:42] local.CRITICAL: ****************************  
[2025-07-02 16:40:42] local.CRITICAL: ****************************2  
[2025-07-02 16:40:42] local.INFO: checkUser 1  
[2025-07-02 16:40:42] local.INFO: {
  "ClientBalanceResult": "185.7107"
}  
[2025-07-02 16:40:42] local.INFO: checkUser 2  
[2025-07-02 16:40:42] local.INFO: array (
  'ClientBalanceResult' => '185.7107',
)  
[2025-07-02 16:40:42] local.INFO: 400  
[2025-07-02 16:40:42] local.ALERT: reach here  
[2025-07-02 16:40:42] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-02 16:40:42] local.ERROR: الكمية  
[2025-07-02 16:40:42] local.ERROR: سعر الوحدة  
[2025-07-02 16:40:42] local.ERROR: المبلغ  
[2025-07-02 16:40:42] local.ERROR: 1.210  
[2025-07-02 16:40:42] local.INFO: 1.210  
[2025-07-02 16:40:42] local.INFO: 330.579  
[2025-07-02 16:40:42] local.ERROR: 400.00  
[2025-07-02 16:40:42] local.INFO: checkUser 3  
[2025-07-02 16:40:42] local.INFO: checkUser 3#  
[2025-07-02 16:40:42] local.INFO: checkUser 4  
[2025-07-02 16:40:43] local.INFO: checkUser 4#  
[2025-07-02 16:40:43] local.INFO: checkUser -4  
[2025-07-02 16:40:43] local.INFO: price less than Balance  
[2025-07-02 16:40:43] local.INFO: thisprice less than Balance  
[2025-07-02 16:41:12] local.INFO: header  
[2025-07-02 16:41:12] local.INFO: header after fliter  
[2025-07-02 16:41:12] local.INFO: Body  after fliter  
[2025-07-02 16:41:12] local.INFO: array (
  'Amount' => 150.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-02 16:41:12] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-02 16:41:12] local.ERROR: الكمية  
[2025-07-02 16:41:12] local.ERROR: سعر الوحدة  
[2025-07-02 16:41:12] local.ERROR: المبلغ  
[2025-07-02 16:41:12] local.ERROR: 1.210  
[2025-07-02 16:41:12] local.INFO: 123.97  
[2025-07-02 16:41:12] local.ERROR: مبلغ وقدرة  
[2025-07-02 16:41:12] local.INFO: الكمية  
[2025-07-02 16:41:12] local.INFO: سعر الوحدة  
[2025-07-02 16:41:12] local.INFO: المبلغ  
[2025-07-02 16:41:12] local.INFO: مبلغ وقدرة  
[2025-07-02 16:41:12] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-02 16:41:12] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '123.97',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '150.00',
  ),
)  
[2025-07-02 16:41:12] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '123.97',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '150.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-02 16:41:14] local.INFO: header  
[2025-07-02 16:41:14] local.CRITICAL: ****************************1  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 3  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 3  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 3  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 3  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 3  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 3  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 3  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 3  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 3  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 1  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 1  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 1  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 1  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 1  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 10013  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 10013  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 10013  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 10013  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 1  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 3  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 3  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 40  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 40  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 40  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 40  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 200  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 200  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 200  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 200  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 200  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 200  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 40  
[2025-07-02 16:41:14] local.ALERT: 2  
[2025-07-02 16:41:14] local.ALERT: 10013  
[2025-07-02 16:41:14] local.CRITICAL: ****************************  
[2025-07-02 16:41:14] local.CRITICAL:   
[2025-07-02 16:41:14] local.CRITICAL: ****************************  
[2025-07-02 16:41:14] local.CRITICAL: ****************************2  
[2025-07-02 16:41:14] local.INFO: checkUser 1  
[2025-07-02 16:41:14] local.INFO: {
  "ClientBalanceResult": "185.7107"
}  
[2025-07-02 16:41:14] local.INFO: checkUser 2  
[2025-07-02 16:41:14] local.INFO: array (
  'ClientBalanceResult' => '185.7107',
)  
[2025-07-02 16:41:14] local.INFO: 150  
[2025-07-02 16:41:14] local.ALERT: reach here  
[2025-07-02 16:41:14] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-02 16:41:14] local.ERROR: الكمية  
[2025-07-02 16:41:14] local.ERROR: سعر الوحدة  
[2025-07-02 16:41:14] local.ERROR: المبلغ  
[2025-07-02 16:41:14] local.ERROR: 1.210  
[2025-07-02 16:41:14] local.INFO: 1.210  
[2025-07-02 16:41:14] local.INFO: 123.967  
[2025-07-02 16:41:14] local.ERROR: 150.00  
[2025-07-02 16:41:14] local.INFO: checkUser 3  
[2025-07-02 16:41:14] local.INFO: checkUser 3#  
[2025-07-02 16:41:14] local.INFO: checkUser 4  
[2025-07-02 16:41:14] local.INFO: checkUser 4#  
[2025-07-02 16:41:14] local.INFO: checkUser 5  
[2025-07-02 16:41:14] local.DEBUG: lattttef  
[2025-07-02 16:41:14] local.DEBUG: array (
  'ClientBalanceResult' => '185.7107',
)  
[2025-07-02 16:41:14] local.INFO: transaction1  
[2025-07-02 16:41:14] local.INFO: transaction2  
[2025-07-02 16:41:14] local.INFO: transaction3  
[2025-07-02 16:41:14] local.INFO: transaction4  
[2025-07-02 16:41:14] local.INFO: transaction4  
[2025-07-02 16:41:15] local.INFO: transaction5  
[2025-07-02 16:41:15] local.INFO: transaction6  
[2025-07-02 16:41:15] local.INFO: transaction7  
[2025-07-02 16:41:15] local.DEBUG: array (
  'AMT' => 150.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561439',
  'mLtype' => '1',
  'LATEFnum' => '123.967',
)  
[2025-07-02 16:41:15] local.INFO: transaction8  
[2025-07-02 16:41:15] local.INFO: transaction9  
[2025-07-02 16:41:15] local.INFO: 150  
[2025-07-02 16:41:15] local.INFO: transaction10  
[2025-07-02 16:41:15] local.INFO: 123.967  
[2025-07-02 16:41:15] local.INFO: 150.00  
[2025-07-02 16:41:15] local.INFO: transaction11  
[2025-07-02 16:41:15] local.INFO: 121  
[2025-07-02 16:41:15] local.INFO: topup1150.00  
[2025-07-02 16:41:15] local.INFO: topup21.210  
[2025-07-02 16:41:15] local.INFO: topup3150.00  
[2025-07-02 16:41:15] local.INFO: topup4150.00  
[2025-07-02 16:41:15] local.INFO: topup5150  
[2025-07-02 16:41:15] local.INFO: topup60  
[2025-07-02 16:41:15] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 150.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-02 16:41:15',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561439',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7314873,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558343',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-02 16:41:15',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '150.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '********164115',
  'Quantity' => '123.967',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '150.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '150.00',
  'TotalAmount' => 150.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 1,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 150.0,
  'ExCode' => NULL,
  'TransNumber' => '********164115',
  'OperationID' => 0,
  'AccountID' => '561267',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'fa76f60ee1102cf3',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#150#0',
  'ResponseTime' => '2025-07-02 16:41:15',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '15',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-02 16:41:15] local.INFO: transaction13  
[2025-07-02 16:41:15] local.INFO: transaction14  
[2025-07-02 16:41:15] local.INFO: transaction19  
[2025-07-02 16:41:15] local.INFO: transaction19#.  
[2025-07-02 16:41:15] local.INFO: transaction19#.  
[2025-07-02 16:41:15] local.INFO: transaction19#  
[2025-07-02 16:41:15] local.INFO: transaction19##  
[2025-07-02 16:41:15] local.INFO: transaction15  
[2025-07-02 16:41:22] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '150.00',
  'remainAmount' => ********,
  'mallrem' => -243112,
  'transid' => '7314873',
  'ref_id' => 92585586,
)  
[2025-07-02 19:11:13] local.INFO: header  
[2025-07-02 19:11:13] local.CRITICAL: ****************************1  
[2025-07-02 19:11:13] local.ALERT: reach here  
[2025-07-02 19:11:13] local.ALERT: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103385775',
  'State' => 0,
  'lateflog' => '584131',
)  
[2025-07-02 19:11:13] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2025-07-02 19:11:13] local.ERROR: المبلغ  
[2025-07-02 19:11:13] local.ERROR: 4000.00  
[2025-07-02 19:11:13] local.ERROR: مبلغ وقدرة  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 3  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 3  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 3  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 3  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 3  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 3  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 3  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 3  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 3  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 2  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 2  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 2  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 2  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 2  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 2  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 2  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 1  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 1  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 1  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 1  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 1  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 2  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 10013  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 10013  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 10013  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 10013  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 1  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 3  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 3  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 40  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 40  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 40  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 40  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.WARNING: 1  
[2025-07-02 19:11:13] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 40  
[2025-07-02 19:11:13] local.ALERT: 200  
[2025-07-02 19:11:13] local.ALERT: 10013  
[2025-07-02 19:11:13] local.CRITICAL: ****************************  
[2025-07-02 19:11:13] local.CRITICAL:   
[2025-07-02 19:11:13] local.CRITICAL: ****************************  
[2025-07-02 19:11:13] local.CRITICAL: ****************************2  
[2025-07-02 19:11:13] local.INFO: checkUser 1  
[2025-07-02 19:11:13] local.INFO: {
  "ClientBalanceResult": "18294.0500"
}  
[2025-07-02 19:11:13] local.INFO: array (
  'ClientBalanceResult' => '18294.0500',
)  
[2025-07-02 19:11:13] local.DEBUG: lattttef  
[2025-07-02 19:11:13] local.DEBUG: array (
  'ClientBalanceResult' => '18294.0500',
)  
[2025-07-02 19:11:13] local.INFO: transaction14  
[2025-07-02 19:11:13] local.INFO: first inquery phone = 103385775  
[2025-07-02 19:11:13] local.DEBUG: response querySubBalance  
[2025-07-02 19:11:13] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-02 19:11:13] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-02 19:11:13] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-02 19:11:13] local.DEBUG: print  before faction by provider price  
[2025-07-02 19:11:13] local.DEBUG: print  after faction by provider price  
[2025-07-02 19:11:13] local.INFO: thisAttempt to read property "Name" on null  
[2025-07-02 19:11:20] local.INFO: header  
[2025-07-02 19:11:20] local.CRITICAL: ****************************1  
[2025-07-02 19:11:20] local.ALERT: reach here  
[2025-07-02 19:11:20] local.ALERT: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103385775',
  'State' => 0,
  'lateflog' => '584131',
)  
[2025-07-02 19:11:20] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2025-07-02 19:11:20] local.ERROR: المبلغ  
[2025-07-02 19:11:20] local.ERROR: 4000.00  
[2025-07-02 19:11:20] local.ERROR: مبلغ وقدرة  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 3  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 3  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 3  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 3  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 3  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 3  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 3  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 3  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 3  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 2  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 2  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 2  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 2  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 2  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 2  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 2  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 1  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 1  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 1  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 1  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 1  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 2  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 10013  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 10013  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 10013  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 10013  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 1  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 3  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 3  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 40  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 40  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 40  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 40  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.WARNING: 1  
[2025-07-02 19:11:20] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 40  
[2025-07-02 19:11:20] local.ALERT: 200  
[2025-07-02 19:11:20] local.ALERT: 10013  
[2025-07-02 19:11:20] local.CRITICAL: ****************************  
[2025-07-02 19:11:20] local.CRITICAL:   
[2025-07-02 19:11:20] local.CRITICAL: ****************************  
[2025-07-02 19:11:20] local.CRITICAL: ****************************2  
[2025-07-02 19:11:20] local.INFO: checkUser 1  
[2025-07-02 19:11:20] local.INFO: {
  "ClientBalanceResult": "18294.0500"
}  
[2025-07-02 19:11:20] local.INFO: array (
  'ClientBalanceResult' => '18294.0500',
)  
[2025-07-02 19:11:20] local.DEBUG: lattttef  
[2025-07-02 19:11:20] local.DEBUG: array (
  'ClientBalanceResult' => '18294.0500',
)  
[2025-07-02 19:11:20] local.INFO: transaction14  
[2025-07-02 19:11:20] local.INFO: first inquery phone = 103385775  
[2025-07-02 19:11:20] local.DEBUG: response querySubBalance  
[2025-07-02 19:11:20] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-02 19:11:20] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-02 19:11:20] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-02 19:11:20] local.DEBUG: print  before faction by provider price  
[2025-07-02 19:11:20] local.DEBUG: print  after faction by provider price  
[2025-07-02 19:11:20] local.INFO: thisAttempt to read property "Name" on null  
