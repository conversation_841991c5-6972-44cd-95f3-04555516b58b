[2025-07-09 17:24:26] local.INFO: header  
[2025-07-09 17:24:27] local.INFO: header after fliter  
[2025-07-09 17:24:27] local.INFO: Body  after fliter  
[2025-07-09 17:24:27] local.INFO: array (
  'Amount' => 450.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-09 17:24:29] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-09 17:24:29] local.ERROR: الكمية  
[2025-07-09 17:24:29] local.ERROR: سعر الوحدة  
[2025-07-09 17:24:29] local.ERROR: المبلغ  
[2025-07-09 17:24:29] local.ERROR: 1.210  
[2025-07-09 17:24:29] local.INFO: 371.90  
[2025-07-09 17:24:29] local.ERROR: مبلغ وقدرة  
[2025-07-09 17:24:29] local.INFO: الكمية  
[2025-07-09 17:24:29] local.INFO: سعر الوحدة  
[2025-07-09 17:24:29] local.INFO: المبلغ  
[2025-07-09 17:24:29] local.INFO: مبلغ وقدرة  
[2025-07-09 17:24:29] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-09 17:24:29] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '371.90',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '450.00',
  ),
)  
[2025-07-09 17:24:29] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '371.90',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '450.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-09 17:24:51] local.INFO: header  
[2025-07-09 17:24:51] local.INFO: header after fliter  
[2025-07-09 17:24:51] local.INFO: Body  after fliter  
[2025-07-09 17:24:51] local.INFO: array (
  'Amount' => 450.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-09 17:24:51] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-09 17:24:51] local.ERROR: الكمية  
[2025-07-09 17:24:51] local.ERROR: سعر الوحدة  
[2025-07-09 17:24:51] local.ERROR: المبلغ  
[2025-07-09 17:24:51] local.ERROR: 1.210  
[2025-07-09 17:24:51] local.INFO: 371.90  
[2025-07-09 17:24:51] local.ERROR: مبلغ وقدرة  
[2025-07-09 17:24:51] local.INFO: الكمية  
[2025-07-09 17:24:51] local.INFO: سعر الوحدة  
[2025-07-09 17:24:51] local.INFO: المبلغ  
[2025-07-09 17:24:51] local.INFO: مبلغ وقدرة  
[2025-07-09 17:24:51] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-09 17:24:51] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '371.90',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '450.00',
  ),
)  
[2025-07-09 17:24:51] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '371.90',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '450.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-09 17:24:53] local.INFO: header  
[2025-07-09 17:24:53] local.CRITICAL: ****************************1  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 3  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 3  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 3  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 3  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 3  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 3  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 3  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 3  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 3  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 1  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 1  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 1  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 1  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 1  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 10013  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 10013  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 10013  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 10013  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 1  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 3  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 3  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 40  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 40  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 40  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 40  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 200  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 200  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 200  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 200  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 200  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 200  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 40  
[2025-07-09 17:24:53] local.ALERT: 2  
[2025-07-09 17:24:53] local.ALERT: 10013  
[2025-07-09 17:24:53] local.CRITICAL: ****************************  
[2025-07-09 17:24:53] local.CRITICAL:   
[2025-07-09 17:24:53] local.CRITICAL: ****************************  
[2025-07-09 17:24:53] local.CRITICAL: ****************************2  
[2025-07-09 17:24:53] local.INFO: checkUser 1  
[2025-07-09 17:24:54] local.INFO: {
  "ClientBalanceResult": "78397.0381"
}  
[2025-07-09 17:24:54] local.INFO: checkUser 2  
[2025-07-09 17:24:54] local.INFO: array (
  'ClientBalanceResult' => '78397.0381',
)  
[2025-07-09 17:24:54] local.INFO: 450  
[2025-07-09 17:24:54] local.ALERT: reach here  
[2025-07-09 17:24:54] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-09 17:24:54] local.ERROR: الكمية  
[2025-07-09 17:24:54] local.ERROR: سعر الوحدة  
[2025-07-09 17:24:54] local.ERROR: المبلغ  
[2025-07-09 17:24:54] local.ERROR: 1.210  
[2025-07-09 17:24:54] local.INFO: 1.210  
[2025-07-09 17:24:54] local.INFO: 371.901  
[2025-07-09 17:24:54] local.ERROR: 450.00  
[2025-07-09 17:24:54] local.INFO: checkUser 3  
[2025-07-09 17:24:54] local.INFO: checkUser 3#  
[2025-07-09 17:24:54] local.INFO: checkUser 4  
[2025-07-09 17:24:55] local.INFO: checkUser 4#  
[2025-07-09 17:24:55] local.INFO: checkUser 5  
[2025-07-09 17:24:55] local.DEBUG: lattttef  
[2025-07-09 17:24:55] local.DEBUG: array (
  'ClientBalanceResult' => '78397.0381',
)  
[2025-07-09 17:24:55] local.INFO: transaction1  
[2025-07-09 17:24:55] local.INFO: transaction2  
[2025-07-09 17:24:55] local.INFO: transaction3  
[2025-07-09 17:24:55] local.INFO: transaction4  
[2025-07-09 17:24:55] local.INFO: transaction4  
[2025-07-09 17:24:55] local.INFO: transaction5  
[2025-07-09 17:24:55] local.INFO: transaction6  
[2025-07-09 17:24:55] local.INFO: transaction7  
[2025-07-09 17:24:55] local.DEBUG: array (
  'AMT' => 450.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '371.901',
)  
[2025-07-09 17:24:55] local.INFO: transaction8  
[2025-07-09 17:24:56] local.INFO: transaction9  
[2025-07-09 17:24:56] local.INFO: 450  
[2025-07-09 17:24:56] local.INFO: transaction10  
[2025-07-09 17:24:56] local.INFO: 371.901  
[2025-07-09 17:24:56] local.INFO: 450.00  
[2025-07-09 17:24:56] local.INFO: transaction11  
[2025-07-09 17:24:56] local.INFO: 121  
[2025-07-09 17:24:56] local.INFO: topup1450.00  
[2025-07-09 17:24:56] local.INFO: topup21.210  
[2025-07-09 17:24:56] local.INFO: topup3450.00  
[2025-07-09 17:24:56] local.INFO: topup4450.00  
[2025-07-09 17:24:56] local.INFO: topup5450  
[2025-07-09 17:24:56] local.INFO: topup60  
[2025-07-09 17:24:56] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 450.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-09 17:24:56',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7339593,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-09 17:24:56',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '450.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '**************',
  'Quantity' => '371.901',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '450.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '450.00',
  'TotalAmount' => 450.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 450.0,
  'ExCode' => NULL,
  'TransNumber' => '**************',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#450#0',
  'ResponseTime' => '2025-07-09 17:24:56',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '56',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-09 17:24:56] local.INFO: transaction13  
[2025-07-09 17:24:56] local.INFO: transaction14  
[2025-07-09 17:24:56] local.INFO: transaction19  
[2025-07-09 17:24:56] local.INFO: transaction19#.  
[2025-07-09 17:24:56] local.INFO: transaction19#.  
[2025-07-09 17:24:56] local.INFO: transaction19#  
[2025-07-09 17:24:56] local.INFO: transaction19##  
[2025-07-09 17:24:56] local.INFO: transaction15  
[2025-07-09 17:24:57] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '450.00',
  'remainAmount' => ********,
  'mallrem' => -1347864,
  'transid' => '7339593',
  'ref_id' => 93385995,
)  
[2025-07-09 20:02:19] local.INFO: header  
[2025-07-09 20:02:19] local.INFO: header after fliter  
[2025-07-09 20:02:19] local.INFO: Body  after fliter  
[2025-07-09 20:02:19] local.INFO: array (
  'Amount' => 155.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-09 20:02:19] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-09 20:02:19] local.ERROR: الكمية  
[2025-07-09 20:02:19] local.ERROR: سعر الوحدة  
[2025-07-09 20:02:19] local.ERROR: المبلغ  
[2025-07-09 20:02:19] local.ERROR: 1.210  
[2025-07-09 20:02:19] local.INFO: 128.10  
[2025-07-09 20:02:19] local.ERROR: مبلغ وقدرة  
[2025-07-09 20:02:19] local.INFO: الكمية  
[2025-07-09 20:02:19] local.INFO: سعر الوحدة  
[2025-07-09 20:02:19] local.INFO: المبلغ  
[2025-07-09 20:02:19] local.INFO: مبلغ وقدرة  
[2025-07-09 20:02:19] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-09 20:02:19] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '128.10',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '155.00',
  ),
)  
[2025-07-09 20:02:19] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '128.10',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '155.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-09 20:02:23] local.INFO: header  
[2025-07-09 20:02:23] local.CRITICAL: ****************************1  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 3  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 3  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 3  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 3  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 3  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 3  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 3  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 3  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 3  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 1  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 1  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 1  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 1  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 1  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 10013  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 10013  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 10013  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 10013  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 1  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 3  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 3  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 40  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 40  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 40  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 40  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 200  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 200  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 200  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 200  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 200  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 200  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 40  
[2025-07-09 20:02:23] local.ALERT: 2  
[2025-07-09 20:02:23] local.ALERT: 10013  
[2025-07-09 20:02:23] local.CRITICAL: ****************************  
[2025-07-09 20:02:23] local.CRITICAL:   
[2025-07-09 20:02:23] local.CRITICAL: ****************************  
[2025-07-09 20:02:23] local.CRITICAL: ****************************2  
[2025-07-09 20:02:23] local.INFO: checkUser 1  
[2025-07-09 20:02:24] local.INFO: {
  "ClientBalanceResult": "76434.5381"
}  
[2025-07-09 20:02:24] local.INFO: checkUser 2  
[2025-07-09 20:02:24] local.INFO: array (
  'ClientBalanceResult' => '76434.5381',
)  
[2025-07-09 20:02:24] local.INFO: 155  
[2025-07-09 20:02:24] local.ALERT: reach here  
[2025-07-09 20:02:24] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-09 20:02:24] local.ERROR: الكمية  
[2025-07-09 20:02:24] local.ERROR: سعر الوحدة  
[2025-07-09 20:02:24] local.ERROR: المبلغ  
[2025-07-09 20:02:24] local.ERROR: 1.210  
[2025-07-09 20:02:24] local.INFO: 1.210  
[2025-07-09 20:02:24] local.INFO: 128.099  
[2025-07-09 20:02:24] local.ERROR: 155.00  
[2025-07-09 20:02:24] local.INFO: checkUser 3  
[2025-07-09 20:02:24] local.INFO: checkUser 3#  
[2025-07-09 20:02:24] local.INFO: checkUser 4  
[2025-07-09 20:02:24] local.INFO: checkUser 4#  
[2025-07-09 20:02:24] local.INFO: checkUser 5  
[2025-07-09 20:02:24] local.DEBUG: lattttef  
[2025-07-09 20:02:24] local.DEBUG: array (
  'ClientBalanceResult' => '76434.5381',
)  
[2025-07-09 20:02:24] local.INFO: transaction1  
[2025-07-09 20:02:24] local.INFO: transaction2  
[2025-07-09 20:02:24] local.INFO: transaction3  
[2025-07-09 20:02:24] local.INFO: transaction4  
[2025-07-09 20:02:24] local.INFO: transaction4  
[2025-07-09 20:02:24] local.INFO: transaction5  
[2025-07-09 20:02:24] local.INFO: transaction6  
[2025-07-09 20:02:24] local.INFO: transaction7  
[2025-07-09 20:02:24] local.DEBUG: array (
  'AMT' => 155.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '128.099',
)  
[2025-07-09 20:02:24] local.INFO: transaction8  
[2025-07-09 20:02:25] local.INFO: transaction9  
[2025-07-09 20:02:25] local.INFO: 155  
[2025-07-09 20:02:25] local.INFO: transaction10  
[2025-07-09 20:02:25] local.INFO: 128.099  
[2025-07-09 20:02:25] local.INFO: 155.00  
[2025-07-09 20:02:25] local.INFO: transaction11  
[2025-07-09 20:02:25] local.INFO: 121  
[2025-07-09 20:02:25] local.INFO: topup1155.00  
[2025-07-09 20:02:25] local.INFO: topup21.210  
[2025-07-09 20:02:25] local.INFO: topup3155.00  
[2025-07-09 20:02:25] local.INFO: topup4155.00  
[2025-07-09 20:02:25] local.INFO: topup5155  
[2025-07-09 20:02:25] local.INFO: topup60  
[2025-07-09 20:02:25] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 155.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-09 20:02:25',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7339837,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-09 20:02:25',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '155.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '********200225',
  'Quantity' => '128.099',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '155.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '155.00',
  'TotalAmount' => 155.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 155.0,
  'ExCode' => NULL,
  'TransNumber' => '********200225',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#155#0',
  'ResponseTime' => '2025-07-09 20:02:25',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '25',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-09 20:02:25] local.INFO: transaction13  
[2025-07-09 20:02:25] local.INFO: transaction14  
[2025-07-09 20:02:25] local.INFO: transaction19  
[2025-07-09 20:02:25] local.INFO: transaction19#.  
[2025-07-09 20:02:25] local.INFO: transaction19#.  
[2025-07-09 20:02:25] local.INFO: transaction19#  
[2025-07-09 20:02:25] local.INFO: transaction19##  
[2025-07-09 20:02:25] local.INFO: transaction15  
[2025-07-09 20:02:27] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '155.00',
  'remainAmount' => ********,
  'mallrem' => -1384788,
  'transid' => '7339837',
  'ref_id' => 93408961,
)  
[2025-07-09 22:53:23] local.INFO: header  
[2025-07-09 22:53:23] local.INFO: header after fliter  
[2025-07-09 22:53:23] local.INFO: Body  after fliter  
[2025-07-09 22:53:23] local.INFO: array (
  'Amount' => 170.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-09 22:53:23] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-09 22:53:23] local.ERROR: الكمية  
[2025-07-09 22:53:23] local.ERROR: سعر الوحدة  
[2025-07-09 22:53:23] local.ERROR: المبلغ  
[2025-07-09 22:53:23] local.ERROR: 1.210  
[2025-07-09 22:53:23] local.INFO: 140.50  
[2025-07-09 22:53:23] local.ERROR: مبلغ وقدرة  
[2025-07-09 22:53:23] local.INFO: الكمية  
[2025-07-09 22:53:23] local.INFO: سعر الوحدة  
[2025-07-09 22:53:23] local.INFO: المبلغ  
[2025-07-09 22:53:23] local.INFO: مبلغ وقدرة  
[2025-07-09 22:53:23] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-09 22:53:23] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '140.50',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '170.00',
  ),
)  
[2025-07-09 22:53:23] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '140.50',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '170.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-09 22:53:24] local.INFO: header  
[2025-07-09 22:53:24] local.CRITICAL: ****************************1  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 3  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 3  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 3  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 3  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 3  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 3  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 3  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 3  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 3  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 1  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 1  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 1  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 1  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 1  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 10013  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 10013  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 10013  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 10013  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 1  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 3  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 3  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 40  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 40  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 40  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 40  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 200  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 200  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 200  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 200  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 200  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 200  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 40  
[2025-07-09 22:53:24] local.ALERT: 2  
[2025-07-09 22:53:24] local.ALERT: 10013  
[2025-07-09 22:53:24] local.CRITICAL: ****************************  
[2025-07-09 22:53:24] local.CRITICAL:   
[2025-07-09 22:53:24] local.CRITICAL: ****************************  
[2025-07-09 22:53:24] local.CRITICAL: ****************************2  
[2025-07-09 22:53:24] local.INFO: checkUser 1  
[2025-07-09 22:53:26] local.INFO: {
  "ClientBalanceResult": "75299.4381"
}  
[2025-07-09 22:53:26] local.INFO: checkUser 2  
[2025-07-09 22:53:26] local.INFO: array (
  'ClientBalanceResult' => '75299.4381',
)  
[2025-07-09 22:53:26] local.INFO: 170  
[2025-07-09 22:53:26] local.ALERT: reach here  
[2025-07-09 22:53:26] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-09 22:53:26] local.ERROR: الكمية  
[2025-07-09 22:53:26] local.ERROR: سعر الوحدة  
[2025-07-09 22:53:26] local.ERROR: المبلغ  
[2025-07-09 22:53:26] local.ERROR: 1.210  
[2025-07-09 22:53:26] local.INFO: 1.210  
[2025-07-09 22:53:26] local.INFO: 140.496  
[2025-07-09 22:53:26] local.ERROR: 170.00  
[2025-07-09 22:53:26] local.INFO: checkUser 3  
[2025-07-09 22:53:26] local.INFO: checkUser 3#  
[2025-07-09 22:53:26] local.INFO: checkUser 4  
[2025-07-09 22:53:26] local.INFO: checkUser 4#  
[2025-07-09 22:53:26] local.INFO: checkUser 5  
[2025-07-09 22:53:26] local.DEBUG: lattttef  
[2025-07-09 22:53:26] local.DEBUG: array (
  'ClientBalanceResult' => '75299.4381',
)  
[2025-07-09 22:53:26] local.INFO: transaction1  
[2025-07-09 22:53:26] local.INFO: transaction2  
[2025-07-09 22:53:26] local.INFO: transaction3  
[2025-07-09 22:53:26] local.INFO: transaction4  
[2025-07-09 22:53:26] local.INFO: transaction4  
[2025-07-09 22:53:26] local.INFO: transaction5  
[2025-07-09 22:53:26] local.INFO: transaction6  
[2025-07-09 22:53:26] local.INFO: transaction7  
[2025-07-09 22:53:26] local.DEBUG: array (
  'AMT' => 170.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '140.496',
)  
[2025-07-09 22:53:26] local.INFO: transaction8  
[2025-07-09 22:53:26] local.INFO: transaction9  
[2025-07-09 22:53:26] local.INFO: 170  
[2025-07-09 22:53:26] local.INFO: transaction10  
[2025-07-09 22:53:26] local.INFO: 140.496  
[2025-07-09 22:53:26] local.INFO: 170.00  
[2025-07-09 22:53:26] local.INFO: transaction11  
[2025-07-09 22:53:26] local.INFO: 121  
[2025-07-09 22:53:26] local.INFO: topup1170.00  
[2025-07-09 22:53:26] local.INFO: topup21.210  
[2025-07-09 22:53:26] local.INFO: topup3170.00  
[2025-07-09 22:53:26] local.INFO: topup4170.00  
[2025-07-09 22:53:26] local.INFO: topup5170  
[2025-07-09 22:53:26] local.INFO: topup60  
[2025-07-09 22:53:26] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 170.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-09 22:53:26',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7339934,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-09 22:53:26',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '170.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '********225326',
  'Quantity' => '140.496',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '170.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '170.00',
  'TotalAmount' => 170.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 170.0,
  'ExCode' => NULL,
  'TransNumber' => '********225326',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#170#0',
  'ResponseTime' => '2025-07-09 22:53:26',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '26',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-09 22:53:26] local.INFO: transaction13  
[2025-07-09 22:53:26] local.INFO: transaction14  
[2025-07-09 22:53:26] local.INFO: transaction19  
[2025-07-09 22:53:26] local.INFO: transaction19#.  
[2025-07-09 22:53:26] local.INFO: transaction19#.  
[2025-07-09 22:53:26] local.INFO: transaction19#  
[2025-07-09 22:53:26] local.INFO: transaction19##  
[2025-07-09 22:53:26] local.INFO: transaction15  
[2025-07-09 22:53:29] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '170.00',
  'remainAmount' => ********,
  'mallrem' => -1394388,
  'transid' => '7339934',
  'ref_id' => 93441262,
)  
