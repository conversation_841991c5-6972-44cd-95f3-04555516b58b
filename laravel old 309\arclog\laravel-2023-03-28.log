[2023-03-28 01:36:17] local.INFO: header  
[2023-03-28 01:36:17] local.INFO: header after fliter  
[2023-03-28 01:36:17] local.INFO: Body  after fliter  
[2023-03-28 01:36:17] local.INFO: array (
)  
[2023-03-28 01:36:17] local.INFO: transaction14  
[2023-03-28 01:36:17] local.INFO: first inquery phone = 106488408  
[2023-03-28 01:36:20] local.DEBUG: response querySubBalance  
[2023-03-28 01:36:20] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#1.93 MB#25-04-2023#0#0##15000.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 01:36:20] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '1.93 MB',
  4 => '25-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '15000.00',
  9 => '4G 25',
)  
[2023-03-28 01:36:20] local.DEBUG: print  before faction by provider price  
[2023-03-28 01:36:20] local.DEBUG: print  after faction by provider price  
[2023-03-28 01:36:20] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-28 01:36:20] local.DEBUG: print1  
[2023-03-28 01:36:20] local.DEBUG: print  2  
[2023-03-28 02:02:38] local.INFO: header  
[2023-03-28 02:02:38] local.INFO: header after fliter  
[2023-03-28 02:02:38] local.INFO: Body  after fliter  
[2023-03-28 02:02:38] local.INFO: array (
)  
[2023-03-28 02:02:38] local.INFO: transaction14  
[2023-03-28 02:02:38] local.INFO: first inquery phone = 103322169  
[2023-03-28 02:02:41] local.DEBUG: response querySubBalance  
[2023-03-28 02:02:41] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#70.43 GB#20-04-2023#0#0##7500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 02:02:41] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '70.43 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7500.00',
  9 => '4G 250',
)  
[2023-03-28 02:02:41] local.DEBUG: print  before faction by provider price  
[2023-03-28 02:02:41] local.DEBUG: print  after faction by provider price  
[2023-03-28 02:02:41] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 02:02:41] local.DEBUG: print1  
[2023-03-28 02:02:41] local.DEBUG: print  2  
[2023-03-28 02:28:24] local.INFO: header  
[2023-03-28 02:28:24] local.INFO: header after fliter  
[2023-03-28 02:28:24] local.INFO: Body  after fliter  
[2023-03-28 02:28:24] local.INFO: array (
)  
[2023-03-28 02:28:24] local.INFO: transaction14  
[2023-03-28 02:28:24] local.INFO: first inquery phone = 103322059  
[2023-03-28 02:28:27] local.DEBUG: response querySubBalance  
[2023-03-28 02:28:27] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#47.21 GB#20-04-2023#0#0##7500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 02:28:27] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '47.21 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7500.00',
  9 => '4G 250',
)  
[2023-03-28 02:28:27] local.DEBUG: print  before faction by provider price  
[2023-03-28 02:28:27] local.DEBUG: print  after faction by provider price  
[2023-03-28 02:28:27] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 02:28:27] local.DEBUG: print1  
[2023-03-28 02:28:27] local.DEBUG: print  2  
[2023-03-28 02:29:29] local.INFO: header  
[2023-03-28 02:29:29] local.INFO: header after fliter  
[2023-03-28 02:29:29] local.INFO: Body  after fliter  
[2023-03-28 02:29:29] local.INFO: array (
)  
[2023-03-28 02:29:29] local.INFO: transaction14  
[2023-03-28 02:29:29] local.INFO: first inquery phone = 101034508  
[2023-03-28 02:29:32] local.DEBUG: response querySubBalance  
[2023-03-28 02:29:32] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#22.80 GB#11-04-2023#0#0##1500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 02:29:32] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '22.80 GB',
  4 => '11-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1500.00',
  9 => '4G 25',
)  
[2023-03-28 02:29:32] local.DEBUG: print  before faction by provider price  
[2023-03-28 02:29:32] local.DEBUG: print  after faction by provider price  
[2023-03-28 02:29:32] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-28 02:29:32] local.DEBUG: print1  
[2023-03-28 02:29:32] local.DEBUG: print  2  
[2023-03-28 03:06:22] local.INFO: header  
[2023-03-28 03:06:22] local.INFO: header after fliter  
[2023-03-28 03:06:22] local.INFO: Body  after fliter  
[2023-03-28 03:06:22] local.INFO: array (
)  
[2023-03-28 03:06:22] local.INFO: transaction14  
[2023-03-28 03:06:22] local.INFO: first inquery phone = 103335565  
[2023-03-28 03:06:24] local.DEBUG: response querySubBalance  
[2023-03-28 03:06:24] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#8.50 GB#02-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 03:06:24] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '8.50 GB',
  4 => '02-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-28 03:06:24] local.DEBUG: print  before faction by provider price  
[2023-03-28 03:06:24] local.DEBUG: print  after faction by provider price  
[2023-03-28 03:06:24] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-28 03:06:24] local.DEBUG: print1  
[2023-03-28 03:06:24] local.DEBUG: print  2  
[2023-03-28 03:06:53] local.INFO: header  
[2023-03-28 03:06:53] local.INFO: header after fliter  
[2023-03-28 03:06:53] local.INFO: Body  after fliter  
[2023-03-28 03:06:53] local.INFO: array (
)  
[2023-03-28 03:06:53] local.INFO: transaction14  
[2023-03-28 03:06:53] local.INFO: first inquery phone = 103335565  
[2023-03-28 03:06:56] local.DEBUG: response querySubBalance  
[2023-03-28 03:06:56] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#8.50 GB#02-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 03:06:56] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '8.50 GB',
  4 => '02-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-28 03:06:56] local.DEBUG: print  before faction by provider price  
[2023-03-28 03:06:56] local.DEBUG: print  after faction by provider price  
[2023-03-28 03:06:56] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-28 03:06:56] local.DEBUG: print1  
[2023-03-28 03:06:56] local.DEBUG: print  2  
[2023-03-28 03:07:00] local.INFO: header  
[2023-03-28 03:07:00] local.INFO: header after fliter  
[2023-03-28 03:07:00] local.INFO: Body  after fliter  
[2023-03-28 03:07:00] local.INFO: array (
)  
[2023-03-28 03:07:00] local.INFO: transaction14  
[2023-03-28 03:07:00] local.INFO: first inquery phone = 103335565  
[2023-03-28 03:07:03] local.DEBUG: response querySubBalance  
[2023-03-28 03:07:03] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#8.50 GB#02-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 03:07:03] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '8.50 GB',
  4 => '02-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-28 03:07:03] local.DEBUG: print  before faction by provider price  
[2023-03-28 03:07:03] local.DEBUG: print  after faction by provider price  
[2023-03-28 03:07:03] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-28 03:07:03] local.DEBUG: print1  
[2023-03-28 03:07:03] local.DEBUG: print  2  
[2023-03-28 03:13:43] local.INFO: header  
[2023-03-28 03:13:43] local.INFO: header after fliter  
[2023-03-28 03:13:43] local.INFO: Body  after fliter  
[2023-03-28 03:13:43] local.INFO: array (
)  
[2023-03-28 03:13:43] local.INFO: transaction14  
[2023-03-28 03:13:43] local.INFO: first inquery phone = 101034508  
[2023-03-28 03:13:46] local.DEBUG: response querySubBalance  
[2023-03-28 03:13:46] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#21.53 GB#11-04-2023#0#0##1500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 03:13:46] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '21.53 GB',
  4 => '11-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1500.00',
  9 => '4G 25',
)  
[2023-03-28 03:13:46] local.DEBUG: print  before faction by provider price  
[2023-03-28 03:13:46] local.DEBUG: print  after faction by provider price  
[2023-03-28 03:13:46] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-28 03:13:46] local.DEBUG: print1  
[2023-03-28 03:13:46] local.DEBUG: print  2  
[2023-03-28 06:20:59] local.INFO: header  
[2023-03-28 06:20:59] local.INFO: header after fliter  
[2023-03-28 06:20:59] local.INFO: Body  after fliter  
[2023-03-28 06:20:59] local.INFO: array (
)  
[2023-03-28 06:20:59] local.INFO: transaction14  
[2023-03-28 06:20:59] local.INFO: first inquery phone = 101034508  
[2023-03-28 06:21:02] local.DEBUG: response querySubBalance  
[2023-03-28 06:21:02] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#19.34 GB#11-04-2023#0#0##1500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 06:21:02] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '19.34 GB',
  4 => '11-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1500.00',
  9 => '4G 25',
)  
[2023-03-28 06:21:02] local.DEBUG: print  before faction by provider price  
[2023-03-28 06:21:02] local.DEBUG: print  after faction by provider price  
[2023-03-28 06:21:02] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-28 06:21:02] local.DEBUG: print1  
[2023-03-28 06:21:02] local.DEBUG: print  2  
[2023-03-28 07:51:29] local.INFO: header  
[2023-03-28 07:51:29] local.INFO: header after fliter  
[2023-03-28 07:51:29] local.INFO: Body  after fliter  
[2023-03-28 07:51:29] local.INFO: array (
)  
[2023-03-28 07:51:29] local.INFO: transaction14  
[2023-03-28 07:51:29] local.INFO: first inquery phone = 103330532  
[2023-03-28 07:51:33] local.DEBUG: response querySubBalance  
[2023-03-28 07:51:33] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#21.95 GB#23-04-2023#0#0##13765.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 07:51:33] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '21.95 GB',
  4 => '23-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '13765.00',
  9 => '4G 60',
)  
[2023-03-28 07:51:33] local.DEBUG: print  before faction by provider price  
[2023-03-28 07:51:33] local.DEBUG: print  after faction by provider price  
[2023-03-28 07:51:33] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-28 07:51:33] local.DEBUG: print1  
[2023-03-28 07:51:33] local.DEBUG: print  2  
[2023-03-28 07:51:52] local.INFO: header  
[2023-03-28 07:51:52] local.CRITICAL: ****************************1  
[2023-03-28 07:51:52] local.ALERT: reach here  
[2023-03-28 07:51:52] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-28 07:51:52] local.ERROR: المبلغ  
[2023-03-28 07:51:52] local.ERROR: 8,000.00  
[2023-03-28 07:51:52] local.ERROR: مبلغ وقدرة  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 3  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 3  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 3  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 3  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 3  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 3  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 3  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 3  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 3  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 2  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 2  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 2  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 2  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 2  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 2  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 2  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 1  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 1  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 1  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 1  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 1  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 2  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 10013  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 10013  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 10013  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 10013  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 1  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 3  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 3  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 40  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 40  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 40  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 40  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.WARNING: 1  
[2023-03-28 07:51:52] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 200  
[2023-03-28 07:51:52] local.ALERT: 40  
[2023-03-28 07:51:52] local.CRITICAL: ****************************2  
[2023-03-28 07:51:52] local.CRITICAL: ****************************  
[2023-03-28 07:51:52] local.CRITICAL:   
[2023-03-28 07:51:52] local.CRITICAL: ****************************  
[2023-03-28 07:51:53] local.INFO: {
  "ClientBalanceResult": "50416.4538"
}  
[2023-03-28 07:51:53] local.INFO: array (
  'ClientBalanceResult' => '50416.4538',
)  
[2023-03-28 07:51:53] local.DEBUG: lattttef  
[2023-03-28 07:51:53] local.DEBUG: array (
  'ClientBalanceResult' => '50416.4538',
)  
[2023-03-28 07:51:53] local.INFO: transaction14  
[2023-03-28 07:51:53] local.INFO: first inquery phone = 103330532  
[2023-03-28 07:51:56] local.DEBUG: response querySubBalance  
[2023-03-28 07:51:56] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#21.95 GB#23-04-2023#0#0##13765.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 07:51:56] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '21.95 GB',
  4 => '23-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '13765.00',
  9 => '4G 60',
)  
[2023-03-28 07:51:56] local.DEBUG: print  before faction by provider price  
[2023-03-28 07:51:56] local.DEBUG: print  after faction by provider price  
[2023-03-28 07:51:56] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-28 07:51:56] local.DEBUG: print1  
[2023-03-28 07:51:56] local.DEBUG: print  2  
[2023-03-28 07:51:56] local.INFO: transaction1  
[2023-03-28 07:51:56] local.INFO: transaction2  
[2023-03-28 07:51:56] local.INFO: transaction3  
[2023-03-28 07:51:56] local.INFO: transaction4  
[2023-03-28 07:51:56] local.INFO: transaction4  
[2023-03-28 07:51:56] local.INFO: transaction7  
[2023-03-28 07:51:56] local.DEBUG: array (
  'AMT' => 8000.0,
  'CType' => 0,
  'FID' => 87,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103330532',
  'State' => 0,
  'lateflog' => '327014',
)  
[2023-03-28 07:51:56] local.INFO: transaction8  
[2023-03-28 07:51:57] local.INFO: transaction9  
[2023-03-28 07:51:57] local.INFO: transaction10  
[2023-03-28 07:51:57] local.INFO: transaction11  
[2023-03-28 07:51:57] local.INFO: 12  
[2023-03-28 07:51:57] local.INFO: transaction13  
[2023-03-28 07:51:57] local.INFO: transaction14  
[2023-03-28 07:51:57] local.INFO: transaction19  
[2023-03-28 07:51:57] local.INFO: transaction15  
[2023-03-28 07:51:57] local.INFO: transaction16  
[2023-03-28 07:51:57] local.INFO: 98#103330532#8000.00#0  
[2023-03-28 07:52:07] local.INFO: transaction18  
[2023-03-28 07:52:07] local.INFO: array (
  0 => 'OK',
  1 => '4,884,523.83',
  2 => 'NONE',
  3 => '61417382',
  4 => '8,000.00',
)  
[2023-03-28 08:22:47] local.INFO: header  
[2023-03-28 08:22:47] local.INFO: header after fliter  
[2023-03-28 08:22:47] local.INFO: Body  after fliter  
[2023-03-28 08:22:47] local.INFO: array (
)  
[2023-03-28 08:22:47] local.INFO: transaction14  
[2023-03-28 08:22:47] local.INFO: first inquery phone = 103322059  
[2023-03-28 08:22:51] local.DEBUG: response querySubBalance  
[2023-03-28 08:22:51] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#39.34 GB#20-04-2023#0#0##7500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 08:22:51] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '39.34 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7500.00',
  9 => '4G 250',
)  
[2023-03-28 08:22:51] local.DEBUG: print  before faction by provider price  
[2023-03-28 08:22:51] local.DEBUG: print  after faction by provider price  
[2023-03-28 08:22:51] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 08:22:51] local.DEBUG: print1  
[2023-03-28 08:22:51] local.DEBUG: print  2  
[2023-03-28 08:23:00] local.INFO: header  
[2023-03-28 08:23:00] local.CRITICAL: ****************************1  
[2023-03-28 08:23:00] local.ALERT: reach here  
[2023-03-28 08:23:00] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '26000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ستة وعشرون ألف  ر.ي.',
  ),
)  
[2023-03-28 08:23:00] local.ERROR: المبلغ  
[2023-03-28 08:23:00] local.ERROR: 26,000.00  
[2023-03-28 08:23:00] local.ERROR: مبلغ وقدرة  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 3  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 3  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 3  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 3  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 3  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 3  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 3  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 3  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 3  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 2  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 2  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 2  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 2  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 2  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 2  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 2  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 1  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 1  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 1  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 1  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 1  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 2  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 10013  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 10013  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 10013  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 10013  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 1  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 3  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 3  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 40  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 40  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 40  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 40  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.WARNING: 1  
[2023-03-28 08:23:00] local.WARNING: array (
  'ID' => 89,
  'Name' => 'فئة 250 جيجا 26000 ريال',
  'ServiceID' => 200,
  'Price' => 26000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 26000.0,
)  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 200  
[2023-03-28 08:23:00] local.ALERT: 40  
[2023-03-28 08:23:00] local.CRITICAL: ****************************2  
[2023-03-28 08:23:00] local.CRITICAL: ****************************  
[2023-03-28 08:23:00] local.CRITICAL:   
[2023-03-28 08:23:00] local.CRITICAL: ****************************  
[2023-03-28 08:23:00] local.INFO: {
  "ClientBalanceResult": "26727.3900"
}  
[2023-03-28 08:23:00] local.INFO: array (
  'ClientBalanceResult' => '26727.3900',
)  
[2023-03-28 08:23:00] local.DEBUG: lattttef  
[2023-03-28 08:23:00] local.DEBUG: array (
  'ClientBalanceResult' => '26727.3900',
)  
[2023-03-28 08:23:00] local.INFO: transaction14  
[2023-03-28 08:23:00] local.INFO: first inquery phone = 103322059  
[2023-03-28 08:23:03] local.DEBUG: response querySubBalance  
[2023-03-28 08:23:03] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#39.34 GB#20-04-2023#0#0##7500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 08:23:03] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '39.34 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7500.00',
  9 => '4G 250',
)  
[2023-03-28 08:23:03] local.DEBUG: print  before faction by provider price  
[2023-03-28 08:23:03] local.DEBUG: print  after faction by provider price  
[2023-03-28 08:23:03] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 08:23:03] local.DEBUG: print1  
[2023-03-28 08:23:03] local.DEBUG: print  2  
[2023-03-28 08:23:03] local.INFO: transaction1  
[2023-03-28 08:23:03] local.INFO: transaction2  
[2023-03-28 08:23:03] local.INFO: transaction3  
[2023-03-28 08:23:03] local.INFO: transaction4  
[2023-03-28 08:23:03] local.INFO: transaction4  
[2023-03-28 08:23:03] local.INFO: transaction7  
[2023-03-28 08:23:03] local.DEBUG: array (
  'AMT' => 26000.0,
  'CType' => 0,
  'FID' => 89,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103322059',
  'State' => 0,
  'lateflog' => '530377',
)  
[2023-03-28 08:23:03] local.INFO: transaction8  
[2023-03-28 08:23:03] local.INFO: transaction9  
[2023-03-28 08:23:03] local.INFO: transaction10  
[2023-03-28 08:23:03] local.INFO: transaction11  
[2023-03-28 08:23:03] local.INFO: 12  
[2023-03-28 08:23:03] local.INFO: transaction13  
[2023-03-28 08:23:03] local.INFO: transaction14  
[2023-03-28 08:23:03] local.INFO: transaction19  
[2023-03-28 08:23:03] local.INFO: transaction15  
[2023-03-28 08:23:03] local.INFO: transaction16  
[2023-03-28 08:23:03] local.INFO: 98#103322059#26000.00#0  
[2023-03-28 08:23:14] local.INFO: transaction18  
[2023-03-28 08:23:14] local.INFO: array (
  0 => 'OK',
  1 => '4,858,057.83',
  2 => 'NONE',
  3 => '61417552',
  4 => '26,000.00',
)  
[2023-03-28 08:23:19] local.INFO: header  
[2023-03-28 08:23:19] local.INFO: header after fliter  
[2023-03-28 08:23:19] local.INFO: Body  after fliter  
[2023-03-28 08:23:19] local.INFO: array (
)  
[2023-03-28 08:23:19] local.INFO: transaction14  
[2023-03-28 08:23:19] local.INFO: first inquery phone = 103322059  
[2023-03-28 08:23:22] local.DEBUG: response querySubBalance  
[2023-03-28 08:23:22] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#39.34 GB#20-04-2023#0#0##7500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 08:23:22] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '39.34 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7500.00',
  9 => '4G 250',
)  
[2023-03-28 08:23:22] local.DEBUG: print  before faction by provider price  
[2023-03-28 08:23:22] local.DEBUG: print  after faction by provider price  
[2023-03-28 08:23:22] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 08:23:22] local.DEBUG: print1  
[2023-03-28 08:23:22] local.DEBUG: print  2  
[2023-03-28 08:23:27] local.INFO: header  
[2023-03-28 08:23:28] local.INFO: header after fliter  
[2023-03-28 08:23:28] local.INFO: Body  after fliter  
[2023-03-28 08:23:28] local.INFO: array (
)  
[2023-03-28 08:23:28] local.INFO: transaction14  
[2023-03-28 08:23:28] local.INFO: first inquery phone = 103322059  
[2023-03-28 08:23:31] local.DEBUG: response querySubBalance  
[2023-03-28 08:23:31] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#289.31 GB#20-04-2023#0#0##8500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 08:23:31] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '289.31 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 250',
)  
[2023-03-28 08:23:31] local.DEBUG: print  before faction by provider price  
[2023-03-28 08:23:31] local.DEBUG: print  after faction by provider price  
[2023-03-28 08:23:31] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 08:23:31] local.DEBUG: print1  
[2023-03-28 08:23:31] local.DEBUG: print  2  
[2023-03-28 08:23:40] local.INFO: header  
[2023-03-28 08:23:40] local.INFO: header after fliter  
[2023-03-28 08:23:40] local.INFO: Body  after fliter  
[2023-03-28 08:23:40] local.INFO: array (
)  
[2023-03-28 08:23:40] local.INFO: transaction14  
[2023-03-28 08:23:40] local.INFO: first inquery phone = 103322169  
[2023-03-28 08:23:43] local.DEBUG: response querySubBalance  
[2023-03-28 08:23:43] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#62.13 GB#20-04-2023#0#0##7500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 08:23:43] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '62.13 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7500.00',
  9 => '4G 250',
)  
[2023-03-28 08:23:43] local.DEBUG: print  before faction by provider price  
[2023-03-28 08:23:43] local.DEBUG: print  after faction by provider price  
[2023-03-28 08:23:43] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 08:23:43] local.DEBUG: print1  
[2023-03-28 08:23:43] local.DEBUG: print  2  
[2023-03-28 08:23:54] local.INFO: header  
[2023-03-28 08:23:54] local.CRITICAL: ****************************1  
[2023-03-28 08:23:54] local.ALERT: reach here  
[2023-03-28 08:23:54] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '26000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ستة وعشرون ألف  ر.ي.',
  ),
)  
[2023-03-28 08:23:54] local.ERROR: المبلغ  
[2023-03-28 08:23:54] local.ERROR: 26,000.00  
[2023-03-28 08:23:54] local.ERROR: مبلغ وقدرة  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 3  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 3  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 3  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 3  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 3  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 3  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 3  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 3  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 3  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 2  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 2  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 2  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 2  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 2  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 2  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 2  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 1  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 1  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 1  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 1  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 1  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 2  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 10013  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 10013  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 10013  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 10013  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 1  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 3  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 3  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 40  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 40  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 40  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 40  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.WARNING: 1  
[2023-03-28 08:23:54] local.WARNING: array (
  'ID' => 89,
  'Name' => 'فئة 250 جيجا 26000 ريال',
  'ServiceID' => 200,
  'Price' => 26000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 26000.0,
)  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 200  
[2023-03-28 08:23:54] local.ALERT: 40  
[2023-03-28 08:23:54] local.CRITICAL: ****************************2  
[2023-03-28 08:23:54] local.CRITICAL: ****************************  
[2023-03-28 08:23:54] local.CRITICAL:   
[2023-03-28 08:23:54] local.CRITICAL: ****************************  
[2023-03-28 08:23:54] local.INFO: {
  "ClientBalanceResult": "727.3900"
}  
[2023-03-28 08:23:54] local.INFO: array (
  'ClientBalanceResult' => '727.3900',
)  
[2023-03-28 08:23:54] local.INFO: price less than Balance  
[2023-03-28 08:24:15] local.INFO: header  
[2023-03-28 08:24:15] local.INFO: header after fliter  
[2023-03-28 08:24:15] local.INFO: Body  after fliter  
[2023-03-28 08:24:15] local.INFO: array (
)  
[2023-03-28 08:24:15] local.INFO: transaction14  
[2023-03-28 08:24:15] local.INFO: first inquery phone = 103322169  
[2023-03-28 08:24:18] local.DEBUG: response querySubBalance  
[2023-03-28 08:24:18] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#62.13 GB#20-04-2023#0#0##7500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 08:24:18] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '62.13 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7500.00',
  9 => '4G 250',
)  
[2023-03-28 08:24:18] local.DEBUG: print  before faction by provider price  
[2023-03-28 08:24:18] local.DEBUG: print  after faction by provider price  
[2023-03-28 08:24:18] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 08:24:18] local.DEBUG: print1  
[2023-03-28 08:24:18] local.DEBUG: print  2  
[2023-03-28 08:24:24] local.INFO: header  
[2023-03-28 08:24:24] local.CRITICAL: ****************************1  
[2023-03-28 08:24:24] local.ALERT: reach here  
[2023-03-28 08:24:24] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '26000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ستة وعشرون ألف  ر.ي.',
  ),
)  
[2023-03-28 08:24:24] local.ERROR: المبلغ  
[2023-03-28 08:24:24] local.ERROR: 26,000.00  
[2023-03-28 08:24:24] local.ERROR: مبلغ وقدرة  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 3  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 3  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 3  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 3  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 3  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 3  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 3  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 3  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 3  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 2  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 2  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 2  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 2  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 2  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 2  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 2  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 1  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 1  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 1  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 1  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 1  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 2  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 10013  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 10013  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 10013  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 10013  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 1  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 3  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 3  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 40  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 40  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 40  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 40  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.WARNING: 1  
[2023-03-28 08:24:24] local.WARNING: array (
  'ID' => 89,
  'Name' => 'فئة 250 جيجا 26000 ريال',
  'ServiceID' => 200,
  'Price' => 26000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 26000.0,
)  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 200  
[2023-03-28 08:24:24] local.ALERT: 40  
[2023-03-28 08:24:24] local.CRITICAL: ****************************2  
[2023-03-28 08:24:24] local.CRITICAL: ****************************  
[2023-03-28 08:24:24] local.CRITICAL:   
[2023-03-28 08:24:24] local.CRITICAL: ****************************  
[2023-03-28 08:24:24] local.INFO: {
  "ClientBalanceResult": "727.3900"
}  
[2023-03-28 08:24:24] local.INFO: array (
  'ClientBalanceResult' => '727.3900',
)  
[2023-03-28 08:24:24] local.INFO: price less than Balance  
[2023-03-28 08:24:30] local.INFO: header  
[2023-03-28 08:24:30] local.CRITICAL: ****************************1  
[2023-03-28 08:24:30] local.ALERT: reach here  
[2023-03-28 08:24:30] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '26000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ستة وعشرون ألف  ر.ي.',
  ),
)  
[2023-03-28 08:24:30] local.ERROR: المبلغ  
[2023-03-28 08:24:30] local.ERROR: 26,000.00  
[2023-03-28 08:24:30] local.ERROR: مبلغ وقدرة  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 3  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 3  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 3  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 3  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 3  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 3  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 3  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 3  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 3  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 2  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 2  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 2  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 2  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 2  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 2  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 2  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 1  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 1  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 1  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 1  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 1  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 2  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 10013  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 10013  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 10013  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 10013  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 1  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 3  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 3  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 40  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 40  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 40  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 40  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.WARNING: 1  
[2023-03-28 08:24:30] local.WARNING: array (
  'ID' => 89,
  'Name' => 'فئة 250 جيجا 26000 ريال',
  'ServiceID' => 200,
  'Price' => 26000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 26000.0,
)  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 200  
[2023-03-28 08:24:30] local.ALERT: 40  
[2023-03-28 08:24:30] local.CRITICAL: ****************************2  
[2023-03-28 08:24:30] local.CRITICAL: ****************************  
[2023-03-28 08:24:30] local.CRITICAL:   
[2023-03-28 08:24:30] local.CRITICAL: ****************************  
[2023-03-28 08:24:31] local.INFO: {
  "ClientBalanceResult": "727.3900"
}  
[2023-03-28 08:24:31] local.INFO: array (
  'ClientBalanceResult' => '727.3900',
)  
[2023-03-28 08:24:31] local.INFO: price less than Balance  
[2023-03-28 11:59:13] local.INFO: header  
[2023-03-28 11:59:13] local.INFO: header after fliter  
[2023-03-28 11:59:13] local.INFO: Body  after fliter  
[2023-03-28 11:59:13] local.INFO: array (
)  
[2023-03-28 11:59:13] local.INFO: transaction14  
[2023-03-28 11:59:13] local.INFO: first inquery phone = 101034508  
[2023-03-28 11:59:16] local.DEBUG: response querySubBalance  
[2023-03-28 11:59:16] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#18.96 GB#11-04-2023#0#0##1500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 11:59:16] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '18.96 GB',
  4 => '11-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1500.00',
  9 => '4G 25',
)  
[2023-03-28 11:59:16] local.DEBUG: print  before faction by provider price  
[2023-03-28 11:59:16] local.DEBUG: print  after faction by provider price  
[2023-03-28 11:59:16] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-28 11:59:16] local.DEBUG: print1  
[2023-03-28 11:59:16] local.DEBUG: print  2  
[2023-03-28 13:38:22] local.INFO: header  
[2023-03-28 13:38:22] local.CRITICAL: ****************************1  
[2023-03-28 13:38:22] local.ALERT: reach here  
[2023-03-28 13:38:22] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-28 13:38:22] local.ERROR: المبلغ  
[2023-03-28 13:38:22] local.ERROR: 8,000.00  
[2023-03-28 13:38:22] local.ERROR: مبلغ وقدرة  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 3  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 3  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 3  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 3  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 3  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 3  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 3  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 3  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 3  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 2  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 2  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 2  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 2  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 2  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 2  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 2  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 1  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 1  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 1  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 1  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 1  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 2  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 10013  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 10013  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 10013  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 10013  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 1  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 3  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 3  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 40  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 40  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 40  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 40  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.WARNING: 1  
[2023-03-28 13:38:22] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 200  
[2023-03-28 13:38:22] local.ALERT: 40  
[2023-03-28 13:38:22] local.CRITICAL: ****************************2  
[2023-03-28 13:38:22] local.CRITICAL: ****************************  
[2023-03-28 13:38:22] local.CRITICAL:   
[2023-03-28 13:38:22] local.CRITICAL: ****************************  
[2023-03-28 13:38:23] local.INFO: {
  "ClientBalanceResult": "-66970.2250"
}  
[2023-03-28 13:38:23] local.INFO: array (
  'ClientBalanceResult' => '-66970.2250',
)  
[2023-03-28 13:38:23] local.INFO: price less than Balance  
[2023-03-28 19:56:08] local.INFO: header  
[2023-03-28 19:56:08] local.INFO: header after fliter  
[2023-03-28 19:56:08] local.INFO: Body  after fliter  
[2023-03-28 19:56:08] local.INFO: array (
)  
[2023-03-28 19:56:08] local.INFO: transaction14  
[2023-03-28 19:56:08] local.INFO: first inquery phone = 106323109  
[2023-03-28 19:56:12] local.DEBUG: response querySubBalance  
[2023-03-28 19:56:12] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#39.50 GB#15-04-2023#0#0##2500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 19:56:12] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '39.50 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '2500.00',
  9 => '4G 60',
)  
[2023-03-28 19:56:12] local.DEBUG: print  before faction by provider price  
[2023-03-28 19:56:12] local.DEBUG: print  after faction by provider price  
[2023-03-28 19:56:12] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-28 19:56:12] local.DEBUG: print1  
[2023-03-28 19:56:12] local.DEBUG: print  2  
[2023-03-28 20:48:10] local.INFO: header  
[2023-03-28 20:48:10] local.INFO: header after fliter  
[2023-03-28 20:48:10] local.INFO: Body  after fliter  
[2023-03-28 20:48:10] local.INFO: array (
)  
[2023-03-28 20:48:10] local.INFO: transaction14  
[2023-03-28 20:48:10] local.INFO: first inquery phone = 103322169  
[2023-03-28 20:48:13] local.DEBUG: response querySubBalance  
[2023-03-28 20:48:13] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#50.85 GB#20-04-2023#0#0##7500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 20:48:13] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '50.85 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7500.00',
  9 => '4G 250',
)  
[2023-03-28 20:48:13] local.DEBUG: print  before faction by provider price  
[2023-03-28 20:48:13] local.DEBUG: print  after faction by provider price  
[2023-03-28 20:48:13] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 20:48:13] local.DEBUG: print1  
[2023-03-28 20:48:13] local.DEBUG: print  2  
[2023-03-28 20:49:16] local.INFO: header  
[2023-03-28 20:49:16] local.INFO: header after fliter  
[2023-03-28 20:49:16] local.INFO: Body  after fliter  
[2023-03-28 20:49:16] local.INFO: array (
)  
[2023-03-28 20:49:16] local.INFO: transaction14  
[2023-03-28 20:49:16] local.INFO: first inquery phone = 103322169  
[2023-03-28 20:49:19] local.DEBUG: response querySubBalance  
[2023-03-28 20:49:19] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#50.81 GB#20-04-2023#0#0##7500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 20:49:19] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '50.81 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7500.00',
  9 => '4G 250',
)  
[2023-03-28 20:49:19] local.DEBUG: print  before faction by provider price  
[2023-03-28 20:49:19] local.DEBUG: print  after faction by provider price  
[2023-03-28 20:49:19] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 20:49:19] local.DEBUG: print1  
[2023-03-28 20:49:19] local.DEBUG: print  2  
[2023-03-28 20:49:58] local.INFO: header  
[2023-03-28 20:49:58] local.CRITICAL: ****************************1  
[2023-03-28 20:49:58] local.ALERT: reach here  
[2023-03-28 20:49:58] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '26000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ستة وعشرون ألف  ر.ي.',
  ),
)  
[2023-03-28 20:49:58] local.ERROR: المبلغ  
[2023-03-28 20:49:58] local.ERROR: 26,000.00  
[2023-03-28 20:49:58] local.ERROR: مبلغ وقدرة  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 3  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 3  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 3  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 3  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 3  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 3  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 3  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 3  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 3  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 2  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 2  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 2  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 2  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 2  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 2  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 2  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 1  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 1  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 1  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 1  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 1  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 2  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 10013  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 10013  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 10013  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 10013  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 1  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 3  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 3  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 40  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 40  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 40  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 40  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.WARNING: 1  
[2023-03-28 20:49:58] local.WARNING: array (
  'ID' => 89,
  'Name' => 'فئة 250 جيجا 26000 ريال',
  'ServiceID' => 200,
  'Price' => 26000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 26000.0,
)  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 200  
[2023-03-28 20:49:58] local.ALERT: 40  
[2023-03-28 20:49:58] local.CRITICAL: ****************************2  
[2023-03-28 20:49:58] local.CRITICAL: ****************************  
[2023-03-28 20:49:58] local.CRITICAL:   
[2023-03-28 20:49:58] local.CRITICAL: ****************************  
[2023-03-28 20:49:58] local.INFO: {
  "ClientBalanceResult": "-1196.5600"
}  
[2023-03-28 20:49:58] local.INFO: array (
  'ClientBalanceResult' => '-1196.5600',
)  
[2023-03-28 20:49:58] local.INFO: price less than Balance  
[2023-03-28 21:02:09] local.INFO: header  
[2023-03-28 21:02:09] local.INFO: header after fliter  
[2023-03-28 21:02:09] local.INFO: Body  after fliter  
[2023-03-28 21:02:09] local.INFO: array (
)  
[2023-03-28 21:02:09] local.INFO: transaction14  
[2023-03-28 21:02:09] local.INFO: first inquery phone = 103322169  
[2023-03-28 21:02:13] local.DEBUG: response querySubBalance  
[2023-03-28 21:02:13] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#50.51 GB#20-04-2023#0#0##7500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 21:02:13] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '50.51 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7500.00',
  9 => '4G 250',
)  
[2023-03-28 21:02:13] local.DEBUG: print  before faction by provider price  
[2023-03-28 21:02:13] local.DEBUG: print  after faction by provider price  
[2023-03-28 21:02:13] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 21:02:13] local.DEBUG: print1  
[2023-03-28 21:02:13] local.DEBUG: print  2  
[2023-03-28 21:02:18] local.INFO: header  
[2023-03-28 21:02:18] local.CRITICAL: ****************************1  
[2023-03-28 21:02:18] local.ALERT: reach here  
[2023-03-28 21:02:18] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '26000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ستة وعشرون ألف  ر.ي.',
  ),
)  
[2023-03-28 21:02:18] local.ERROR: المبلغ  
[2023-03-28 21:02:18] local.ERROR: 26,000.00  
[2023-03-28 21:02:18] local.ERROR: مبلغ وقدرة  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 3  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 3  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 3  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 3  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 3  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 3  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 3  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 3  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 3  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 2  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 2  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 2  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 2  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 2  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 2  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 2  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 1  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 1  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 1  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 1  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 1  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 2  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 10013  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 10013  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 10013  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 10013  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 1  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 3  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 3  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 40  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 40  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 40  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 40  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.WARNING: 1  
[2023-03-28 21:02:18] local.WARNING: array (
  'ID' => 89,
  'Name' => 'فئة 250 جيجا 26000 ريال',
  'ServiceID' => 200,
  'Price' => 26000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 26000.0,
)  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 200  
[2023-03-28 21:02:18] local.ALERT: 40  
[2023-03-28 21:02:18] local.CRITICAL: ****************************2  
[2023-03-28 21:02:18] local.CRITICAL: ****************************  
[2023-03-28 21:02:18] local.CRITICAL:   
[2023-03-28 21:02:18] local.CRITICAL: ****************************  
[2023-03-28 21:02:18] local.INFO: {
  "ClientBalanceResult": "25803.4400"
}  
[2023-03-28 21:02:18] local.INFO: array (
  'ClientBalanceResult' => '25803.4400',
)  
[2023-03-28 21:02:18] local.INFO: price less than Balance  
[2023-03-28 22:18:58] local.INFO: header  
[2023-03-28 22:18:59] local.INFO: header after fliter  
[2023-03-28 22:18:59] local.INFO: Body  after fliter  
[2023-03-28 22:18:59] local.INFO: array (
)  
[2023-03-28 22:18:59] local.INFO: transaction14  
[2023-03-28 22:18:59] local.INFO: first inquery phone = 106400860  
[2023-03-28 22:19:03] local.DEBUG: response querySubBalance  
[2023-03-28 22:19:03] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#10.55 GB#28-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 22:19:03] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '10.55 GB',
  4 => '28-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-28 22:19:03] local.DEBUG: print  before faction by provider price  
[2023-03-28 22:19:03] local.DEBUG: print  after faction by provider price  
[2023-03-28 22:19:03] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-28 22:19:03] local.DEBUG: print1  
[2023-03-28 22:19:03] local.DEBUG: print  2  
[2023-03-28 22:19:37] local.INFO: header  
[2023-03-28 22:19:37] local.INFO: header after fliter  
[2023-03-28 22:19:37] local.INFO: Body  after fliter  
[2023-03-28 22:19:37] local.INFO: array (
)  
[2023-03-28 22:19:37] local.INFO: transaction14  
[2023-03-28 22:19:37] local.INFO: first inquery phone = 103322169  
[2023-03-28 22:19:41] local.DEBUG: response querySubBalance  
[2023-03-28 22:19:41] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#299.56 GB#28-04-2023#0#0##8500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 22:19:41] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '299.56 GB',
  4 => '28-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 250',
)  
[2023-03-28 22:19:41] local.DEBUG: print  before faction by provider price  
[2023-03-28 22:19:41] local.DEBUG: print  after faction by provider price  
[2023-03-28 22:19:41] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 22:19:41] local.DEBUG: print1  
[2023-03-28 22:19:41] local.DEBUG: print  2  
[2023-03-28 22:20:58] local.INFO: header  
[2023-03-28 22:20:58] local.INFO: header after fliter  
[2023-03-28 22:20:58] local.INFO: Body  after fliter  
[2023-03-28 22:20:58] local.INFO: array (
)  
[2023-03-28 22:20:58] local.INFO: transaction14  
[2023-03-28 22:20:58] local.INFO: first inquery phone = 106400598  
[2023-03-28 22:21:02] local.DEBUG: response querySubBalance  
[2023-03-28 22:21:02] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#37.27 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 22:21:02] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '37.27 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-28 22:21:02] local.DEBUG: print  before faction by provider price  
[2023-03-28 22:21:02] local.DEBUG: print  after faction by provider price  
[2023-03-28 22:21:02] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 22:21:02] local.DEBUG: print1  
[2023-03-28 22:21:02] local.DEBUG: print  2  
[2023-03-28 22:23:08] local.INFO: header  
[2023-03-28 22:23:08] local.INFO: header after fliter  
[2023-03-28 22:23:08] local.INFO: Body  after fliter  
[2023-03-28 22:23:08] local.INFO: array (
)  
[2023-03-28 22:23:08] local.INFO: transaction14  
[2023-03-28 22:23:08] local.INFO: first inquery phone = 106400598  
[2023-03-28 22:23:12] local.DEBUG: response querySubBalance  
[2023-03-28 22:23:12] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#37.22 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 22:23:12] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '37.22 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-28 22:23:12] local.DEBUG: print  before faction by provider price  
[2023-03-28 22:23:12] local.DEBUG: print  after faction by provider price  
[2023-03-28 22:23:12] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 22:23:12] local.DEBUG: print1  
[2023-03-28 22:23:12] local.DEBUG: print  2  
[2023-03-28 22:33:48] local.INFO: header  
[2023-03-28 22:33:48] local.INFO: header after fliter  
[2023-03-28 22:33:48] local.INFO: Body  after fliter  
[2023-03-28 22:33:48] local.INFO: array (
)  
[2023-03-28 22:33:48] local.INFO: transaction14  
[2023-03-28 22:33:48] local.INFO: first inquery phone = 103322169  
[2023-03-28 22:33:50] local.DEBUG: response querySubBalance  
[2023-03-28 22:33:50] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#299.03 GB#28-04-2023#0#0##8500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 22:33:50] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '299.03 GB',
  4 => '28-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 250',
)  
[2023-03-28 22:33:50] local.DEBUG: print  before faction by provider price  
[2023-03-28 22:33:50] local.DEBUG: print  after faction by provider price  
[2023-03-28 22:33:50] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 22:33:50] local.DEBUG: print1  
[2023-03-28 22:33:50] local.DEBUG: print  2  
[2023-03-28 22:33:59] local.INFO: header  
[2023-03-28 22:33:59] local.INFO: header after fliter  
[2023-03-28 22:33:59] local.INFO: Body  after fliter  
[2023-03-28 22:33:59] local.INFO: array (
)  
[2023-03-28 22:33:59] local.INFO: transaction14  
[2023-03-28 22:33:59] local.INFO: first inquery phone = 103322059  
[2023-03-28 22:34:03] local.DEBUG: response querySubBalance  
[2023-03-28 22:34:03] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#276.04 GB#28-04-2023#0#0##8500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 22:34:03] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '276.04 GB',
  4 => '28-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 250',
)  
[2023-03-28 22:34:03] local.DEBUG: print  before faction by provider price  
[2023-03-28 22:34:03] local.DEBUG: print  after faction by provider price  
[2023-03-28 22:34:03] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 22:34:03] local.DEBUG: print1  
[2023-03-28 22:34:03] local.DEBUG: print  2  
[2023-03-28 22:52:08] local.INFO: header  
[2023-03-28 22:52:08] local.INFO: header after fliter  
[2023-03-28 22:52:08] local.INFO: Body  after fliter  
[2023-03-28 22:52:08] local.INFO: array (
)  
[2023-03-28 22:52:08] local.INFO: transaction14  
[2023-03-28 22:52:08] local.INFO: first inquery phone = 106400860  
[2023-03-28 22:52:11] local.DEBUG: response querySubBalance  
[2023-03-28 22:52:11] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#9.33 GB#28-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 22:52:11] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '9.33 GB',
  4 => '28-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-28 22:52:11] local.DEBUG: print  before faction by provider price  
[2023-03-28 22:52:11] local.DEBUG: print  after faction by provider price  
[2023-03-28 22:52:11] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-28 22:52:11] local.DEBUG: print1  
[2023-03-28 22:52:11] local.DEBUG: print  2  
[2023-03-28 22:52:19] local.INFO: header  
[2023-03-28 22:52:19] local.INFO: header after fliter  
[2023-03-28 22:52:19] local.INFO: Body  after fliter  
[2023-03-28 22:52:19] local.INFO: array (
)  
[2023-03-28 22:52:19] local.INFO: transaction14  
[2023-03-28 22:52:19] local.INFO: first inquery phone = 106400860  
[2023-03-28 22:52:21] local.DEBUG: response querySubBalance  
[2023-03-28 22:52:21] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#9.33 GB#28-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 22:52:21] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '9.33 GB',
  4 => '28-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-28 22:52:21] local.DEBUG: print  before faction by provider price  
[2023-03-28 22:52:21] local.DEBUG: print  after faction by provider price  
[2023-03-28 22:52:21] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-28 22:52:21] local.DEBUG: print1  
[2023-03-28 22:52:21] local.DEBUG: print  2  
[2023-03-28 22:52:50] local.INFO: header  
[2023-03-28 22:52:50] local.INFO: header after fliter  
[2023-03-28 22:52:50] local.INFO: Body  after fliter  
[2023-03-28 22:52:50] local.INFO: array (
)  
[2023-03-28 22:52:50] local.INFO: transaction14  
[2023-03-28 22:52:50] local.INFO: first inquery phone = 106400598  
[2023-03-28 22:52:53] local.DEBUG: response querySubBalance  
[2023-03-28 22:52:53] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#36.64 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 22:52:53] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '36.64 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-28 22:52:53] local.DEBUG: print  before faction by provider price  
[2023-03-28 22:52:53] local.DEBUG: print  after faction by provider price  
[2023-03-28 22:52:53] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 22:52:53] local.DEBUG: print1  
[2023-03-28 22:52:53] local.DEBUG: print  2  
[2023-03-28 23:00:50] local.INFO: header  
[2023-03-28 23:00:50] local.INFO: header after fliter  
[2023-03-28 23:00:50] local.INFO: Body  after fliter  
[2023-03-28 23:00:50] local.INFO: array (
)  
[2023-03-28 23:00:50] local.INFO: transaction14  
[2023-03-28 23:00:50] local.INFO: first inquery phone = 106400860  
[2023-03-28 23:00:53] local.DEBUG: response querySubBalance  
[2023-03-28 23:00:53] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#8.70 GB#28-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 23:00:53] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '8.70 GB',
  4 => '28-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-28 23:00:53] local.DEBUG: print  before faction by provider price  
[2023-03-28 23:00:53] local.DEBUG: print  after faction by provider price  
[2023-03-28 23:00:53] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-28 23:00:53] local.DEBUG: print1  
[2023-03-28 23:00:53] local.DEBUG: print  2  
[2023-03-28 23:01:39] local.INFO: header  
[2023-03-28 23:01:39] local.INFO: header after fliter  
[2023-03-28 23:01:39] local.INFO: Body  after fliter  
[2023-03-28 23:01:39] local.INFO: array (
)  
[2023-03-28 23:01:39] local.INFO: transaction14  
[2023-03-28 23:01:39] local.INFO: first inquery phone = 106400598  
[2023-03-28 23:01:42] local.DEBUG: response querySubBalance  
[2023-03-28 23:01:42] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#36.49 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 23:01:42] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '36.49 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-28 23:01:42] local.DEBUG: print  before faction by provider price  
[2023-03-28 23:01:42] local.DEBUG: print  after faction by provider price  
[2023-03-28 23:01:42] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 23:01:42] local.DEBUG: print1  
[2023-03-28 23:01:42] local.DEBUG: print  2  
[2023-03-28 23:02:10] local.INFO: header  
[2023-03-28 23:02:10] local.INFO: header after fliter  
[2023-03-28 23:02:10] local.INFO: Body  after fliter  
[2023-03-28 23:02:10] local.INFO: array (
)  
[2023-03-28 23:02:10] local.INFO: transaction14  
[2023-03-28 23:02:10] local.INFO: first inquery phone = 106400598  
[2023-03-28 23:02:13] local.DEBUG: response querySubBalance  
[2023-03-28 23:02:13] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#36.49 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-28 23:02:13] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '36.49 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-28 23:02:13] local.DEBUG: print  before faction by provider price  
[2023-03-28 23:02:14] local.DEBUG: print  after faction by provider price  
[2023-03-28 23:02:14] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-28 23:02:14] local.DEBUG: print1  
[2023-03-28 23:02:14] local.DEBUG: print  2  
