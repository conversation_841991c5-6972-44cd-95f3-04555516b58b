[2023-03-24 00:25:18] local.INFO: header  
[2023-03-24 00:25:18] local.INFO: header after fliter  
[2023-03-24 00:25:18] local.INFO: Body  after fliter  
[2023-03-24 00:25:18] local.INFO: array (
)  
[2023-03-24 00:25:18] local.INFO: transaction14  
[2023-03-24 00:25:18] local.INFO: first inquery phone = 106400860  
[2023-03-24 00:25:21] local.DEBUG: response querySubBalance  
[2023-03-24 00:25:21] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#12.27 GB#23-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 00:25:21] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '12.27 GB',
  4 => '23-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-24 00:25:21] local.DEBUG: print  before faction by provider price  
[2023-03-24 00:25:21] local.DEBUG: print  after faction by provider price  
[2023-03-24 00:25:21] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-24 00:25:21] local.DEBUG: print1  
[2023-03-24 00:25:21] local.DEBUG: print  2  
[2023-03-24 00:25:42] local.INFO: header  
[2023-03-24 00:25:42] local.INFO: header after fliter  
[2023-03-24 00:25:42] local.INFO: Body  after fliter  
[2023-03-24 00:25:42] local.INFO: array (
)  
[2023-03-24 00:25:42] local.INFO: transaction14  
[2023-03-24 00:25:42] local.INFO: first inquery phone = 106400976  
[2023-03-24 00:25:46] local.DEBUG: response querySubBalance  
[2023-03-24 00:25:46] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#78.62 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 00:25:46] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '78.62 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-24 00:25:46] local.DEBUG: print  before faction by provider price  
[2023-03-24 00:25:46] local.DEBUG: print  after faction by provider price  
[2023-03-24 00:25:46] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 00:25:46] local.DEBUG: print1  
[2023-03-24 00:25:46] local.DEBUG: print  2  
[2023-03-24 00:29:28] local.INFO: header  
[2023-03-24 00:29:28] local.INFO: header after fliter  
[2023-03-24 00:29:28] local.INFO: Body  after fliter  
[2023-03-24 00:29:28] local.INFO: array (
)  
[2023-03-24 00:29:28] local.INFO: transaction14  
[2023-03-24 00:29:28] local.INFO: first inquery phone = 106400598  
[2023-03-24 00:29:32] local.DEBUG: response querySubBalance  
[2023-03-24 00:29:32] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#167.95 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 00:29:32] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '167.95 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-24 00:29:32] local.DEBUG: print  before faction by provider price  
[2023-03-24 00:29:32] local.DEBUG: print  after faction by provider price  
[2023-03-24 00:29:32] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 00:29:32] local.DEBUG: print1  
[2023-03-24 00:29:32] local.DEBUG: print  2  
[2023-03-24 00:45:13] local.INFO: header  
[2023-03-24 00:45:13] local.INFO: header after fliter  
[2023-03-24 00:45:13] local.INFO: Body  after fliter  
[2023-03-24 00:45:13] local.INFO: array (
)  
[2023-03-24 00:45:13] local.INFO: transaction14  
[2023-03-24 00:45:13] local.INFO: first inquery phone = 106400598  
[2023-03-24 00:45:16] local.DEBUG: response querySubBalance  
[2023-03-24 00:45:16] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#167.17 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 00:45:16] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '167.17 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-24 00:45:16] local.DEBUG: print  before faction by provider price  
[2023-03-24 00:45:16] local.DEBUG: print  after faction by provider price  
[2023-03-24 00:45:16] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 00:45:16] local.DEBUG: print1  
[2023-03-24 00:45:16] local.DEBUG: print  2  
[2023-03-24 00:45:56] local.INFO: header  
[2023-03-24 00:45:56] local.INFO: header after fliter  
[2023-03-24 00:45:56] local.INFO: Body  after fliter  
[2023-03-24 00:45:56] local.INFO: array (
)  
[2023-03-24 00:45:56] local.INFO: transaction14  
[2023-03-24 00:45:56] local.INFO: first inquery phone = 106400860  
[2023-03-24 00:45:58] local.DEBUG: response querySubBalance  
[2023-03-24 00:45:58] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#11.88 GB#23-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 00:45:58] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '11.88 GB',
  4 => '23-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-24 00:45:58] local.DEBUG: print  before faction by provider price  
[2023-03-24 00:45:58] local.DEBUG: print  after faction by provider price  
[2023-03-24 00:45:58] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-24 00:45:58] local.DEBUG: print1  
[2023-03-24 00:45:58] local.DEBUG: print  2  
[2023-03-24 00:46:13] local.INFO: header  
[2023-03-24 00:46:13] local.INFO: header after fliter  
[2023-03-24 00:46:13] local.INFO: Body  after fliter  
[2023-03-24 00:46:13] local.INFO: array (
)  
[2023-03-24 00:46:13] local.INFO: transaction14  
[2023-03-24 00:46:13] local.INFO: first inquery phone = 106400976  
[2023-03-24 00:46:17] local.DEBUG: response querySubBalance  
[2023-03-24 00:46:17] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#78.18 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 00:46:17] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '78.18 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-24 00:46:17] local.DEBUG: print  before faction by provider price  
[2023-03-24 00:46:17] local.DEBUG: print  after faction by provider price  
[2023-03-24 00:46:17] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 00:46:17] local.DEBUG: print1  
[2023-03-24 00:46:17] local.DEBUG: print  2  
[2023-03-24 00:47:08] local.INFO: header  
[2023-03-24 00:47:08] local.INFO: header after fliter  
[2023-03-24 00:47:08] local.INFO: Body  after fliter  
[2023-03-24 00:47:08] local.INFO: array (
)  
[2023-03-24 00:47:08] local.INFO: transaction14  
[2023-03-24 00:47:08] local.INFO: first inquery phone = 106400598  
[2023-03-24 00:47:11] local.DEBUG: response querySubBalance  
[2023-03-24 00:47:11] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#167.02 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 00:47:11] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '167.02 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-24 00:47:11] local.DEBUG: print  before faction by provider price  
[2023-03-24 00:47:11] local.DEBUG: print  after faction by provider price  
[2023-03-24 00:47:11] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 00:47:11] local.DEBUG: print1  
[2023-03-24 00:47:11] local.DEBUG: print  2  
[2023-03-24 00:47:53] local.INFO: header  
[2023-03-24 00:47:53] local.INFO: header after fliter  
[2023-03-24 00:47:53] local.INFO: Body  after fliter  
[2023-03-24 00:47:53] local.INFO: array (
)  
[2023-03-24 00:47:53] local.INFO: transaction14  
[2023-03-24 00:47:53] local.INFO: first inquery phone = 106400598  
[2023-03-24 00:47:55] local.DEBUG: response querySubBalance  
[2023-03-24 00:47:55] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#166.97 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 00:47:55] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '166.97 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-24 00:47:55] local.DEBUG: print  before faction by provider price  
[2023-03-24 00:47:55] local.DEBUG: print  after faction by provider price  
[2023-03-24 00:47:55] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 00:47:55] local.DEBUG: print1  
[2023-03-24 00:47:55] local.DEBUG: print  2  
[2023-03-24 01:06:40] local.INFO: header  
[2023-03-24 01:06:40] local.INFO: header after fliter  
[2023-03-24 01:06:40] local.INFO: Body  after fliter  
[2023-03-24 01:06:40] local.INFO: array (
)  
[2023-03-24 01:06:40] local.INFO: transaction14  
[2023-03-24 01:06:40] local.INFO: first inquery phone = 103322500  
[2023-03-24 01:06:43] local.DEBUG: response querySubBalance  
[2023-03-24 01:06:43] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#4.42 MB#21-04-2023#0#0##10322.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 01:06:43] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '4.42 MB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10322.00',
  9 => '4G 25',
)  
[2023-03-24 01:06:43] local.DEBUG: print  before faction by provider price  
[2023-03-24 01:06:43] local.DEBUG: print  after faction by provider price  
[2023-03-24 01:06:43] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-24 01:06:43] local.DEBUG: print1  
[2023-03-24 01:06:43] local.DEBUG: print  2  
[2023-03-24 01:07:02] local.INFO: header  
[2023-03-24 01:07:02] local.CRITICAL: ****************************1  
[2023-03-24 01:07:02] local.ALERT: reach here  
[2023-03-24 01:07:02] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-24 01:07:02] local.ERROR: المبلغ  
[2023-03-24 01:07:02] local.ERROR: 4,000.00  
[2023-03-24 01:07:02] local.ERROR: مبلغ وقدرة  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 3  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 3  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 3  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 3  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 3  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 3  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 3  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 3  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 3  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 2  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 2  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 2  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 2  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 2  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 2  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 2  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 1  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 1  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 1  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 1  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 1  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 2  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 10013  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 10013  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 10013  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 10013  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 1  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 3  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 3  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 40  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 40  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 40  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 40  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.WARNING: 1  
[2023-03-24 01:07:02] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 200  
[2023-03-24 01:07:02] local.ALERT: 40  
[2023-03-24 01:07:02] local.CRITICAL: ****************************2  
[2023-03-24 01:07:02] local.CRITICAL: ****************************  
[2023-03-24 01:07:02] local.CRITICAL:   
[2023-03-24 01:07:02] local.CRITICAL: ****************************  
[2023-03-24 01:07:04] local.INFO: {
  "ClientBalanceResult": "451476.6400"
}  
[2023-03-24 01:07:04] local.INFO: array (
  'ClientBalanceResult' => '451476.6400',
)  
[2023-03-24 01:07:04] local.DEBUG: lattttef  
[2023-03-24 01:07:04] local.DEBUG: array (
  'ClientBalanceResult' => '451476.6400',
)  
[2023-03-24 01:07:04] local.INFO: transaction14  
[2023-03-24 01:07:04] local.INFO: first inquery phone = 103322500  
[2023-03-24 01:07:06] local.DEBUG: response querySubBalance  
[2023-03-24 01:07:06] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#4.42 MB#21-04-2023#0#0##10322.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 01:07:06] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '4.42 MB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10322.00',
  9 => '4G 25',
)  
[2023-03-24 01:07:06] local.DEBUG: print  before faction by provider price  
[2023-03-24 01:07:06] local.DEBUG: print  after faction by provider price  
[2023-03-24 01:07:06] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-24 01:07:06] local.DEBUG: print1  
[2023-03-24 01:07:06] local.DEBUG: print  2  
[2023-03-24 01:07:06] local.INFO: transaction1  
[2023-03-24 01:07:06] local.INFO: transaction2  
[2023-03-24 01:07:06] local.INFO: transaction3  
[2023-03-24 01:07:06] local.INFO: transaction4  
[2023-03-24 01:07:06] local.INFO: transaction4  
[2023-03-24 01:07:06] local.INFO: transaction5  
[2023-03-24 01:07:06] local.INFO: transaction6  
[2023-03-24 01:07:06] local.INFO: transaction7  
[2023-03-24 01:07:06] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103322500',
  'State' => 0,
  'lateflog' => '337620',
)  
[2023-03-24 01:07:06] local.INFO: transaction8  
[2023-03-24 01:07:07] local.INFO: transaction9  
[2023-03-24 01:07:07] local.INFO: transaction10  
[2023-03-24 01:07:07] local.INFO: transaction11  
[2023-03-24 01:07:07] local.INFO: 12  
[2023-03-24 01:07:07] local.INFO: transaction13  
[2023-03-24 01:07:07] local.INFO: transaction14  
[2023-03-24 01:07:07] local.INFO: transaction19  
[2023-03-24 01:07:07] local.INFO: transaction15  
[2023-03-24 01:07:07] local.INFO: transaction16  
[2023-03-24 01:07:07] local.INFO: 98#103322500#4000.00#0  
[2023-03-24 01:07:16] local.INFO: transaction18  
[2023-03-24 01:07:16] local.INFO: array (
  0 => 'OK',
  1 => '12,766,261.83',
  2 => 'NONE',
  3 => '60966851',
  4 => '4,000.00',
)  
[2023-03-24 01:07:24] local.INFO: header  
[2023-03-24 01:07:24] local.INFO: header after fliter  
[2023-03-24 01:07:24] local.INFO: Body  after fliter  
[2023-03-24 01:07:24] local.INFO: array (
)  
[2023-03-24 01:07:24] local.INFO: transaction14  
[2023-03-24 01:07:24] local.INFO: first inquery phone = 103322500  
[2023-03-24 01:07:26] local.DEBUG: response querySubBalance  
[2023-03-24 01:07:26] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#4.42 MB#21-04-2023#0#0##10322.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 01:07:26] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '4.42 MB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10322.00',
  9 => '4G 25',
)  
[2023-03-24 01:07:26] local.DEBUG: print  before faction by provider price  
[2023-03-24 01:07:26] local.DEBUG: print  after faction by provider price  
[2023-03-24 01:07:26] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-24 01:07:26] local.DEBUG: print1  
[2023-03-24 01:07:26] local.DEBUG: print  2  
[2023-03-24 01:07:30] local.INFO: header  
[2023-03-24 01:07:30] local.INFO: header after fliter  
[2023-03-24 01:07:30] local.INFO: Body  after fliter  
[2023-03-24 01:07:30] local.INFO: array (
)  
[2023-03-24 01:07:30] local.INFO: transaction14  
[2023-03-24 01:07:30] local.INFO: first inquery phone = 103322500  
[2023-03-24 01:07:33] local.DEBUG: response querySubBalance  
[2023-03-24 01:07:33] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#25.00 GB#24-04-2023#0#0##10822.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 01:07:33] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '25.00 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10822.00',
  9 => '4G 25',
)  
[2023-03-24 01:07:33] local.DEBUG: print  before faction by provider price  
[2023-03-24 01:07:33] local.DEBUG: print  after faction by provider price  
[2023-03-24 01:07:33] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-24 01:07:33] local.DEBUG: print1  
[2023-03-24 01:07:33] local.DEBUG: print  2  
[2023-03-24 01:30:59] local.INFO: header  
[2023-03-24 01:30:59] local.INFO: header after fliter  
[2023-03-24 01:30:59] local.INFO: Body  after fliter  
[2023-03-24 01:30:59] local.INFO: array (
)  
[2023-03-24 01:30:59] local.INFO: transaction14  
[2023-03-24 01:30:59] local.INFO: first inquery phone = 106400860  
[2023-03-24 01:31:03] local.DEBUG: response querySubBalance  
[2023-03-24 01:31:03] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#10.61 GB#23-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 01:31:03] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '10.61 GB',
  4 => '23-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-24 01:31:03] local.DEBUG: print  before faction by provider price  
[2023-03-24 01:31:03] local.DEBUG: print  after faction by provider price  
[2023-03-24 01:31:03] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-24 01:31:03] local.DEBUG: print1  
[2023-03-24 01:31:03] local.DEBUG: print  2  
[2023-03-24 01:31:13] local.INFO: header  
[2023-03-24 01:31:13] local.INFO: header after fliter  
[2023-03-24 01:31:13] local.INFO: Body  after fliter  
[2023-03-24 01:31:13] local.INFO: array (
)  
[2023-03-24 01:31:13] local.INFO: transaction14  
[2023-03-24 01:31:13] local.INFO: first inquery phone = 106400976  
[2023-03-24 01:31:16] local.DEBUG: response querySubBalance  
[2023-03-24 01:31:16] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#77.01 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 01:31:16] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '77.01 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-24 01:31:16] local.DEBUG: print  before faction by provider price  
[2023-03-24 01:31:16] local.DEBUG: print  after faction by provider price  
[2023-03-24 01:31:16] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 01:31:16] local.DEBUG: print1  
[2023-03-24 01:31:16] local.DEBUG: print  2  
[2023-03-24 01:32:59] local.INFO: header  
[2023-03-24 01:32:59] local.INFO: header after fliter  
[2023-03-24 01:32:59] local.INFO: Body  after fliter  
[2023-03-24 01:32:59] local.INFO: array (
)  
[2023-03-24 01:32:59] local.INFO: transaction14  
[2023-03-24 01:32:59] local.INFO: first inquery phone = 106400598  
[2023-03-24 01:33:03] local.DEBUG: response querySubBalance  
[2023-03-24 01:33:03] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#165.47 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 01:33:03] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '165.47 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-24 01:33:03] local.DEBUG: print  before faction by provider price  
[2023-03-24 01:33:03] local.DEBUG: print  after faction by provider price  
[2023-03-24 01:33:03] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 01:33:03] local.DEBUG: print1  
[2023-03-24 01:33:03] local.DEBUG: print  2  
[2023-03-24 01:57:26] local.INFO: header  
[2023-03-24 01:57:26] local.INFO: header after fliter  
[2023-03-24 01:57:26] local.INFO: Body  after fliter  
[2023-03-24 01:57:26] local.INFO: array (
)  
[2023-03-24 01:57:26] local.INFO: transaction14  
[2023-03-24 01:57:26] local.INFO: first inquery phone = 106488408  
[2023-03-24 01:57:30] local.DEBUG: response querySubBalance  
[2023-03-24 01:57:30] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#11.84 GB#21-04-2023#0#0##14500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 01:57:30] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '11.84 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '14500.00',
  9 => '4G 25',
)  
[2023-03-24 01:57:30] local.DEBUG: print  before faction by provider price  
[2023-03-24 01:57:30] local.DEBUG: print  after faction by provider price  
[2023-03-24 01:57:30] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-24 01:57:30] local.DEBUG: print1  
[2023-03-24 01:57:30] local.DEBUG: print  2  
[2023-03-24 02:32:52] local.INFO: header  
[2023-03-24 02:32:52] local.INFO: header after fliter  
[2023-03-24 02:32:52] local.INFO: Body  after fliter  
[2023-03-24 02:32:52] local.INFO: array (
)  
[2023-03-24 02:32:52] local.INFO: transaction14  
[2023-03-24 02:32:52] local.INFO: first inquery phone = 106400860  
[2023-03-24 02:32:55] local.DEBUG: response querySubBalance  
[2023-03-24 02:32:55] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#9.78 GB#23-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 02:32:55] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '9.78 GB',
  4 => '23-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-24 02:32:55] local.DEBUG: print  before faction by provider price  
[2023-03-24 02:32:55] local.DEBUG: print  after faction by provider price  
[2023-03-24 02:32:55] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-24 02:32:55] local.DEBUG: print1  
[2023-03-24 02:32:55] local.DEBUG: print  2  
[2023-03-24 02:32:56] local.INFO: header  
[2023-03-24 02:32:56] local.CRITICAL: ****************************1  
[2023-03-24 02:32:56] local.ALERT: reach here  
[2023-03-24 02:32:56] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-24 02:32:56] local.ERROR: المبلغ  
[2023-03-24 02:32:56] local.ERROR: 4,000.00  
[2023-03-24 02:32:56] local.ERROR: مبلغ وقدرة  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 3  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 3  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 3  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 3  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 3  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 3  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 3  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 3  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 3  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 2  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 2  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 2  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 2  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 2  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 2  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 2  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 1  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 1  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 1  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 1  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 1  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 2  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 10013  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 10013  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 10013  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 10013  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 1  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 3  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 3  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 40  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 40  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 40  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 40  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.WARNING: 1  
[2023-03-24 02:32:56] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 200  
[2023-03-24 02:32:56] local.ALERT: 40  
[2023-03-24 02:32:56] local.CRITICAL: ****************************2  
[2023-03-24 02:32:56] local.CRITICAL: ****************************  
[2023-03-24 02:32:56] local.CRITICAL:   
[2023-03-24 02:32:56] local.CRITICAL: ****************************  
[2023-03-24 02:32:56] local.INFO: {
  "ClientBalanceResult": "68854.7500"
}  
[2023-03-24 02:32:56] local.INFO: array (
  'ClientBalanceResult' => '68854.7500',
)  
[2023-03-24 02:32:56] local.DEBUG: lattttef  
[2023-03-24 02:32:56] local.DEBUG: array (
  'ClientBalanceResult' => '68854.7500',
)  
[2023-03-24 02:32:56] local.INFO: transaction14  
[2023-03-24 02:32:56] local.INFO: first inquery phone = 101900047  
[2023-03-24 02:32:58] local.DEBUG: response querySubBalance  
[2023-03-24 02:32:58] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#4.36 GB#27-03-2023#0#0##3000.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 02:32:58] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '4.36 GB',
  4 => '27-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '3000.00',
  9 => '4G 25',
)  
[2023-03-24 02:32:58] local.DEBUG: print  before faction by provider price  
[2023-03-24 02:32:58] local.DEBUG: print  after faction by provider price  
[2023-03-24 02:32:58] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-24 02:32:58] local.DEBUG: print1  
[2023-03-24 02:32:58] local.DEBUG: print  2  
[2023-03-24 02:32:58] local.INFO: transaction1  
[2023-03-24 02:32:58] local.INFO: transaction2  
[2023-03-24 02:32:58] local.INFO: transaction3  
[2023-03-24 02:32:58] local.INFO: transaction4  
[2023-03-24 02:32:58] local.INFO: transaction4  
[2023-03-24 02:32:58] local.INFO: transaction5  
[2023-03-24 02:32:58] local.INFO: transaction6  
[2023-03-24 02:32:58] local.INFO: transaction7  
[2023-03-24 02:32:58] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '101900047',
  'State' => 0,
  'lateflog' => '587932',
)  
[2023-03-24 02:32:58] local.INFO: transaction8  
[2023-03-24 02:32:58] local.INFO: transaction9  
[2023-03-24 02:32:58] local.INFO: transaction10  
[2023-03-24 02:32:58] local.INFO: transaction11  
[2023-03-24 02:32:58] local.INFO: 12  
[2023-03-24 02:32:58] local.INFO: transaction13  
[2023-03-24 02:32:58] local.INFO: transaction14  
[2023-03-24 02:32:58] local.INFO: transaction19  
[2023-03-24 02:32:58] local.INFO: transaction15  
[2023-03-24 02:32:58] local.INFO: transaction16  
[2023-03-24 02:32:58] local.INFO: 98#101900047#4000.00#0  
[2023-03-24 02:33:07] local.INFO: transaction18  
[2023-03-24 02:33:07] local.INFO: array (
  0 => 'OK',
  1 => '12,611,158.83',
  2 => 'NONE',
  3 => '60974096',
  4 => '4,000.00',
)  
[2023-03-24 02:33:08] local.INFO: header  
[2023-03-24 02:33:08] local.INFO: header after fliter  
[2023-03-24 02:33:08] local.INFO: Body  after fliter  
[2023-03-24 02:33:08] local.INFO: array (
)  
[2023-03-24 02:33:08] local.INFO: transaction14  
[2023-03-24 02:33:08] local.INFO: first inquery phone = 106400976  
[2023-03-24 02:33:10] local.DEBUG: response querySubBalance  
[2023-03-24 02:33:10] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#75.94 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 02:33:10] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '75.94 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-24 02:33:10] local.DEBUG: print  before faction by provider price  
[2023-03-24 02:33:10] local.DEBUG: print  after faction by provider price  
[2023-03-24 02:33:10] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 02:33:10] local.DEBUG: print1  
[2023-03-24 02:33:10] local.DEBUG: print  2  
[2023-03-24 02:33:33] local.INFO: header  
[2023-03-24 02:33:33] local.INFO: header after fliter  
[2023-03-24 02:33:33] local.INFO: Body  after fliter  
[2023-03-24 02:33:33] local.INFO: array (
)  
[2023-03-24 02:33:33] local.INFO: transaction14  
[2023-03-24 02:33:33] local.INFO: first inquery phone = 106400598  
[2023-03-24 02:33:35] local.DEBUG: response querySubBalance  
[2023-03-24 02:33:35] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#162.93 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 02:33:35] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '162.93 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-24 02:33:35] local.DEBUG: print  before faction by provider price  
[2023-03-24 02:33:35] local.DEBUG: print  after faction by provider price  
[2023-03-24 02:33:35] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 02:33:35] local.DEBUG: print1  
[2023-03-24 02:33:35] local.DEBUG: print  2  
[2023-03-24 02:35:09] local.INFO: header  
[2023-03-24 02:35:09] local.INFO: header after fliter  
[2023-03-24 02:35:09] local.INFO: Body  after fliter  
[2023-03-24 02:35:09] local.INFO: array (
)  
[2023-03-24 02:35:09] local.INFO: transaction14  
[2023-03-24 02:35:09] local.INFO: first inquery phone = 106400976  
[2023-03-24 02:35:11] local.DEBUG: response querySubBalance  
[2023-03-24 02:35:11] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#75.89 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 02:35:11] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '75.89 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-24 02:35:11] local.DEBUG: print  before faction by provider price  
[2023-03-24 02:35:11] local.DEBUG: print  after faction by provider price  
[2023-03-24 02:35:11] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 02:35:11] local.DEBUG: print1  
[2023-03-24 02:35:11] local.DEBUG: print  2  
[2023-03-24 02:36:06] local.INFO: header  
[2023-03-24 02:36:06] local.INFO: header after fliter  
[2023-03-24 02:36:06] local.INFO: Body  after fliter  
[2023-03-24 02:36:06] local.INFO: array (
)  
[2023-03-24 02:36:06] local.INFO: transaction14  
[2023-03-24 02:36:06] local.INFO: first inquery phone = 106400976  
[2023-03-24 02:36:08] local.DEBUG: response querySubBalance  
[2023-03-24 02:36:08] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#75.89 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 02:36:08] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '75.89 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-24 02:36:08] local.DEBUG: print  before faction by provider price  
[2023-03-24 02:36:08] local.DEBUG: print  after faction by provider price  
[2023-03-24 02:36:08] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 02:36:08] local.DEBUG: print1  
[2023-03-24 02:36:08] local.DEBUG: print  2  
[2023-03-24 02:36:15] local.INFO: header  
[2023-03-24 02:36:15] local.INFO: header after fliter  
[2023-03-24 02:36:15] local.INFO: Body  after fliter  
[2023-03-24 02:36:15] local.INFO: array (
)  
[2023-03-24 02:36:15] local.INFO: transaction14  
[2023-03-24 02:36:15] local.INFO: first inquery phone = 106400598  
[2023-03-24 02:36:17] local.DEBUG: response querySubBalance  
[2023-03-24 02:36:17] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#162.78 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 02:36:17] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '162.78 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-24 02:36:17] local.DEBUG: print  before faction by provider price  
[2023-03-24 02:36:17] local.DEBUG: print  after faction by provider price  
[2023-03-24 02:36:17] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 02:36:17] local.DEBUG: print1  
[2023-03-24 02:36:17] local.DEBUG: print  2  
[2023-03-24 02:38:02] local.INFO: header  
[2023-03-24 02:38:02] local.INFO: header after fliter  
[2023-03-24 02:38:02] local.INFO: Body  after fliter  
[2023-03-24 02:38:02] local.INFO: array (
)  
[2023-03-24 02:38:02] local.INFO: transaction14  
[2023-03-24 02:38:02] local.INFO: first inquery phone = 106400598  
[2023-03-24 02:38:04] local.DEBUG: response querySubBalance  
[2023-03-24 02:38:04] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#162.68 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 02:38:04] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '162.68 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-24 02:38:04] local.DEBUG: print  before faction by provider price  
[2023-03-24 02:38:04] local.DEBUG: print  after faction by provider price  
[2023-03-24 02:38:04] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 02:38:04] local.DEBUG: print1  
[2023-03-24 02:38:04] local.DEBUG: print  2  
[2023-03-24 02:38:26] local.INFO: header  
[2023-03-24 02:38:26] local.INFO: header after fliter  
[2023-03-24 02:38:26] local.INFO: Body  after fliter  
[2023-03-24 02:38:26] local.INFO: array (
)  
[2023-03-24 02:38:26] local.INFO: transaction14  
[2023-03-24 02:38:26] local.INFO: first inquery phone = 106400860  
[2023-03-24 02:38:28] local.DEBUG: response querySubBalance  
[2023-03-24 02:38:28] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#9.69 GB#23-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 02:38:28] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '9.69 GB',
  4 => '23-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-24 02:38:28] local.DEBUG: print  before faction by provider price  
[2023-03-24 02:38:28] local.DEBUG: print  after faction by provider price  
[2023-03-24 02:38:28] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-24 02:38:28] local.DEBUG: print1  
[2023-03-24 02:38:28] local.DEBUG: print  2  
[2023-03-24 15:52:06] local.INFO: header  
[2023-03-24 15:52:06] local.INFO: header after fliter  
[2023-03-24 15:52:06] local.INFO: Body  after fliter  
[2023-03-24 15:52:06] local.INFO: array (
)  
[2023-03-24 15:52:06] local.INFO: transaction14  
[2023-03-24 15:52:06] local.INFO: first inquery phone = 101034508  
[2023-03-24 15:52:09] local.DEBUG: response querySubBalance  
[2023-03-24 15:52:09] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#27.45 GB#11-04-2023#0#0##1500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 15:52:09] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '27.45 GB',
  4 => '11-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1500.00',
  9 => '4G 25',
)  
[2023-03-24 15:52:09] local.DEBUG: print  before faction by provider price  
[2023-03-24 15:52:09] local.DEBUG: print  after faction by provider price  
[2023-03-24 15:52:09] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-24 15:52:09] local.DEBUG: print1  
[2023-03-24 15:52:09] local.DEBUG: print  2  
[2023-03-24 16:57:39] local.INFO: header  
[2023-03-24 16:57:39] local.CRITICAL: ****************************1  
[2023-03-24 16:57:39] local.ALERT: reach here  
[2023-03-24 16:57:39] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '12000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'إثنا عشر ألف  ر.ي.',
  ),
)  
[2023-03-24 16:57:39] local.ERROR: المبلغ  
[2023-03-24 16:57:39] local.ERROR: 12,000.00  
[2023-03-24 16:57:39] local.ERROR: مبلغ وقدرة  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 3  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 3  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 3  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 3  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 3  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 3  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 3  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 3  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 3  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 2  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 2  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 2  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 2  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 2  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 2  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 2  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 1  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 1  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 1  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 1  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 1  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 2  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 10013  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 10013  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 10013  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 10013  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 1  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 3  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 3  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.WARNING: 1  
[2023-03-24 16:57:39] local.WARNING: array (
  'ID' => 84,
  'Name' => 'باقة 80جيجا 12000 ريال',
  'ServiceID' => 40,
  'Price' => 12000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '12000',
  'PersonnalPrice' => 12000.0,
)  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 200  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 200  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 200  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 200  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 200  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 200  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.ALERT: 40  
[2023-03-24 16:57:39] local.CRITICAL: ****************************2  
[2023-03-24 16:57:39] local.CRITICAL: ****************************  
[2023-03-24 16:57:39] local.CRITICAL:   
[2023-03-24 16:57:39] local.CRITICAL: ****************************  
[2023-03-24 16:57:40] local.INFO: {
  "ClientBalanceResult": "520894.7767"
}  
[2023-03-24 16:57:40] local.INFO: array (
  'ClientBalanceResult' => '520894.7767',
)  
[2023-03-24 16:57:40] local.DEBUG: lattttef  
[2023-03-24 16:57:40] local.DEBUG: array (
  'ClientBalanceResult' => '520894.7767',
)  
[2023-03-24 16:57:40] local.INFO: transaction1  
[2023-03-24 16:57:40] local.INFO: transaction2  
[2023-03-24 16:57:40] local.INFO: transaction3  
[2023-03-24 16:57:40] local.INFO: transaction4  
[2023-03-24 16:57:40] local.INFO: transaction4  
[2023-03-24 16:57:40] local.INFO: transaction5  
[2023-03-24 16:57:40] local.INFO: transaction6  
[2023-03-24 16:57:40] local.INFO: transaction7  
[2023-03-24 16:57:40] local.DEBUG: array (
  'AMT' => 12000.0,
  'CType' => 0,
  'FID' => 84,
  'LType' => '1',
  'SID' => 40,
  'SNO' => '798711307',
  'State' => 0,
  'lateflog' => '70760',
)  
[2023-03-24 16:57:40] local.INFO: transaction8  
[2023-03-24 16:57:40] local.INFO: transaction9  
[2023-03-24 16:57:40] local.INFO: transaction10  
[2023-03-24 16:57:40] local.INFO: transaction11  
[2023-03-24 16:57:40] local.INFO: 12  
[2023-03-24 16:57:40] local.INFO: transaction13  
[2023-03-24 16:57:40] local.INFO: transaction14  
[2023-03-24 16:57:40] local.INFO: transaction19  
[2023-03-24 16:57:40] local.INFO: transaction15  
[2023-03-24 16:57:48] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '12060',
  'remainAmount' => 29577279,
  'mallrem' => -20422721,
  'transid' => '4389583',
  'ref_id' => 25972305,
)  
[2023-03-24 17:58:14] local.INFO: header  
[2023-03-24 17:58:14] local.INFO: header after fliter  
[2023-03-24 17:58:14] local.INFO: Body  after fliter  
[2023-03-24 17:58:14] local.INFO: array (
)  
[2023-03-24 17:58:14] local.INFO: transaction14  
[2023-03-24 17:58:14] local.INFO: first inquery phone = 106404309  
[2023-03-24 17:58:17] local.DEBUG: response querySubBalance  
[2023-03-24 17:58:17] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#60.00 GB#24-04-2023#0#0##1184.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 17:58:17] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '60.00 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1184.00',
  9 => '4G 60',
)  
[2023-03-24 17:58:17] local.DEBUG: print  before faction by provider price  
[2023-03-24 17:58:17] local.DEBUG: print  after faction by provider price  
[2023-03-24 17:58:17] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-24 17:58:17] local.DEBUG: print1  
[2023-03-24 17:58:17] local.DEBUG: print  2  
[2023-03-24 20:26:00] local.INFO: header  
[2023-03-24 20:26:00] local.INFO: header after fliter  
[2023-03-24 20:26:00] local.INFO: Body  after fliter  
[2023-03-24 20:26:00] local.INFO: array (
)  
[2023-03-24 20:26:00] local.INFO: transaction14  
[2023-03-24 20:26:00] local.INFO: first inquery phone = 103322059  
[2023-03-24 20:26:02] local.DEBUG: response querySubBalance  
[2023-03-24 20:26:02] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#151.37 GB#20-04-2023#0#0##7000.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 20:26:02] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '151.37 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7000.00',
  9 => '4G 250',
)  
[2023-03-24 20:26:02] local.DEBUG: print  before faction by provider price  
[2023-03-24 20:26:02] local.DEBUG: print  after faction by provider price  
[2023-03-24 20:26:02] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 20:26:02] local.DEBUG: print1  
[2023-03-24 20:26:02] local.DEBUG: print  2  
[2023-03-24 20:26:11] local.INFO: header  
[2023-03-24 20:26:11] local.INFO: header after fliter  
[2023-03-24 20:26:11] local.INFO: Body  after fliter  
[2023-03-24 20:26:11] local.INFO: array (
)  
[2023-03-24 20:26:11] local.INFO: transaction14  
[2023-03-24 20:26:11] local.INFO: first inquery phone = 103322169  
[2023-03-24 20:26:12] local.DEBUG: response querySubBalance  
[2023-03-24 20:26:12] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#156.35 GB#20-04-2023#0#0##7000.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 20:26:12] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '156.35 GB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7000.00',
  9 => '4G 250',
)  
[2023-03-24 20:26:12] local.DEBUG: print  before faction by provider price  
[2023-03-24 20:26:13] local.DEBUG: print  after faction by provider price  
[2023-03-24 20:26:13] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 20:26:13] local.DEBUG: print1  
[2023-03-24 20:26:13] local.DEBUG: print  2  
[2023-03-24 22:08:44] local.INFO: header  
[2023-03-24 22:08:45] local.INFO: header after fliter  
[2023-03-24 22:08:45] local.INFO: Body  after fliter  
[2023-03-24 22:08:45] local.INFO: array (
)  
[2023-03-24 22:08:45] local.INFO: transaction14  
[2023-03-24 22:08:45] local.INFO: first inquery phone = 106488408  
[2023-03-24 22:08:48] local.DEBUG: response querySubBalance  
[2023-03-24 22:08:48] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#8.06 GB#21-04-2023#0#0##14500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 22:08:48] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '8.06 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '14500.00',
  9 => '4G 25',
)  
[2023-03-24 22:08:48] local.DEBUG: print  before faction by provider price  
[2023-03-24 22:08:48] local.DEBUG: print  after faction by provider price  
[2023-03-24 22:08:48] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-24 22:08:48] local.DEBUG: print1  
[2023-03-24 22:08:48] local.DEBUG: print  2  
[2023-03-24 22:19:03] local.INFO: header  
[2023-03-24 22:19:03] local.INFO: header after fliter  
[2023-03-24 22:19:03] local.INFO: Body  after fliter  
[2023-03-24 22:19:03] local.INFO: array (
)  
[2023-03-24 22:19:03] local.INFO: transaction14  
[2023-03-24 22:19:03] local.INFO: first inquery phone = 106400860  
[2023-03-24 22:19:06] local.DEBUG: response querySubBalance  
[2023-03-24 22:19:06] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#12.63 GB#24-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 22:19:06] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '12.63 GB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-24 22:19:06] local.DEBUG: print  before faction by provider price  
[2023-03-24 22:19:06] local.DEBUG: print  after faction by provider price  
[2023-03-24 22:19:06] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-24 22:19:06] local.DEBUG: print1  
[2023-03-24 22:19:06] local.DEBUG: print  2  
[2023-03-24 22:19:34] local.INFO: header  
[2023-03-24 22:19:34] local.INFO: header after fliter  
[2023-03-24 22:19:34] local.INFO: Body  after fliter  
[2023-03-24 22:19:34] local.INFO: array (
)  
[2023-03-24 22:19:34] local.INFO: transaction14  
[2023-03-24 22:19:34] local.INFO: first inquery phone = 106400976  
[2023-03-24 22:19:36] local.DEBUG: response querySubBalance  
[2023-03-24 22:19:36] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#62.77 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 22:19:36] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '62.77 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-24 22:19:36] local.DEBUG: print  before faction by provider price  
[2023-03-24 22:19:36] local.DEBUG: print  after faction by provider price  
[2023-03-24 22:19:36] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 22:19:36] local.DEBUG: print1  
[2023-03-24 22:19:36] local.DEBUG: print  2  
[2023-03-24 22:19:44] local.INFO: header  
[2023-03-24 22:19:44] local.INFO: header after fliter  
[2023-03-24 22:19:44] local.INFO: Body  after fliter  
[2023-03-24 22:19:44] local.INFO: array (
)  
[2023-03-24 22:19:44] local.INFO: transaction14  
[2023-03-24 22:19:44] local.INFO: first inquery phone = 106400598  
[2023-03-24 22:19:47] local.DEBUG: response querySubBalance  
[2023-03-24 22:19:47] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#142.65 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-24 22:19:47] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '142.65 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-24 22:19:47] local.DEBUG: print  before faction by provider price  
[2023-03-24 22:19:47] local.DEBUG: print  after faction by provider price  
[2023-03-24 22:19:47] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-24 22:19:47] local.DEBUG: print1  
[2023-03-24 22:19:47] local.DEBUG: print  2  
