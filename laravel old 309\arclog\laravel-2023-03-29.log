[2023-03-29 00:04:09] local.INFO: header  
[2023-03-29 00:04:09] local.INFO: header after fliter  
[2023-03-29 00:04:09] local.INFO: Body  after fliter  
[2023-03-29 00:04:09] local.INFO: array (
)  
[2023-03-29 00:04:09] local.INFO: transaction14  
[2023-03-29 00:04:09] local.INFO: first inquery phone = 103322758  
[2023-03-29 00:04:21] local.DEBUG: response querySubBalance  
[2023-03-29 00:04:21] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#2.00 MB#01-04-2023#0#0##3500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-29 00:04:21] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '2.00 MB',
  4 => '01-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '3500.00',
  9 => '4G 25',
)  
[2023-03-29 00:04:21] local.DEBUG: print  before faction by provider price  
[2023-03-29 00:04:21] local.DEBUG: print  after faction by provider price  
[2023-03-29 00:04:21] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-29 00:04:21] local.DEBUG: print1  
[2023-03-29 00:04:21] local.DEBUG: print  2  
[2023-03-29 00:04:27] local.INFO: header  
[2023-03-29 00:04:27] local.CRITICAL: ****************************1  
[2023-03-29 00:04:27] local.ALERT: reach here  
[2023-03-29 00:04:27] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-29 00:04:27] local.ERROR: المبلغ  
[2023-03-29 00:04:27] local.ERROR: 4,000.00  
[2023-03-29 00:04:27] local.ERROR: مبلغ وقدرة  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 3  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 3  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 3  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 3  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 3  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 3  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 3  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 3  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 3  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 2  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 2  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 2  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 2  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 2  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 2  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 2  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 1  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 1  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 1  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 1  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 1  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 2  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 10013  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 10013  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 10013  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 10013  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 1  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 3  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 3  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 40  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 40  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 40  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 40  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.WARNING: 1  
[2023-03-29 00:04:27] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 200  
[2023-03-29 00:04:27] local.ALERT: 40  
[2023-03-29 00:04:27] local.CRITICAL: ****************************2  
[2023-03-29 00:04:27] local.CRITICAL: ****************************  
[2023-03-29 00:04:27] local.CRITICAL:   
[2023-03-29 00:04:27] local.CRITICAL: ****************************  
[2023-03-29 00:04:28] local.INFO: {
  "ClientBalanceResult": "229887.8750"
}  
[2023-03-29 00:04:28] local.INFO: array (
  'ClientBalanceResult' => '229887.8750',
)  
[2023-03-29 00:04:28] local.DEBUG: lattttef  
[2023-03-29 00:04:28] local.DEBUG: array (
  'ClientBalanceResult' => '229887.8750',
)  
[2023-03-29 00:04:28] local.INFO: transaction14  
[2023-03-29 00:04:28] local.INFO: first inquery phone = 103322758  
[2023-03-29 00:04:31] local.DEBUG: response querySubBalance  
[2023-03-29 00:04:31] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#2.00 MB#01-04-2023#0#0##3500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-29 00:04:31] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '2.00 MB',
  4 => '01-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '3500.00',
  9 => '4G 25',
)  
[2023-03-29 00:04:31] local.DEBUG: print  before faction by provider price  
[2023-03-29 00:04:31] local.DEBUG: print  after faction by provider price  
[2023-03-29 00:04:31] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-29 00:04:31] local.DEBUG: print1  
[2023-03-29 00:04:31] local.DEBUG: print  2  
[2023-03-29 00:04:31] local.INFO: transaction1  
[2023-03-29 00:04:31] local.INFO: transaction2  
[2023-03-29 00:04:31] local.INFO: transaction3  
[2023-03-29 00:04:32] local.INFO: transaction4  
[2023-03-29 00:04:32] local.INFO: transaction4  
[2023-03-29 00:04:32] local.INFO: transaction5  
[2023-03-29 00:04:32] local.INFO: transaction6  
[2023-03-29 00:04:32] local.INFO: transaction7  
[2023-03-29 00:04:32] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103322758',
  'State' => 0,
  'lateflog' => '337620',
)  
[2023-03-29 00:04:32] local.INFO: transaction8  
[2023-03-29 00:04:32] local.INFO: transaction9  
[2023-03-29 00:04:32] local.INFO: transaction10  
[2023-03-29 00:04:32] local.INFO: transaction11  
[2023-03-29 00:04:32] local.INFO: 12  
[2023-03-29 00:04:32] local.INFO: transaction13  
[2023-03-29 00:04:32] local.INFO: transaction14  
[2023-03-29 00:04:32] local.INFO: transaction19  
[2023-03-29 00:04:32] local.INFO: transaction15  
[2023-03-29 00:04:32] local.INFO: transaction16  
[2023-03-29 00:04:32] local.INFO: 98#103322758#4000.00#0  
[2023-03-29 00:04:43] local.INFO: transaction18  
[2023-03-29 00:04:43] local.INFO: array (
  0 => 'OK',
  1 => '3,411,996.83',
  2 => 'NONE',
  3 => '61516281',
  4 => '4,000.00',
)  
[2023-03-29 01:21:34] local.INFO: header  
[2023-03-29 01:21:34] local.INFO: header after fliter  
[2023-03-29 01:21:34] local.INFO: Body  after fliter  
[2023-03-29 01:21:34] local.INFO: array (
)  
[2023-03-29 01:21:34] local.INFO: transaction14  
[2023-03-29 01:21:34] local.INFO: first inquery phone = 101034508  
[2023-03-29 01:21:38] local.DEBUG: response querySubBalance  
[2023-03-29 01:21:38] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#18.18 GB#11-04-2023#0#0##1500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-29 01:21:38] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '18.18 GB',
  4 => '11-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1500.00',
  9 => '4G 25',
)  
[2023-03-29 01:21:38] local.DEBUG: print  before faction by provider price  
[2023-03-29 01:21:38] local.DEBUG: print  after faction by provider price  
[2023-03-29 01:21:38] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-29 01:21:38] local.DEBUG: print1  
[2023-03-29 01:21:38] local.DEBUG: print  2  
[2023-03-29 01:22:07] local.INFO: header  
[2023-03-29 01:22:07] local.CRITICAL: ****************************1  
[2023-03-29 01:22:07] local.ALERT: reach here  
[2023-03-29 01:22:07] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '16000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ستة عشر ألف  ر.ي.',
  ),
)  
[2023-03-29 01:22:07] local.ERROR: المبلغ  
[2023-03-29 01:22:07] local.ERROR: 16,000.00  
[2023-03-29 01:22:07] local.ERROR: مبلغ وقدرة  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 3  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 3  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 3  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 3  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 3  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 3  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 3  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 3  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 3  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 2  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 2  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 2  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 2  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 2  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 2  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 2  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 1  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 1  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 1  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 1  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 1  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 2  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 10013  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 10013  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 10013  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 10013  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 1  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 3  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 3  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 40  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 40  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 40  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 40  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.WARNING: 1  
[2023-03-29 01:22:07] local.WARNING: array (
  'ID' => 88,
  'Name' => 'فئة 130 جيجا 16000 ريال',
  'ServiceID' => 200,
  'Price' => 16000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 16000.0,
)  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 200  
[2023-03-29 01:22:07] local.ALERT: 40  
[2023-03-29 01:22:07] local.CRITICAL: ****************************2  
[2023-03-29 01:22:07] local.CRITICAL: ****************************  
[2023-03-29 01:22:07] local.CRITICAL:   
[2023-03-29 01:22:07] local.CRITICAL: ****************************  
[2023-03-29 01:22:07] local.INFO: {
  "ClientBalanceResult": "3215.1500"
}  
[2023-03-29 01:22:07] local.INFO: array (
  'ClientBalanceResult' => '3215.1500',
)  
[2023-03-29 01:22:07] local.INFO: price less than Balance  
[2023-03-29 02:04:48] local.INFO: header  
[2023-03-29 02:04:48] local.INFO: header after fliter  
[2023-03-29 02:04:48] local.INFO: Body  after fliter  
[2023-03-29 02:04:48] local.INFO: array (
)  
[2023-03-29 02:04:48] local.INFO: transaction14  
[2023-03-29 02:04:48] local.INFO: first inquery phone = 103377914  
[2023-03-29 02:04:50] local.DEBUG: response querySubBalance  
[2023-03-29 02:04:50] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00##28-03-2023#0#0##.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-29 02:04:50] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '',
  4 => '28-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 25',
)  
[2023-03-29 02:04:50] local.DEBUG: print  before faction by provider price  
[2023-03-29 02:04:50] local.DEBUG: print  after faction by provider price  
[2023-03-29 02:04:50] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-29 02:04:50] local.DEBUG: print1  
[2023-03-29 02:04:50] local.DEBUG: print  2  
[2023-03-29 02:05:39] local.INFO: header  
[2023-03-29 02:05:39] local.INFO: header after fliter  
[2023-03-29 02:05:39] local.INFO: Body  after fliter  
[2023-03-29 02:05:39] local.INFO: array (
)  
[2023-03-29 02:05:39] local.INFO: transaction14  
[2023-03-29 02:05:39] local.INFO: first inquery phone = 103377914  
[2023-03-29 02:05:42] local.DEBUG: response querySubBalance  
[2023-03-29 02:05:42] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00##28-03-2023#0#0##.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-29 02:05:42] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '',
  4 => '28-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 25',
)  
[2023-03-29 02:05:42] local.DEBUG: print  before faction by provider price  
[2023-03-29 02:05:42] local.DEBUG: print  after faction by provider price  
[2023-03-29 02:05:42] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-29 02:05:42] local.DEBUG: print1  
[2023-03-29 02:05:42] local.DEBUG: print  2  
[2023-03-29 04:00:21] local.INFO: header  
[2023-03-29 04:00:21] local.INFO: header after fliter  
[2023-03-29 04:00:21] local.INFO: Body  after fliter  
[2023-03-29 04:00:21] local.INFO: array (
)  
[2023-03-29 04:00:21] local.INFO: transaction14  
[2023-03-29 04:00:21] local.INFO: first inquery phone = 101900500  
[2023-03-29 04:00:24] local.DEBUG: response querySubBalance  
[2023-03-29 04:00:24] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#10.14 GB#13-04-2023#0#0##7501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-29 04:00:24] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '10.14 GB',
  4 => '13-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7501.00',
  9 => '4G 250',
)  
[2023-03-29 04:00:24] local.DEBUG: print  before faction by provider price  
[2023-03-29 04:00:24] local.DEBUG: print  after faction by provider price  
[2023-03-29 04:00:24] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-29 04:00:24] local.DEBUG: print1  
[2023-03-29 04:00:24] local.DEBUG: print  2  
[2023-03-29 04:02:41] local.INFO: header  
[2023-03-29 04:02:41] local.CRITICAL: ****************************1  
[2023-03-29 04:02:41] local.ALERT: reach here  
[2023-03-29 04:02:41] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '26000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ستة وعشرون ألف  ر.ي.',
  ),
)  
[2023-03-29 04:02:41] local.ERROR: المبلغ  
[2023-03-29 04:02:41] local.ERROR: 26,000.00  
[2023-03-29 04:02:41] local.ERROR: مبلغ وقدرة  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 3  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 3  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 3  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 3  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 3  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 3  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 3  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 3  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 3  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 2  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 2  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 2  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 2  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 2  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 2  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 2  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 1  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 1  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 1  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 1  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 1  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 2  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 10013  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 10013  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 10013  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 10013  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 1  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 3  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 3  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 40  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 40  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 40  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 40  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.WARNING: 1  
[2023-03-29 04:02:42] local.WARNING: array (
  'ID' => 89,
  'Name' => 'فئة 250 جيجا 26000 ريال',
  'ServiceID' => 200,
  'Price' => 26000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 26000.0,
)  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 200  
[2023-03-29 04:02:42] local.ALERT: 40  
[2023-03-29 04:02:42] local.CRITICAL: ****************************2  
[2023-03-29 04:02:42] local.CRITICAL: ****************************  
[2023-03-29 04:02:42] local.CRITICAL:   
[2023-03-29 04:02:42] local.CRITICAL: ****************************  
[2023-03-29 04:02:42] local.INFO: {
  "ClientBalanceResult": "37898.3500"
}  
[2023-03-29 04:02:42] local.INFO: array (
  'ClientBalanceResult' => '37898.3500',
)  
[2023-03-29 04:02:42] local.DEBUG: lattttef  
[2023-03-29 04:02:42] local.DEBUG: array (
  'ClientBalanceResult' => '37898.3500',
)  
[2023-03-29 04:02:42] local.INFO: transaction14  
[2023-03-29 04:02:42] local.INFO: first inquery phone = 101900500  
[2023-03-29 04:02:44] local.DEBUG: response querySubBalance  
[2023-03-29 04:02:44] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#10.05 GB#13-04-2023#0#0##7501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-29 04:02:44] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '10.05 GB',
  4 => '13-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '7501.00',
  9 => '4G 250',
)  
[2023-03-29 04:02:44] local.DEBUG: print  before faction by provider price  
[2023-03-29 04:02:44] local.DEBUG: print  after faction by provider price  
[2023-03-29 04:02:44] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-29 04:02:44] local.DEBUG: print1  
[2023-03-29 04:02:44] local.DEBUG: print  2  
[2023-03-29 04:02:44] local.INFO: transaction1  
[2023-03-29 04:02:44] local.INFO: transaction2  
[2023-03-29 04:02:44] local.INFO: transaction3  
[2023-03-29 04:02:44] local.INFO: transaction4  
[2023-03-29 04:02:44] local.INFO: transaction4  
[2023-03-29 04:02:44] local.INFO: transaction5  
[2023-03-29 04:02:44] local.INFO: transaction6  
[2023-03-29 04:02:44] local.INFO: transaction7  
[2023-03-29 04:02:44] local.DEBUG: array (
  'AMT' => 26000.0,
  'CType' => 0,
  'FID' => 89,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '101900500',
  'State' => 0,
  'lateflog' => '587932',
)  
[2023-03-29 04:02:44] local.INFO: transaction8  
[2023-03-29 04:02:45] local.INFO: transaction9  
[2023-03-29 04:02:45] local.INFO: transaction10  
[2023-03-29 04:02:45] local.INFO: transaction11  
[2023-03-29 04:02:45] local.INFO: 12  
[2023-03-29 04:02:45] local.INFO: transaction13  
[2023-03-29 04:02:45] local.INFO: transaction14  
[2023-03-29 04:02:45] local.INFO: transaction19  
[2023-03-29 04:02:45] local.INFO: transaction15  
[2023-03-29 04:02:45] local.INFO: transaction16  
[2023-03-29 04:02:45] local.INFO: 98#101900500#26000.00#0  
[2023-03-29 04:02:56] local.INFO: transaction18  
[2023-03-29 04:02:56] local.INFO: array (
  0 => 'OK',
  1 => '2,862,518.83',
  2 => 'NONE',
  3 => '61545794',
  4 => '26,000.00',
)  
[2023-03-29 04:09:01] local.INFO: header  
[2023-03-29 04:09:01] local.CRITICAL: ****************************1  
[2023-03-29 04:09:01] local.ALERT: reach here  
[2023-03-29 04:09:01] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-29 04:09:01] local.ERROR: المبلغ  
[2023-03-29 04:09:01] local.ERROR: 4,000.00  
[2023-03-29 04:09:01] local.ERROR: مبلغ وقدرة  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 3  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 3  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 3  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 3  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 3  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 3  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 3  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 3  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 3  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 2  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 2  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 2  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 2  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 2  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 2  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 2  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 1  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 1  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 1  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 1  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 1  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 2  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 10013  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 10013  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 10013  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 10013  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 1  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 3  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 3  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 40  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 40  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 40  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 40  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.WARNING: 1  
[2023-03-29 04:09:01] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 200  
[2023-03-29 04:09:01] local.ALERT: 40  
[2023-03-29 04:09:01] local.CRITICAL: ****************************2  
[2023-03-29 04:09:01] local.CRITICAL: ****************************  
[2023-03-29 04:09:01] local.CRITICAL:   
[2023-03-29 04:09:01] local.CRITICAL: ****************************  
[2023-03-29 04:09:01] local.INFO: {
  "ClientBalanceResult": "-3926836.1390"
}  
[2023-03-29 04:09:01] local.INFO: array (
  'ClientBalanceResult' => '-3926836.1390',
)  
[2023-03-29 04:09:01] local.INFO: price less than Balance  
[2023-03-29 04:09:06] local.INFO: header  
[2023-03-29 04:09:06] local.INFO: header after fliter  
[2023-03-29 04:09:06] local.INFO: Body  after fliter  
[2023-03-29 04:09:06] local.INFO: array (
)  
[2023-03-29 04:09:06] local.INFO: transaction14  
[2023-03-29 04:09:06] local.INFO: first inquery phone = 103330445  
[2023-03-29 04:09:11] local.DEBUG: response querySubBalance  
[2023-03-29 04:09:11] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#8.55 MB#20-04-2023#0#0##3859.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-29 04:09:11] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '8.55 MB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '3859.00',
  9 => '4G 25',
)  
[2023-03-29 04:09:11] local.DEBUG: print  before faction by provider price  
[2023-03-29 04:09:11] local.DEBUG: print  after faction by provider price  
[2023-03-29 04:09:11] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-29 04:09:11] local.DEBUG: print1  
[2023-03-29 04:09:11] local.DEBUG: print  2  
[2023-03-29 04:46:23] local.INFO: header  
[2023-03-29 04:46:23] local.INFO: header after fliter  
[2023-03-29 04:46:23] local.INFO: Body  after fliter  
[2023-03-29 04:46:23] local.INFO: array (
)  
[2023-03-29 04:46:23] local.INFO: transaction14  
[2023-03-29 04:46:23] local.INFO: first inquery phone = 103330445  
[2023-03-29 04:46:26] local.DEBUG: response querySubBalance  
[2023-03-29 04:46:26] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#8.55 MB#20-04-2023#0#0##3859.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-29 04:46:26] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '8.55 MB',
  4 => '20-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '3859.00',
  9 => '4G 25',
)  
[2023-03-29 04:46:26] local.DEBUG: print  before faction by provider price  
[2023-03-29 04:46:26] local.DEBUG: print  after faction by provider price  
[2023-03-29 04:46:26] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-29 04:46:26] local.DEBUG: print1  
[2023-03-29 04:46:26] local.DEBUG: print  2  
[2023-03-29 04:46:39] local.INFO: header  
[2023-03-29 04:46:39] local.CRITICAL: ****************************1  
[2023-03-29 04:46:39] local.ALERT: reach here  
[2023-03-29 04:46:39] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-29 04:46:39] local.ERROR: المبلغ  
[2023-03-29 04:46:39] local.ERROR: 4,000.00  
[2023-03-29 04:46:39] local.ERROR: مبلغ وقدرة  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 3  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 3  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 3  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 3  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 3  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 3  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 3  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 3  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 3  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 2  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 2  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 2  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 2  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 2  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 2  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 2  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 1  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 1  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 1  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 1  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 1  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 2  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 10013  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 10013  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 10013  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 10013  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 1  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 3  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 3  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 40  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 40  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 40  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 40  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.WARNING: 1  
[2023-03-29 04:46:39] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 200  
[2023-03-29 04:46:39] local.ALERT: 40  
[2023-03-29 04:46:39] local.CRITICAL: ****************************2  
[2023-03-29 04:46:39] local.CRITICAL: ****************************  
[2023-03-29 04:46:39] local.CRITICAL:   
[2023-03-29 04:46:39] local.CRITICAL: ****************************  
[2023-03-29 04:46:39] local.INFO: {
  "ClientBalanceResult": "-3926836.1390"
}  
[2023-03-29 04:46:39] local.INFO: array (
  'ClientBalanceResult' => '-3926836.1390',
)  
[2023-03-29 04:46:39] local.INFO: price less than Balance  
[2023-03-29 14:48:00] local.INFO: header  
[2023-03-29 14:48:00] local.INFO: header after fliter  
[2023-03-29 14:48:00] local.INFO: Body  after fliter  
[2023-03-29 14:48:00] local.INFO: array (
)  
[2023-03-29 14:48:00] local.INFO: transaction14  
[2023-03-29 14:48:00] local.INFO: first inquery phone = 103366225  
[2023-03-29 14:48:03] local.DEBUG: response querySubBalance  
[2023-03-29 14:48:03] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#1.65 GB#04-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-29 14:48:03] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '1.65 GB',
  4 => '04-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-29 14:48:03] local.DEBUG: print  before faction by provider price  
[2023-03-29 14:48:03] local.DEBUG: print  after faction by provider price  
[2023-03-29 14:48:03] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-29 14:48:03] local.DEBUG: print1  
[2023-03-29 14:48:03] local.DEBUG: print  2  
[2023-03-29 14:49:09] local.INFO: header  
[2023-03-29 14:49:09] local.CRITICAL: ****************************1  
[2023-03-29 14:49:09] local.ALERT: reach here  
[2023-03-29 14:49:09] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2023-03-29 14:49:09] local.ERROR: المبلغ  
[2023-03-29 14:49:09] local.ERROR: 2,400.00  
[2023-03-29 14:49:09] local.ERROR: مبلغ وقدرة  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 3  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 3  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 3  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 3  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 3  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 3  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 3  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 3  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 3  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 2  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 2  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 2  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 2  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 2  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 2  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 2  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 1  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 1  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 1  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 1  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 1  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 2  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 10013  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 10013  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 10013  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 10013  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 1  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 3  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 3  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 40  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 40  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 40  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 40  
[2023-03-29 14:49:09] local.WARNING: 1  
[2023-03-29 14:49:09] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 200  
[2023-03-29 14:49:09] local.ALERT: 40  
[2023-03-29 14:49:09] local.CRITICAL: ****************************2  
[2023-03-29 14:49:09] local.CRITICAL: ****************************  
[2023-03-29 14:49:09] local.CRITICAL:   
[2023-03-29 14:49:09] local.CRITICAL: ****************************  
[2023-03-29 14:49:09] local.INFO: {
  "ClientBalanceResult": "9861.5500"
}  
[2023-03-29 14:49:09] local.INFO: array (
  'ClientBalanceResult' => '9861.5500',
)  
[2023-03-29 14:49:09] local.DEBUG: lattttef  
[2023-03-29 14:49:09] local.DEBUG: array (
  'ClientBalanceResult' => '9861.5500',
)  
[2023-03-29 14:49:09] local.INFO: transaction14  
[2023-03-29 14:49:09] local.INFO: first inquery phone = 103366225  
[2023-03-29 14:49:11] local.DEBUG: response querySubBalance  
[2023-03-29 14:49:11] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#1.65 GB#04-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-29 14:49:11] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '1.65 GB',
  4 => '04-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-29 14:49:11] local.DEBUG: print  before faction by provider price  
[2023-03-29 14:49:11] local.DEBUG: print  after faction by provider price  
[2023-03-29 14:49:11] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-29 14:49:11] local.DEBUG: print1  
[2023-03-29 14:49:11] local.DEBUG: print  2  
[2023-03-29 14:49:11] local.INFO: transaction1  
[2023-03-29 14:49:11] local.INFO: transaction2  
[2023-03-29 14:49:11] local.INFO: transaction3  
[2023-03-29 14:49:11] local.INFO: transaction4  
[2023-03-29 14:49:11] local.INFO: transaction4  
[2023-03-29 14:49:11] local.INFO: transaction5  
[2023-03-29 14:49:11] local.INFO: transaction6  
[2023-03-29 14:49:11] local.INFO: transaction7  
[2023-03-29 14:49:11] local.DEBUG: array (
  'AMT' => 2400.0,
  'CType' => 0,
  'FID' => 85,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103366225',
  'State' => 0,
  'lateflog' => '584503',
)  
[2023-03-29 14:49:11] local.INFO: transaction8  
[2023-03-29 14:49:11] local.INFO: transaction9  
[2023-03-29 14:49:11] local.INFO: transaction10  
[2023-03-29 14:49:11] local.INFO: transaction11  
[2023-03-29 14:49:11] local.INFO: 12  
[2023-03-29 14:49:11] local.INFO: transaction13  
[2023-03-29 14:49:11] local.INFO: transaction14  
[2023-03-29 14:49:11] local.INFO: transaction19  
[2023-03-29 14:49:11] local.INFO: transaction15  
[2023-03-29 14:49:11] local.INFO: transaction16  
[2023-03-29 14:49:11] local.INFO: 98#103366225#2400.00#0  
[2023-03-29 14:49:19] local.INFO: transaction18  
[2023-03-29 14:49:19] local.INFO: array (
  0 => 'OK',
  1 => '2,733,649.83',
  2 => 'NONE',
  3 => '61556749',
  4 => '2,400.00',
)  
[2023-03-29 19:30:05] local.INFO: header  
[2023-03-29 19:30:05] local.CRITICAL: ****************************1  
[2023-03-29 19:30:05] local.ALERT: reach here  
[2023-03-29 19:30:05] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '12000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'إثنا عشر ألف  ر.ي.',
  ),
)  
[2023-03-29 19:30:05] local.ERROR: المبلغ  
[2023-03-29 19:30:05] local.ERROR: 12,000.00  
[2023-03-29 19:30:05] local.ERROR: مبلغ وقدرة  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 3  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 3  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 3  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 3  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 3  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 3  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 3  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 3  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 3  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 2  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 2  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 2  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 2  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 2  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 2  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 2  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 1  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 1  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 1  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 1  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 1  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 2  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 10013  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 10013  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 10013  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 10013  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 1  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 3  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 3  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.WARNING: 1  
[2023-03-29 19:30:06] local.WARNING: array (
  'ID' => 84,
  'Name' => 'باقة 80جيجا 12000 ريال',
  'ServiceID' => 40,
  'Price' => 12000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '12000',
  'PersonnalPrice' => 12000.0,
)  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 200  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 200  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 200  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 200  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 200  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 200  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.ALERT: 40  
[2023-03-29 19:30:06] local.CRITICAL: ****************************2  
[2023-03-29 19:30:06] local.CRITICAL: ****************************  
[2023-03-29 19:30:06] local.CRITICAL:   
[2023-03-29 19:30:06] local.CRITICAL: ****************************  
[2023-03-29 19:30:07] local.INFO: {
  "ClientBalanceResult": "494868.4767"
}  
[2023-03-29 19:30:07] local.INFO: array (
  'ClientBalanceResult' => '494868.4767',
)  
[2023-03-29 19:30:07] local.DEBUG: lattttef  
[2023-03-29 19:30:07] local.DEBUG: array (
  'ClientBalanceResult' => '494868.4767',
)  
[2023-03-29 19:30:07] local.INFO: transaction1  
[2023-03-29 19:30:07] local.INFO: transaction2  
[2023-03-29 19:30:07] local.INFO: transaction3  
[2023-03-29 19:30:07] local.INFO: transaction4  
[2023-03-29 19:30:07] local.INFO: transaction4  
[2023-03-29 19:30:07] local.INFO: transaction5  
[2023-03-29 19:30:07] local.INFO: transaction6  
[2023-03-29 19:30:07] local.INFO: transaction7  
[2023-03-29 19:30:07] local.DEBUG: array (
  'AMT' => 12000.0,
  'CType' => 0,
  'FID' => 84,
  'LType' => '1',
  'SID' => 40,
  'SNO' => '798718869',
  'State' => 0,
  'lateflog' => '70760',
)  
[2023-03-29 19:30:07] local.INFO: transaction8  
[2023-03-29 19:30:07] local.INFO: transaction9  
[2023-03-29 19:30:07] local.INFO: transaction10  
[2023-03-29 19:30:07] local.INFO: transaction11  
[2023-03-29 19:30:07] local.INFO: 12  
[2023-03-29 19:30:07] local.INFO: transaction13  
[2023-03-29 19:30:07] local.INFO: transaction14  
[2023-03-29 19:30:07] local.INFO: transaction19  
[2023-03-29 19:30:07] local.INFO: transaction15  
[2023-03-29 19:30:15] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '12060',
  'remainAmount' => 25280747,
  'mallrem' => -24719253,
  'transid' => '4422768',
  'ref_id' => 26285453,
)  
[2023-03-29 23:16:25] local.INFO: header  
[2023-03-29 23:16:26] local.CRITICAL: ****************************1  
[2023-03-29 23:16:26] local.ALERT: reach here  
[2023-03-29 23:16:27] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-29 23:16:27] local.ERROR: المبلغ  
[2023-03-29 23:16:27] local.ERROR: 4,000.00  
[2023-03-29 23:16:27] local.ERROR: مبلغ وقدرة  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 3  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 3  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 3  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 3  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 3  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 3  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 3  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 3  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 3  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 2  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 2  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 2  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 2  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 2  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 2  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 2  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 1  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 1  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 1  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 1  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 1  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 2  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 10013  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 10013  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 10013  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 10013  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 1  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 3  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 3  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 40  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 40  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 40  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 40  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.WARNING: 1  
[2023-03-29 23:16:27] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 200  
[2023-03-29 23:16:27] local.ALERT: 40  
[2023-03-29 23:16:27] local.CRITICAL: ****************************2  
[2023-03-29 23:16:27] local.CRITICAL: ****************************  
[2023-03-29 23:16:27] local.CRITICAL:   
[2023-03-29 23:16:27] local.CRITICAL: ****************************  
[2023-03-29 23:16:27] local.INFO: {
  "ClientBalanceResult": "42179.2500"
}  
[2023-03-29 23:16:28] local.INFO: array (
  'ClientBalanceResult' => '42179.2500',
)  
[2023-03-29 23:16:28] local.DEBUG: lattttef  
[2023-03-29 23:16:28] local.DEBUG: array (
  'ClientBalanceResult' => '42179.2500',
)  
[2023-03-29 23:16:28] local.INFO: transaction14  
[2023-03-29 23:16:28] local.INFO: first inquery phone = 103344681  
[2023-03-29 23:16:32] local.DEBUG: response querySubBalance  
[2023-03-29 23:16:32] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#0.00 B#27-04-2023#0#0##3000.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-29 23:16:32] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '0.00 B',
  4 => '27-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '3000.00',
  9 => '4G 25',
)  
[2023-03-29 23:16:32] local.DEBUG: print  before faction by provider price  
[2023-03-29 23:16:32] local.DEBUG: print  after faction by provider price  
[2023-03-29 23:16:32] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-29 23:16:32] local.DEBUG: print1  
[2023-03-29 23:16:32] local.DEBUG: print  2  
[2023-03-29 23:16:32] local.INFO: transaction1  
[2023-03-29 23:16:32] local.INFO: transaction2  
[2023-03-29 23:16:32] local.INFO: transaction3  
[2023-03-29 23:16:32] local.INFO: transaction4  
[2023-03-29 23:16:32] local.INFO: transaction4  
[2023-03-29 23:16:32] local.INFO: transaction5  
[2023-03-29 23:16:32] local.INFO: transaction6  
[2023-03-29 23:16:32] local.INFO: transaction7  
[2023-03-29 23:16:32] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103344681',
  'State' => 0,
  'lateflog' => '588030',
)  
[2023-03-29 23:16:32] local.INFO: transaction8  
[2023-03-29 23:16:32] local.INFO: transaction9  
[2023-03-29 23:16:32] local.INFO: transaction10  
[2023-03-29 23:16:32] local.INFO: transaction11  
[2023-03-29 23:16:32] local.INFO: 12  
[2023-03-29 23:16:32] local.INFO: transaction13  
[2023-03-29 23:16:32] local.INFO: transaction14  
[2023-03-29 23:16:32] local.INFO: transaction19  
[2023-03-29 23:16:32] local.INFO: transaction15  
[2023-03-29 23:16:32] local.INFO: transaction16  
[2023-03-29 23:16:32] local.INFO: 98#103344681#4000.00#0  
[2023-03-29 23:16:50] local.INFO: transaction18  
[2023-03-29 23:16:50] local.INFO: array (
  0 => 'OK',
  1 => '1,640,754.83',
  2 => 'NONE',
  3 => '61617916',
  4 => '4,000.00',
)  
