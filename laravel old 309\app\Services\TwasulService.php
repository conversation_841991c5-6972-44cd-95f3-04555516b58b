<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Helpers\EncreptData;
use App\Jobs\CheckTopup;
use App\Jobs\ProcessTopupPlanResubscriber;
use App\Models\Journal;
use DateTime;
use Illuminate\Support\Env;
use App\Repositories\ReverseUnderRepository;
use Illuminate\Validation\ValidationException;
use Sebastian<PERSON><PERSON><PERSON>n\Invoker\TimeoutException;
use App\Models\Faction;
use stdClass;
use App\Models\JournalEntry;

// TODO use inteface for topup gateway
class TwasulService
{
    private $topup;

    private $Journal;
    public $returnResponse = 1;
    public function __construct($topup, $Journal)
    {
        Log::info("transaction14");

        $this->topup = $topup;
        $this->Journal = $Journal;
    }
    public function encoderPassAndToken($myRefID, $phone)
    {
        $Username =  'nawafdhsouth';
        $Password =  'lggu2df';

        $encodePassword = md5($Password);
        $encodeToken = md5($encodePassword . $myRefID . $Username . $phone);

        return $encodeToken;
    } 
 
    public static function confirmProcess($topup, $url)
    {
        $userSanaOrAden = in_array($topup->ServiceID, [40, 20024]) ? 2 : 1;   // 1 sana   2 aden
        Log::info("transaction15");
        $response = Http::asJson()
            ->timeout(60) // 1 Min
            ->post($url);
            Log::info($response->json());

        return  $response;
    }


    public function confirm()
    {
        Log::info("transaction19");

        $response = null;
        try {
            $phone =  $this->topup->SubscriberNumber;
            $amount = $this->topup->ProviderPrice;
            $myRefID = $this->topup->EntryID;

            $faction=Faction::where('id',$this->topup->FactionID)->first();
            if($faction){
                $codeBundle=$faction->ProviderCode;

            }
            else{
                $codeBundle=null;
            }


            $url = "https://wkala.yemoney.net/api/yr/" .
                "adenet?action=bill" .
                "&userid=" . '4936' .
                "&mobile=" .  $phone .
                "&transid=" .  $myRefID .
                "&token=" . $this->encoderPassAndToken($myRefID, $phone) .
                "&num=" . $codeBundle  ;  // here code bundle to provider
                 
            $response = self::confirmProcess($this->topup,  $url);
           
            $this->topup->ExecutionPeroid = date('s');
           

            if (!($response && $response->ok())) {
                $this->topup->ResponseInfo = 'timeout';
                $this->topup->ResponseStatus = 0;
                $this->topup->Status = 0;
                $this->topup->Debited = 0;
                $this->topup->Note = "العملية فاشلة";
                $this->topup->FailedReason = "فشل في اجراء العملية";
                $this->topup->ProviderRM = "فشل في اجراء العملية";
                $this->topup->StateClass = "العملية فاشلة";
                $this->topup->AccountNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->AdminNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->ResponseTime = null;
                $this->topup->Responded = 0;
                $this->topup->save();

                Log::info("transaction20");
                throw ValidationException::withMessages([
                    'exception' => ['TimeOut'],
                ]);
            }
          
            if ($response && $response->json('resultCode') != "0") {
                $this->topup->RefNumber = intval($response->json('resultCode'));
                $this->topup->ResponseReference = intval($response->json('resultCode'));
                $this->topup->ResponseInfo =json_encode([$response->json()]);
                $this->topup->ResponseStatus = 0;
                $this->topup->Status = 0;
                $this->topup->Debited = 0;
                $this->topup->Note = "العملية فاشلة";
                $this->topup->FailedReason = "فشل في اجراء العملية";
                $this->topup->ProviderRM = $response->json('resultDesc');                $this->topup->StateClass = "العملية فاشلة";
                $this->topup->AccountNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->AdminNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->ResponseTime = date('Y-m-d H:i:s');
                $this->topup->Responded = 1;
                $this->topup->save();
                throw new Exception('assertion failed');
            }



            if ($response && $response->json('resultCode') == "0" && Str::contains($response->body(), 'under')) {

                $this->topup->ResponseInfo = json_encode([$response->json()]);
                // $this->topup->ResponseStatus = $response->status();
                $this->topup->Status = 2;
                $this->topup->Note = "العملية معلقة";
                $this->topup->ProviderRM = "العملية معلقة";
                $this->topup->StateClass = "العملية معلقة";
                $this->topup->Responded = 1;

                $this->topup->ResponseTime = date('Y-m-d H:i:s');

                $this->topup->save();
                throw ValidationException::withMessages([
                    'exception' => ['response check'],
                ]);


                // return null;
            }

            $this->topup->Status = 1;
            $this->topup->BillState = 0;
            $this->topup->RefNumber = $response->json('ref_id');
            $this->topup->ResponseInfo =json_encode([$response->json()]);
            $this->topup->ResponseStatus = 1;
            $this->topup->Note = "العملية ناجحة";
            $this->topup->StateClass = "جاهز";
            $this->topup->ProviderRM = "جاهز";
            $this->topup->AccountNote = "تمت العملية بنجاح";
            $this->topup->AdminNote = "تمت العملية بنجاح";
            $this->topup->Responded = 1;
            $this->topup->ResponseReference = $response->json('ref_id');

            $this->topup->save();
            return  1;
        } catch (\Throwable $exception) {

            Log::debug($this->topup);

            if ($exception instanceof ValidationException) {

                Log::debug($exception->getMessage());

                if (str_contains($exception->getMessage(), 'response check')) {
                   
                    CheckTopup::dispatch($this->topup, $this->Journal)->delay(now()->addSeconds(10));
                    return  1;
                }
                Log::debug("validateee");
                Log::debug($exception->getMessage());
            } else {
                $this->topup->ResponseInfo = json_encode(["provider" => $response != null ? $response->body() : '', "twasul" => $exception->getMessage()]);
                $this->topup->save();
            }


            $this->Journal->Status = 0;
            $this->Journal->Debited = 0;
            $this->Journal->save();
            return  0;
        }
    }

    public function check()
    { 
        Log::info("transaction19");
 
        $response = null;
        try {
            $phone =  $this->topup->SubscriberNumber;
            $amount = $this->topup->ProviderPrice;
            $myRefID = $this->topup->EntryID;
           
            $url = "https://wkala.yemoney.net/api/yr/" .
            "info?" .
            "&userid=".'4936' .
            "&mobile=".  $phone .
            "&transid=".  $myRefID . 
            "&token=". $this->encoderPassAndToken($myRefID , $phone) .
            "&action=staus";
 
             
            $response = self::confirmProcess($this->topup,  $url);
            $this->topup->ExecutionPeroid = date('s');

            if (!($response && $response->ok())) {
                $this->topup->ResponseInfo = 'timeout';
                $this->topup->ResponseStatus = 0;
                $this->topup->Status = 0;
                $this->topup->Debited = 0;
                $this->topup->Note = "العملية فاشلة";
                $this->topup->FailedReason = "فشل في اجراء العملية";
                $this->topup->ProviderRM = "فشل في اجراء العملية";
                $this->topup->StateClass = "العملية فاشلة";
                $this->topup->AccountNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->AdminNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->ResponseTime = null;
                $this->topup->Responded = 0;
                $this->topup->save(); 
                Log::info("transaction20");
                throw ValidationException::withMessages([
                    'exception' => ['TimeOut'],
                ]);
            } 

            if ($response && $response->json("isBan") == "1") { 
                $this->topup->ResponseInfo =json_encode([$response->json()]);
                $this->topup->ResponseStatus = 0;
                $this->topup->Status = 0;
                $this->topup->Debited = 0;
                $this->topup->Note = "العملية فاشلة";
                $this->topup->FailedReason = "فشل في اجراء العملية";
                $this->topup->ProviderRM = "فشل في اجراء العملية";
                $this->topup->StateClass = "العملية فاشلة";
                $this->topup->AccountNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->AdminNote = "فشل في اجراء العملية تم الغاء العملية";
                $this->topup->ResponseTime = date('Y-m-d H:i:s');
                $this->topup->Responded = 1;
                $this->topup->save();
                throw new Exception('assertion failed');
            }
          
 
             if ($response && $response->json('resultCode') == "0" && Str::contains($response->body(), 'under')) {
 
                throw ValidationException::withMessages([
                    'exception' => ['response check'],
                ]); 
            }  

            if ($response && $response->json("isDone") == "1") {
               
            $this->topup->Status = 1;
            $this->topup->BillState = 0;
            $this->topup->RefNumber =$response->json('sequenceId');
            $this->topup->ResponseInfo =json_encode([$response->json()]);
            $this->topup->ResponseStatus = 1;
            $this->topup->Note = "العملية ناجحة";
            $this->topup->StateClass = "جاهز";
            $this->topup->ProviderRM = "جاهز";
            $this->topup->AccountNote = "تمت العملية بنجاح";
            $this->topup->AdminNote = "تمت العملية بنجاح";
            $this->topup->Responded = 1;
            $this->topup->ResponseReference = $response->json('sequenceId');
            if((number_format(floatval($response->json('price')),2))!=(number_format(floatval($faction->ProviderPrice),2))){
                Log::debug("journalEntry provider after response");
                $journalEntry=JournalEntry::where('ParentID',$this->Journal->ID)->where('AccountID',316581)->first();
                if($journalEntry){
                    Log::debug("journalEntry after response true");
                    $journalEntry->DCAmount=(number_format(floatval($response->json('price')),2));
                    $journalEntry->Amount=(number_format(floatval($response->json('price')),2));
                    $journalEntry->save();
    
                }
                $journalEntry=JournalEntry::where('ParentID',$this->Journal->ID)->where('AccountID',222839)->first();
                Log::debug("journalEntry commision after response");
                if($journalEntry){
                    Log::debug("journalEntry commision after response true");
                    $journalEntry->DCAmount=(number_format(floatval($this->topup->TotalAmount),2)) -  (number_format(floatval($response->json('price')),2))  ;
                    $journalEntry->Amount= (number_format(floatval($this->topup->TotalAmount),2)) -  (number_format(floatval($response->json('price')),2)) ;
                    $journalEntry->save();
    
                }
                Log::debug("update in topup UnitCost");
                $this->topup->UnitCost=((number_format(floatval($response->json('price')),2)))/$faction->Quantity;
                Log::debug("update in topup CostAmount");
                $this->topup->CostAmount = (number_format(floatval($response->json('price')),2));
                Log::debug("update in topup Profits");
                $this->topup->Profits = ($this->topup->TotalAmount)-((number_format(floatval($response->json('price')),2)));
                Log::debug("update in topup UnitCost");
            }
            $this->topup->save();
            return  1; 
                
            }

        } catch (\Throwable $exception) {
            
            if ($exception instanceof ValidationException) {

                Log::debug($exception->getMessage());

                if (str_contains($exception->getMessage(), 'response check')) {
                    CheckTopup::dispatch($this->topup, $this->Journal)->delay(now()->addSeconds(10));
                    return  1;
                }
                Log::debug("validateee");
                Log::debug($exception->getMessage());
            } else {
                $this->topup->ResponseInfo = json_encode(["provider" => $response->json() != null ? json_encode($response->json()) : '', "NetCoin" => $exception->getMessage()]);
                $this->topup->save();
            }


            $this->Journal->Status = 0;
            $this->Journal->Debited = 0;
            $this->Journal->save();
            return  0;
        }
    }
}
