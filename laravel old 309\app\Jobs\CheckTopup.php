<?php

namespace App\Jobs;

use App\Services\NetCoinService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\TwasulService;

class CheckTopup implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $topup;
    private $journal;
    public function __construct($topupParam,$journalParam)
    {
        $this->topup=$topupParam;
        $this->journal=$journalParam;
        $this->queue = 'check-topup';
        $this->connection = 'database';
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if ($this->topup->ServiceID == 40) {
            $confimTopup = new TwasulService($this->topup, $this->journal);
        } else {
            $confimTopup = new NetCoinService($this->topup, $this->journal);
        } 
        
        $confimTopup->check();
       // $confimTopup=new NetCoinService($this->topup,$this->journal);
       // $confimTopup->check();
    }
}
