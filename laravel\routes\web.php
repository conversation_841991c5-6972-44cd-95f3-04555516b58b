#!/bin/bash

# تشغيل خادم Laravel على المنافذ المحددة
echo "Starting Laravel server on ports 8081 and 4433..."

# تشغيل الخادم على المنفذ 8081 في الخلفية
php artisan serve --host=0.0.0.0 --port=8081 &
SERVER_PID_8081=$!

# تشغيل الخادم على المنفذ 4433 في الخلفية  
php artisan serve --host=0.0.0.0 --port=4433 &
SERVER_PID_4433=$!

echo "Servers started:"
echo "HTTP: http://localhost:8081"
echo "HTTPS: https://localhost:4433"
echo "PIDs: $SERVER_PID_8081, $SERVER_PID_4433"

# حفظ PIDs للإيقاف لاحق
echo $SERVER_PID_8081 > server_8081.pid
echo $SERVER_PID_4433 > server_4433.pid

# انتظار إشارة الإيقاف
trap 'kill $SERVER_PID_8081 $SERVER_PID_4433; rm -f server_*.pid; exit' SIGINT SIGTERM

wait


