[2025-07-03 01:53:37] local.INFO: header  
[2025-07-03 01:53:37] local.INFO: header after fliter  
[2025-07-03 01:53:37] local.INFO: Body  after fliter  
[2025-07-03 01:53:37] local.INFO: array (
  'Amount' => 550.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-03 01:53:37] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-03 01:53:37] local.ERROR: الكمية  
[2025-07-03 01:53:37] local.ERROR: سعر الوحدة  
[2025-07-03 01:53:37] local.ERROR: المبلغ  
[2025-07-03 01:53:37] local.ERROR: 1.210  
[2025-07-03 01:53:37] local.INFO: 454.55  
[2025-07-03 01:53:37] local.ERROR: مبلغ وقدرة  
[2025-07-03 01:53:37] local.INFO: الكمية  
[2025-07-03 01:53:37] local.INFO: سعر الوحدة  
[2025-07-03 01:53:37] local.INFO: المبلغ  
[2025-07-03 01:53:37] local.INFO: مبلغ وقدرة  
[2025-07-03 01:53:37] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-03 01:53:37] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '454.55',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '550.00',
  ),
)  
[2025-07-03 01:53:37] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '454.55',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '550.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-03 01:53:41] local.INFO: header  
[2025-07-03 01:53:41] local.CRITICAL: ****************************1  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 3  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 3  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 3  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 3  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 3  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 3  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 3  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 3  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 3  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 1  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 1  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 1  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 1  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 1  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 10013  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 10013  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 10013  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 10013  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 1  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 3  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 3  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 40  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 40  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 40  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 40  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 200  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 200  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 200  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 200  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 200  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 200  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 40  
[2025-07-03 01:53:41] local.ALERT: 2  
[2025-07-03 01:53:41] local.ALERT: 10013  
[2025-07-03 01:53:41] local.CRITICAL: ****************************  
[2025-07-03 01:53:41] local.CRITICAL:   
[2025-07-03 01:53:41] local.CRITICAL: ****************************  
[2025-07-03 01:53:41] local.CRITICAL: ****************************2  
[2025-07-03 01:53:41] local.INFO: checkUser 1  
[2025-07-03 01:53:42] local.INFO: {
  "ClientBalanceResult": "25194.8081"
}  
[2025-07-03 01:53:42] local.INFO: checkUser 2  
[2025-07-03 01:53:42] local.INFO: array (
  'ClientBalanceResult' => '25194.8081',
)  
[2025-07-03 01:53:42] local.INFO: 550  
[2025-07-03 01:53:42] local.ALERT: reach here  
[2025-07-03 01:53:42] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-03 01:53:42] local.ERROR: الكمية  
[2025-07-03 01:53:42] local.ERROR: سعر الوحدة  
[2025-07-03 01:53:42] local.ERROR: المبلغ  
[2025-07-03 01:53:42] local.ERROR: 1.210  
[2025-07-03 01:53:42] local.INFO: 1.210  
[2025-07-03 01:53:42] local.INFO: 454.545  
[2025-07-03 01:53:42] local.ERROR: 550.00  
[2025-07-03 01:53:42] local.INFO: checkUser 3  
[2025-07-03 01:53:42] local.INFO: checkUser 3#  
[2025-07-03 01:53:42] local.INFO: checkUser 4  
[2025-07-03 01:53:42] local.INFO: checkUser 4#  
[2025-07-03 01:53:42] local.INFO: checkUser 5  
[2025-07-03 01:53:42] local.DEBUG: lattttef  
[2025-07-03 01:53:42] local.DEBUG: array (
  'ClientBalanceResult' => '25194.8081',
)  
[2025-07-03 01:53:42] local.INFO: transaction1  
[2025-07-03 01:53:42] local.INFO: transaction2  
[2025-07-03 01:53:42] local.INFO: transaction3  
[2025-07-03 01:53:42] local.INFO: transaction4  
[2025-07-03 01:53:42] local.INFO: transaction4  
[2025-07-03 01:53:42] local.INFO: transaction5  
[2025-07-03 01:53:42] local.INFO: transaction6  
[2025-07-03 01:53:42] local.INFO: transaction7  
[2025-07-03 01:53:42] local.DEBUG: array (
  'AMT' => 550.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '454.545',
)  
[2025-07-03 01:53:42] local.INFO: transaction8  
[2025-07-03 01:53:42] local.INFO: transaction9  
[2025-07-03 01:53:42] local.INFO: 550  
[2025-07-03 01:53:42] local.INFO: transaction10  
[2025-07-03 01:53:42] local.INFO: 454.545  
[2025-07-03 01:53:42] local.INFO: 550.00  
[2025-07-03 01:53:43] local.INFO: transaction11  
[2025-07-03 01:53:43] local.INFO: 121  
[2025-07-03 01:53:43] local.INFO: topup1550.00  
[2025-07-03 01:53:43] local.INFO: topup21.210  
[2025-07-03 01:53:43] local.INFO: topup3550.00  
[2025-07-03 01:53:43] local.INFO: topup4550.00  
[2025-07-03 01:53:43] local.INFO: topup5550  
[2025-07-03 01:53:43] local.INFO: topup60  
[2025-07-03 01:53:43] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 550.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-03 01:53:43',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7315424,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-03 01:53:43',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '550.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '**************',
  'Quantity' => '454.545',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '550.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '550.00',
  'TotalAmount' => 550.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 550.0,
  'ExCode' => NULL,
  'TransNumber' => '**************',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#550#0',
  'ResponseTime' => '2025-07-03 01:53:43',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '43',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-03 01:53:43] local.INFO: transaction13  
[2025-07-03 01:53:43] local.INFO: transaction14  
[2025-07-03 01:53:43] local.INFO: transaction19  
[2025-07-03 01:53:43] local.INFO: transaction19#.  
[2025-07-03 01:53:43] local.INFO: transaction19#.  
[2025-07-03 01:53:43] local.INFO: transaction19#  
[2025-07-03 01:53:43] local.INFO: transaction19##  
[2025-07-03 01:53:43] local.INFO: transaction15  
[2025-07-03 01:53:44] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '550.00',
  'remainAmount' => ********,
  'mallrem' => -339857,
  'transid' => '7315424',
  'ref_id' => 92645970,
)  
[2025-07-03 02:08:34] local.INFO: header  
[2025-07-03 02:08:34] local.INFO: header after fliter  
[2025-07-03 02:08:34] local.INFO: Body  after fliter  
[2025-07-03 02:08:34] local.INFO: array (
  'Amount' => 250.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-03 02:08:35] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-03 02:08:35] local.ERROR: الكمية  
[2025-07-03 02:08:35] local.ERROR: سعر الوحدة  
[2025-07-03 02:08:35] local.ERROR: المبلغ  
[2025-07-03 02:08:35] local.ERROR: 1.210  
[2025-07-03 02:08:35] local.INFO: 206.61  
[2025-07-03 02:08:35] local.ERROR: مبلغ وقدرة  
[2025-07-03 02:08:35] local.INFO: الكمية  
[2025-07-03 02:08:35] local.INFO: سعر الوحدة  
[2025-07-03 02:08:35] local.INFO: المبلغ  
[2025-07-03 02:08:35] local.INFO: مبلغ وقدرة  
[2025-07-03 02:08:35] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-03 02:08:35] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '206.61',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '250.00',
  ),
)  
[2025-07-03 02:08:35] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '206.61',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '250.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-03 02:08:49] local.INFO: header  
[2025-07-03 02:08:49] local.INFO: header after fliter  
[2025-07-03 02:08:49] local.INFO: Body  after fliter  
[2025-07-03 02:08:49] local.INFO: array (
  'Amount' => 260.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-03 02:08:49] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-03 02:08:49] local.ERROR: الكمية  
[2025-07-03 02:08:49] local.ERROR: سعر الوحدة  
[2025-07-03 02:08:49] local.ERROR: المبلغ  
[2025-07-03 02:08:49] local.ERROR: 1.210  
[2025-07-03 02:08:49] local.INFO: 214.88  
[2025-07-03 02:08:49] local.ERROR: مبلغ وقدرة  
[2025-07-03 02:08:49] local.INFO: الكمية  
[2025-07-03 02:08:49] local.INFO: سعر الوحدة  
[2025-07-03 02:08:49] local.INFO: المبلغ  
[2025-07-03 02:08:49] local.INFO: مبلغ وقدرة  
[2025-07-03 02:08:49] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-03 02:08:49] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '214.88',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '260.00',
  ),
)  
[2025-07-03 02:08:49] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '214.88',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '260.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-03 02:08:50] local.INFO: header  
[2025-07-03 02:08:50] local.CRITICAL: ****************************1  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 3  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 3  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 3  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 3  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 3  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 3  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 3  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 3  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 3  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 1  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 1  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 1  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 1  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 1  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 10013  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 10013  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 10013  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 10013  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 1  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 3  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 3  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 40  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 40  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 40  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 40  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 200  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 200  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 200  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 200  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 200  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 200  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 40  
[2025-07-03 02:08:50] local.ALERT: 2  
[2025-07-03 02:08:50] local.ALERT: 10013  
[2025-07-03 02:08:50] local.CRITICAL: ****************************  
[2025-07-03 02:08:50] local.CRITICAL:   
[2025-07-03 02:08:50] local.CRITICAL: ****************************  
[2025-07-03 02:08:50] local.CRITICAL: ****************************2  
[2025-07-03 02:08:50] local.INFO: checkUser 1  
[2025-07-03 02:08:52] local.INFO: {
  "ClientBalanceResult": "24644.8081"
}  
[2025-07-03 02:08:52] local.INFO: checkUser 2  
[2025-07-03 02:08:52] local.INFO: array (
  'ClientBalanceResult' => '24644.8081',
)  
[2025-07-03 02:08:52] local.INFO: 260  
[2025-07-03 02:08:52] local.ALERT: reach here  
[2025-07-03 02:08:52] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-03 02:08:52] local.ERROR: الكمية  
[2025-07-03 02:08:52] local.ERROR: سعر الوحدة  
[2025-07-03 02:08:52] local.ERROR: المبلغ  
[2025-07-03 02:08:52] local.ERROR: 1.210  
[2025-07-03 02:08:52] local.INFO: 1.210  
[2025-07-03 02:08:52] local.INFO: 214.876  
[2025-07-03 02:08:52] local.ERROR: 260.00  
[2025-07-03 02:08:52] local.INFO: checkUser 3  
[2025-07-03 02:08:52] local.INFO: checkUser 3#  
[2025-07-03 02:08:52] local.INFO: checkUser 4  
[2025-07-03 02:08:52] local.INFO: checkUser 4#  
[2025-07-03 02:08:52] local.INFO: checkUser 5  
[2025-07-03 02:08:52] local.DEBUG: lattttef  
[2025-07-03 02:08:52] local.DEBUG: array (
  'ClientBalanceResult' => '24644.8081',
)  
[2025-07-03 02:08:52] local.INFO: transaction1  
[2025-07-03 02:08:52] local.INFO: transaction2  
[2025-07-03 02:08:52] local.INFO: transaction3  
[2025-07-03 02:08:52] local.INFO: transaction4  
[2025-07-03 02:08:52] local.INFO: transaction4  
[2025-07-03 02:08:52] local.INFO: transaction5  
[2025-07-03 02:08:52] local.INFO: transaction6  
[2025-07-03 02:08:52] local.INFO: transaction7  
[2025-07-03 02:08:52] local.DEBUG: array (
  'AMT' => 260.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '214.876',
)  
[2025-07-03 02:08:52] local.INFO: transaction8  
[2025-07-03 02:08:52] local.INFO: transaction9  
[2025-07-03 02:08:52] local.INFO: 260  
[2025-07-03 02:08:52] local.INFO: transaction10  
[2025-07-03 02:08:52] local.INFO: 214.876  
[2025-07-03 02:08:52] local.INFO: 260.00  
[2025-07-03 02:08:52] local.INFO: transaction11  
[2025-07-03 02:08:52] local.INFO: 121  
[2025-07-03 02:08:52] local.INFO: topup1260.00  
[2025-07-03 02:08:52] local.INFO: topup21.210  
[2025-07-03 02:08:52] local.INFO: topup3260.00  
[2025-07-03 02:08:52] local.INFO: topup4260.00  
[2025-07-03 02:08:52] local.INFO: topup5260  
[2025-07-03 02:08:52] local.INFO: topup60  
[2025-07-03 02:08:52] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 260.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-03 02:08:52',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7315425,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-03 02:08:52',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '260.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '**************',
  'Quantity' => '214.876',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '260.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '260.00',
  'TotalAmount' => 260.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '***********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 260.0,
  'ExCode' => NULL,
  'TransNumber' => '**************',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#260#0',
  'ResponseTime' => '2025-07-03 02:08:52',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '52',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-03 02:08:52] local.INFO: transaction13  
[2025-07-03 02:08:52] local.INFO: transaction14  
[2025-07-03 02:08:52] local.INFO: transaction19  
[2025-07-03 02:08:52] local.INFO: transaction19#.  
[2025-07-03 02:08:52] local.INFO: transaction19#.  
[2025-07-03 02:08:52] local.INFO: transaction19#  
[2025-07-03 02:08:52] local.INFO: transaction19##  
[2025-07-03 02:08:52] local.INFO: transaction15  
[2025-07-03 02:08:54] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '260.00',
  'remainAmount' => ********,
  'mallrem' => -340117,
  'transid' => '7315425',
  'ref_id' => 92646236,
)  
[2025-07-03 03:11:18] local.INFO: header  
[2025-07-03 03:11:18] local.INFO: header after fliter  
[2025-07-03 03:11:18] local.INFO: Body  after fliter  
[2025-07-03 03:11:18] local.INFO: array (
  'Amount' => 450.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-03 03:11:18] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-03 03:11:18] local.ERROR: الكمية  
[2025-07-03 03:11:18] local.ERROR: سعر الوحدة  
[2025-07-03 03:11:18] local.ERROR: المبلغ  
[2025-07-03 03:11:18] local.ERROR: 1.210  
[2025-07-03 03:11:18] local.INFO: 371.90  
[2025-07-03 03:11:18] local.ERROR: مبلغ وقدرة  
[2025-07-03 03:11:18] local.INFO: الكمية  
[2025-07-03 03:11:18] local.INFO: سعر الوحدة  
[2025-07-03 03:11:18] local.INFO: المبلغ  
[2025-07-03 03:11:18] local.INFO: مبلغ وقدرة  
[2025-07-03 03:11:18] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-03 03:11:18] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '371.90',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '450.00',
  ),
)  
[2025-07-03 03:11:18] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '371.90',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '450.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-03 03:11:20] local.INFO: header  
[2025-07-03 03:11:20] local.CRITICAL: ****************************1  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 3  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 3  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 3  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 3  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 3  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 3  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 3  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 3  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 3  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 1  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 1  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 1  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 1  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 1  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 10013  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 10013  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 10013  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 10013  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 1  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 3  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 3  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 40  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 40  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 40  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 40  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 200  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 200  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 200  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 200  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 200  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 200  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 40  
[2025-07-03 03:11:21] local.ALERT: 2  
[2025-07-03 03:11:21] local.ALERT: 10013  
[2025-07-03 03:11:21] local.CRITICAL: ****************************  
[2025-07-03 03:11:21] local.CRITICAL:   
[2025-07-03 03:11:21] local.CRITICAL: ****************************  
[2025-07-03 03:11:21] local.CRITICAL: ****************************2  
[2025-07-03 03:11:21] local.INFO: checkUser 1  
[2025-07-03 03:11:22] local.INFO: {
  "ClientBalanceResult": "23900.8081"
}  
[2025-07-03 03:11:22] local.INFO: checkUser 2  
[2025-07-03 03:11:22] local.INFO: array (
  'ClientBalanceResult' => '23900.8081',
)  
[2025-07-03 03:11:22] local.INFO: 450  
[2025-07-03 03:11:22] local.ALERT: reach here  
[2025-07-03 03:11:22] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-03 03:11:22] local.ERROR: الكمية  
[2025-07-03 03:11:22] local.ERROR: سعر الوحدة  
[2025-07-03 03:11:22] local.ERROR: المبلغ  
[2025-07-03 03:11:22] local.ERROR: 1.210  
[2025-07-03 03:11:22] local.INFO: 1.210  
[2025-07-03 03:11:22] local.INFO: 371.901  
[2025-07-03 03:11:22] local.ERROR: 450.00  
[2025-07-03 03:11:22] local.INFO: checkUser 3  
[2025-07-03 03:11:22] local.INFO: checkUser 3#  
[2025-07-03 03:11:22] local.INFO: checkUser 4  
[2025-07-03 03:11:22] local.INFO: checkUser 4#  
[2025-07-03 03:11:22] local.INFO: checkUser 5  
[2025-07-03 03:11:22] local.DEBUG: lattttef  
[2025-07-03 03:11:22] local.DEBUG: array (
  'ClientBalanceResult' => '23900.8081',
)  
[2025-07-03 03:11:22] local.INFO: transaction1  
[2025-07-03 03:11:22] local.INFO: transaction2  
[2025-07-03 03:11:22] local.INFO: transaction3  
[2025-07-03 03:11:22] local.INFO: transaction4  
[2025-07-03 03:11:22] local.INFO: transaction4  
[2025-07-03 03:11:22] local.INFO: transaction5  
[2025-07-03 03:11:22] local.INFO: transaction6  
[2025-07-03 03:11:22] local.INFO: transaction7  
[2025-07-03 03:11:22] local.DEBUG: array (
  'AMT' => 450.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '371.901',
)  
[2025-07-03 03:11:22] local.INFO: transaction8  
[2025-07-03 03:11:22] local.INFO: transaction9  
[2025-07-03 03:11:22] local.INFO: 450  
[2025-07-03 03:11:22] local.INFO: transaction10  
[2025-07-03 03:11:22] local.INFO: 371.901  
[2025-07-03 03:11:22] local.INFO: 450.00  
[2025-07-03 03:11:22] local.INFO: transaction11  
[2025-07-03 03:11:22] local.INFO: 121  
[2025-07-03 03:11:22] local.INFO: topup1450.00  
[2025-07-03 03:11:22] local.INFO: topup21.210  
[2025-07-03 03:11:22] local.INFO: topup3450.00  
[2025-07-03 03:11:22] local.INFO: topup4450.00  
[2025-07-03 03:11:22] local.INFO: topup5450  
[2025-07-03 03:11:22] local.INFO: topup60  
[2025-07-03 03:11:22] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 450.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-03 03:11:22',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7315433,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-03 03:11:22',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '450.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '********031122',
  'Quantity' => '371.901',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '450.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '450.00',
  'TotalAmount' => 450.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 450.0,
  'ExCode' => NULL,
  'TransNumber' => '********031122',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#450#0',
  'ResponseTime' => '2025-07-03 03:11:22',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '22',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-03 03:11:22] local.INFO: transaction13  
[2025-07-03 03:11:22] local.INFO: transaction14  
[2025-07-03 03:11:22] local.INFO: transaction19  
[2025-07-03 03:11:22] local.INFO: transaction19#.  
[2025-07-03 03:11:22] local.INFO: transaction19#.  
[2025-07-03 03:11:22] local.INFO: transaction19#  
[2025-07-03 03:11:22] local.INFO: transaction19##  
[2025-07-03 03:11:22] local.INFO: transaction15  
[2025-07-03 03:11:24] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '450.00',
  'remainAmount' => ********,
  'mallrem' => -343551,
  'transid' => '7315433',
  'ref_id' => 92646899,
)  
[2025-07-03 04:26:52] local.INFO: header  
[2025-07-03 04:26:52] local.INFO: header after fliter  
[2025-07-03 04:26:52] local.INFO: Body  after fliter  
[2025-07-03 04:26:52] local.INFO: array (
  'Amount' => 260.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-03 04:26:53] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-03 04:26:53] local.ERROR: الكمية  
[2025-07-03 04:26:53] local.ERROR: سعر الوحدة  
[2025-07-03 04:26:53] local.ERROR: المبلغ  
[2025-07-03 04:26:53] local.ERROR: 1.210  
[2025-07-03 04:26:53] local.INFO: 214.88  
[2025-07-03 04:26:53] local.ERROR: مبلغ وقدرة  
[2025-07-03 04:26:53] local.INFO: الكمية  
[2025-07-03 04:26:53] local.INFO: سعر الوحدة  
[2025-07-03 04:26:53] local.INFO: المبلغ  
[2025-07-03 04:26:53] local.INFO: مبلغ وقدرة  
[2025-07-03 04:26:53] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-03 04:26:53] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '214.88',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '260.00',
  ),
)  
[2025-07-03 04:26:53] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '214.88',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '260.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-03 04:26:54] local.INFO: header  
[2025-07-03 04:26:54] local.CRITICAL: ****************************1  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 3  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 3  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 3  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 3  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 3  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 3  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 3  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 3  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 3  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 1  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 1  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 1  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 1  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 1  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 10013  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 10013  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 10013  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 10013  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 1  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 3  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 3  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 40  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 40  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 40  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 40  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 200  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 200  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 200  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 200  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 200  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 200  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 40  
[2025-07-03 04:26:54] local.ALERT: 2  
[2025-07-03 04:26:54] local.ALERT: 10013  
[2025-07-03 04:26:54] local.CRITICAL: ****************************  
[2025-07-03 04:26:54] local.CRITICAL:   
[2025-07-03 04:26:54] local.CRITICAL: ****************************  
[2025-07-03 04:26:54] local.CRITICAL: ****************************2  
[2025-07-03 04:26:54] local.INFO: checkUser 1  
[2025-07-03 04:26:56] local.INFO: {
  "ClientBalanceResult": "21738.8081"
}  
[2025-07-03 04:26:56] local.INFO: checkUser 2  
[2025-07-03 04:26:56] local.INFO: array (
  'ClientBalanceResult' => '21738.8081',
)  
[2025-07-03 04:26:56] local.INFO: 260  
[2025-07-03 04:26:56] local.ALERT: reach here  
[2025-07-03 04:26:56] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-03 04:26:56] local.ERROR: الكمية  
[2025-07-03 04:26:56] local.ERROR: سعر الوحدة  
[2025-07-03 04:26:56] local.ERROR: المبلغ  
[2025-07-03 04:26:56] local.ERROR: 1.210  
[2025-07-03 04:26:56] local.INFO: 1.210  
[2025-07-03 04:26:56] local.INFO: 214.876  
[2025-07-03 04:26:56] local.ERROR: 260.00  
[2025-07-03 04:26:56] local.INFO: checkUser 3  
[2025-07-03 04:26:56] local.INFO: checkUser 3#  
[2025-07-03 04:26:56] local.INFO: checkUser 4  
[2025-07-03 04:26:56] local.INFO: checkUser 4#  
[2025-07-03 04:26:56] local.INFO: checkUser 5  
[2025-07-03 04:26:56] local.DEBUG: lattttef  
[2025-07-03 04:26:56] local.DEBUG: array (
  'ClientBalanceResult' => '21738.8081',
)  
[2025-07-03 04:26:56] local.INFO: transaction1  
[2025-07-03 04:26:56] local.INFO: transaction2  
[2025-07-03 04:26:56] local.INFO: transaction3  
[2025-07-03 04:26:56] local.INFO: transaction4  
[2025-07-03 04:26:56] local.INFO: transaction4  
[2025-07-03 04:26:56] local.INFO: transaction5  
[2025-07-03 04:26:56] local.INFO: transaction6  
[2025-07-03 04:26:56] local.INFO: transaction7  
[2025-07-03 04:26:56] local.DEBUG: array (
  'AMT' => 260.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '214.876',
)  
[2025-07-03 04:26:56] local.INFO: transaction8  
[2025-07-03 04:26:56] local.INFO: transaction9  
[2025-07-03 04:26:56] local.INFO: 260  
[2025-07-03 04:26:56] local.INFO: transaction10  
[2025-07-03 04:26:56] local.INFO: 214.876  
[2025-07-03 04:26:56] local.INFO: 260.00  
[2025-07-03 04:26:56] local.INFO: transaction11  
[2025-07-03 04:26:56] local.INFO: 121  
[2025-07-03 04:26:56] local.INFO: topup1260.00  
[2025-07-03 04:26:56] local.INFO: topup21.210  
[2025-07-03 04:26:56] local.INFO: topup3260.00  
[2025-07-03 04:26:56] local.INFO: topup4260.00  
[2025-07-03 04:26:56] local.INFO: topup5260  
[2025-07-03 04:26:56] local.INFO: topup60  
[2025-07-03 04:26:56] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 260.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-03 04:26:56',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7315441,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-03 04:26:56',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '260.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '********042656',
  'Quantity' => '214.876',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '260.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '260.00',
  'TotalAmount' => 260.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 260.0,
  'ExCode' => NULL,
  'TransNumber' => '********042656',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#260#0',
  'ResponseTime' => '2025-07-03 04:26:56',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '56',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-03 04:26:56] local.INFO: transaction13  
[2025-07-03 04:26:56] local.INFO: transaction14  
[2025-07-03 04:26:56] local.INFO: transaction19  
[2025-07-03 04:26:56] local.INFO: transaction19#.  
[2025-07-03 04:26:56] local.INFO: transaction19#.  
[2025-07-03 04:26:56] local.INFO: transaction19#  
[2025-07-03 04:26:56] local.INFO: transaction19##  
[2025-07-03 04:26:56] local.INFO: transaction15  
[2025-07-03 04:26:58] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '260.00',
  'remainAmount' => ********,
  'mallrem' => -346229,
  'transid' => '7315441',
  'ref_id' => 92647248,
)  
