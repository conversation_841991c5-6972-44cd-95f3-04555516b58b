[2025-07-11 01:08:39] local.INFO: header  
[2025-07-11 01:08:39] local.INFO: header after fliter  
[2025-07-11 01:08:39] local.INFO: Body  after fliter  
[2025-07-11 01:08:39] local.INFO: array (
  'Amount' => 260.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-11 01:08:40] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-11 01:08:40] local.ERROR: الكمية  
[2025-07-11 01:08:40] local.ERROR: سعر الوحدة  
[2025-07-11 01:08:40] local.ERROR: المبلغ  
[2025-07-11 01:08:40] local.ERROR: 1.210  
[2025-07-11 01:08:40] local.INFO: 214.88  
[2025-07-11 01:08:40] local.ERROR: مبلغ وقدرة  
[2025-07-11 01:08:40] local.INFO: الكمية  
[2025-07-11 01:08:40] local.INFO: سعر الوحدة  
[2025-07-11 01:08:40] local.INFO: المبلغ  
[2025-07-11 01:08:40] local.INFO: مبلغ وقدرة  
[2025-07-11 01:08:40] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-11 01:08:40] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '214.88',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '260.00',
  ),
)  
[2025-07-11 01:08:40] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '214.88',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '260.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-11 01:08:43] local.INFO: header  
[2025-07-11 01:08:43] local.CRITICAL: ****************************1  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 3  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 3  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 3  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 3  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 3  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 3  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 3  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 3  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 3  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 1  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 1  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 1  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 1  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 1  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 10013  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 10013  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 10013  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 10013  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 1  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 3  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 3  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 40  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 40  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 40  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 40  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 200  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 200  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 200  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 200  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 200  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 200  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 40  
[2025-07-11 01:08:43] local.ALERT: 2  
[2025-07-11 01:08:43] local.ALERT: 10013  
[2025-07-11 01:08:43] local.CRITICAL: ****************************  
[2025-07-11 01:08:43] local.CRITICAL:   
[2025-07-11 01:08:43] local.CRITICAL: ****************************  
[2025-07-11 01:08:43] local.CRITICAL: ****************************2  
[2025-07-11 01:08:43] local.INFO: checkUser 1  
[2025-07-11 01:08:44] local.INFO: {
  "ClientBalanceResult": "52099.6081"
}  
[2025-07-11 01:08:44] local.INFO: checkUser 2  
[2025-07-11 01:08:44] local.INFO: array (
  'ClientBalanceResult' => '52099.6081',
)  
[2025-07-11 01:08:44] local.INFO: 260  
[2025-07-11 01:08:44] local.ALERT: reach here  
[2025-07-11 01:08:44] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-11 01:08:44] local.ERROR: الكمية  
[2025-07-11 01:08:44] local.ERROR: سعر الوحدة  
[2025-07-11 01:08:44] local.ERROR: المبلغ  
[2025-07-11 01:08:44] local.ERROR: 1.210  
[2025-07-11 01:08:44] local.INFO: 1.210  
[2025-07-11 01:08:44] local.INFO: 214.876  
[2025-07-11 01:08:44] local.ERROR: 260.00  
[2025-07-11 01:08:44] local.INFO: checkUser 3  
[2025-07-11 01:08:44] local.INFO: checkUser 3#  
[2025-07-11 01:08:44] local.INFO: checkUser 4  
[2025-07-11 01:08:45] local.INFO: checkUser 4#  
[2025-07-11 01:08:45] local.INFO: checkUser 5  
[2025-07-11 01:08:45] local.DEBUG: lattttef  
[2025-07-11 01:08:45] local.DEBUG: array (
  'ClientBalanceResult' => '52099.6081',
)  
[2025-07-11 01:08:45] local.INFO: transaction1  
[2025-07-11 01:08:45] local.INFO: transaction2  
[2025-07-11 01:08:45] local.INFO: transaction3  
[2025-07-11 01:08:45] local.INFO: transaction4  
[2025-07-11 01:08:45] local.INFO: transaction4  
[2025-07-11 01:08:45] local.INFO: transaction5  
[2025-07-11 01:08:45] local.INFO: transaction6  
[2025-07-11 01:08:45] local.INFO: transaction7  
[2025-07-11 01:08:45] local.DEBUG: array (
  'AMT' => 260.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '214.876',
)  
[2025-07-11 01:08:45] local.INFO: transaction8  
[2025-07-11 01:08:45] local.INFO: transaction9  
[2025-07-11 01:08:45] local.INFO: 260  
[2025-07-11 01:08:46] local.INFO: transaction10  
[2025-07-11 01:08:46] local.INFO: 214.876  
[2025-07-11 01:08:46] local.INFO: 260.00  
[2025-07-11 01:08:46] local.INFO: transaction11  
[2025-07-11 01:08:46] local.INFO: 121  
[2025-07-11 01:08:46] local.INFO: topup1260.00  
[2025-07-11 01:08:46] local.INFO: topup21.210  
[2025-07-11 01:08:46] local.INFO: topup3260.00  
[2025-07-11 01:08:46] local.INFO: topup4260.00  
[2025-07-11 01:08:46] local.INFO: topup5260  
[2025-07-11 01:08:46] local.INFO: topup60  
[2025-07-11 01:08:46] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 260.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-11 01:08:46',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7340870,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-11 01:08:46',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '260.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '********010846',
  'Quantity' => '214.876',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '260.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '260.00',
  'TotalAmount' => 260.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 260.0,
  'ExCode' => NULL,
  'TransNumber' => '********010846',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#260#0',
  'ResponseTime' => '2025-07-11 01:08:46',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '46',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-11 01:08:46] local.INFO: transaction13  
[2025-07-11 01:08:46] local.INFO: transaction14  
[2025-07-11 01:08:46] local.INFO: transaction19  
[2025-07-11 01:08:46] local.INFO: transaction19#.  
[2025-07-11 01:08:46] local.INFO: transaction19#.  
[2025-07-11 01:08:46] local.INFO: transaction19#  
[2025-07-11 01:08:46] local.INFO: transaction19##  
[2025-07-11 01:08:46] local.INFO: transaction15  
[2025-07-11 01:08:49] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '260.00',
  'remainAmount' => ********,
  'mallrem' => -1546307,
  'transid' => '7340870',
  'ref_id' => 93557588,
)  
[2025-07-11 11:22:06] local.INFO: header  
[2025-07-11 11:22:06] local.INFO: header after fliter  
[2025-07-11 11:22:06] local.INFO: Body  after fliter  
[2025-07-11 11:22:06] local.INFO: array (
)  
[2025-07-11 11:22:06] local.INFO: transaction14  
[2025-07-11 11:22:06] local.INFO: first inquery phone = 107206034  
[2025-07-11 11:22:06] local.DEBUG: response querySubBalance  
[2025-07-11 11:22:06] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-11 11:22:06] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-11 11:22:06] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-11 11:22:06] local.DEBUG: print  before faction by provider price  
[2025-07-11 11:22:06] local.DEBUG: print  after faction by provider price  
[2025-07-11 16:41:14] local.INFO: header  
[2025-07-11 16:41:16] local.INFO: header after fliter  
[2025-07-11 16:41:16] local.INFO: Body  after fliter  
[2025-07-11 16:41:16] local.INFO: array (
)  
[2025-07-11 16:41:16] local.INFO: transaction14  
[2025-07-11 16:41:16] local.INFO: first inquery phone = 103325837  
[2025-07-11 16:41:18] local.DEBUG: response querySubBalance  
[2025-07-11 16:41:18] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-11 16:41:18] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-11 16:41:18] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-11 16:41:18] local.DEBUG: print  before faction by provider price  
[2025-07-11 16:41:19] local.DEBUG: print  after faction by provider price  
[2025-07-11 16:41:21] local.INFO: header  
[2025-07-11 16:41:21] local.INFO: header after fliter  
[2025-07-11 16:41:21] local.INFO: Body  after fliter  
[2025-07-11 16:41:21] local.INFO: array (
)  
[2025-07-11 16:41:21] local.INFO: transaction14  
[2025-07-11 16:41:21] local.INFO: first inquery phone = 103325837  
[2025-07-11 16:41:21] local.DEBUG: response querySubBalance  
[2025-07-11 16:41:21] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-11 16:41:21] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-11 16:41:21] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-11 16:41:21] local.DEBUG: print  before faction by provider price  
[2025-07-11 16:41:21] local.DEBUG: print  after faction by provider price  
[2025-07-11 16:43:43] local.INFO: header  
[2025-07-11 16:43:43] local.INFO: header after fliter  
[2025-07-11 16:43:43] local.INFO: Body  after fliter  
[2025-07-11 16:43:43] local.INFO: array (
)  
[2025-07-11 16:43:43] local.INFO: transaction14  
[2025-07-11 16:43:43] local.INFO: first inquery phone = 103322059  
[2025-07-11 16:43:43] local.DEBUG: response querySubBalance  
[2025-07-11 16:43:43] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-11 16:43:43] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-11 16:43:43] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-11 16:43:43] local.DEBUG: print  before faction by provider price  
[2025-07-11 16:43:43] local.DEBUG: print  after faction by provider price  
[2025-07-11 16:44:52] local.INFO: header  
[2025-07-11 16:44:52] local.INFO: header after fliter  
[2025-07-11 16:44:52] local.INFO: Body  after fliter  
[2025-07-11 16:44:52] local.INFO: array (
)  
[2025-07-11 16:44:52] local.INFO: transaction14  
[2025-07-11 16:44:52] local.INFO: first inquery phone = 103322192  
[2025-07-11 16:44:52] local.DEBUG: response querySubBalance  
[2025-07-11 16:44:52] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-11 16:44:52] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-11 16:44:52] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-11 16:44:52] local.DEBUG: print  before faction by provider price  
[2025-07-11 16:44:52] local.DEBUG: print  after faction by provider price  
[2025-07-11 21:37:55] local.INFO: header  
[2025-07-11 21:37:55] local.INFO: header after fliter  
[2025-07-11 21:37:55] local.INFO: Body  after fliter  
[2025-07-11 21:37:55] local.INFO: array (
  'Amount' => 450.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-11 21:37:55] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-11 21:37:55] local.ERROR: الكمية  
[2025-07-11 21:37:55] local.ERROR: سعر الوحدة  
[2025-07-11 21:37:55] local.ERROR: المبلغ  
[2025-07-11 21:37:55] local.ERROR: 1.210  
[2025-07-11 21:37:55] local.INFO: 371.90  
[2025-07-11 21:37:55] local.ERROR: مبلغ وقدرة  
[2025-07-11 21:37:55] local.INFO: الكمية  
[2025-07-11 21:37:55] local.INFO: سعر الوحدة  
[2025-07-11 21:37:55] local.INFO: المبلغ  
[2025-07-11 21:37:55] local.INFO: مبلغ وقدرة  
[2025-07-11 21:37:55] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-11 21:37:55] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '371.90',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '450.00',
  ),
)  
[2025-07-11 21:37:55] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '371.90',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '450.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-11 21:37:57] local.INFO: header  
[2025-07-11 21:37:57] local.CRITICAL: ****************************1  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 3  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 3  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 3  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 3  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 3  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 3  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 3  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 3  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 3  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 1  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 1  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 1  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 1  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 1  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 10013  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 10013  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 10013  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 10013  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 1  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 3  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 3  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 40  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 40  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 40  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 40  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 200  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 200  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 200  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 200  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 200  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 200  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 40  
[2025-07-11 21:37:57] local.ALERT: 2  
[2025-07-11 21:37:57] local.ALERT: 10013  
[2025-07-11 21:37:57] local.CRITICAL: ****************************  
[2025-07-11 21:37:57] local.CRITICAL:   
[2025-07-11 21:37:57] local.CRITICAL: ****************************  
[2025-07-11 21:37:57] local.CRITICAL: ****************************2  
[2025-07-11 21:37:57] local.INFO: checkUser 1  
[2025-07-11 21:37:58] local.INFO: {
  "ClientBalanceResult": "37150.3781"
}  
[2025-07-11 21:37:58] local.INFO: checkUser 2  
[2025-07-11 21:37:58] local.INFO: array (
  'ClientBalanceResult' => '37150.3781',
)  
[2025-07-11 21:37:58] local.INFO: 450  
[2025-07-11 21:37:58] local.ALERT: reach here  
[2025-07-11 21:37:58] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-11 21:37:58] local.ERROR: الكمية  
[2025-07-11 21:37:58] local.ERROR: سعر الوحدة  
[2025-07-11 21:37:58] local.ERROR: المبلغ  
[2025-07-11 21:37:58] local.ERROR: 1.210  
[2025-07-11 21:37:58] local.INFO: 1.210  
[2025-07-11 21:37:58] local.INFO: 371.901  
[2025-07-11 21:37:58] local.ERROR: 450.00  
[2025-07-11 21:37:58] local.INFO: checkUser 3  
[2025-07-11 21:37:58] local.INFO: checkUser 3#  
[2025-07-11 21:37:58] local.INFO: checkUser 4  
[2025-07-11 21:37:59] local.INFO: checkUser 4#  
[2025-07-11 21:37:59] local.INFO: checkUser 5  
[2025-07-11 21:37:59] local.DEBUG: lattttef  
[2025-07-11 21:37:59] local.DEBUG: array (
  'ClientBalanceResult' => '37150.3781',
)  
[2025-07-11 21:37:59] local.INFO: transaction1  
[2025-07-11 21:37:59] local.INFO: transaction2  
[2025-07-11 21:37:59] local.INFO: transaction3  
[2025-07-11 21:37:59] local.INFO: transaction4  
[2025-07-11 21:37:59] local.INFO: transaction4  
[2025-07-11 21:37:59] local.INFO: transaction5  
[2025-07-11 21:37:59] local.INFO: transaction6  
[2025-07-11 21:37:59] local.INFO: transaction7  
[2025-07-11 21:37:59] local.DEBUG: array (
  'AMT' => 450.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '371.901',
)  
[2025-07-11 21:37:59] local.INFO: transaction8  
[2025-07-11 21:37:59] local.INFO: transaction9  
[2025-07-11 21:37:59] local.INFO: 450  
[2025-07-11 21:37:59] local.INFO: transaction10  
[2025-07-11 21:37:59] local.INFO: 371.901  
[2025-07-11 21:37:59] local.INFO: 450.00  
[2025-07-11 21:37:59] local.INFO: transaction11  
[2025-07-11 21:37:59] local.INFO: 121  
[2025-07-11 21:37:59] local.INFO: topup1450.00  
[2025-07-11 21:37:59] local.INFO: topup21.210  
[2025-07-11 21:37:59] local.INFO: topup3450.00  
[2025-07-11 21:37:59] local.INFO: topup4450.00  
[2025-07-11 21:37:59] local.INFO: topup5450  
[2025-07-11 21:37:59] local.INFO: topup60  
[2025-07-11 21:37:59] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 450.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-11 21:37:59',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7341175,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-11 21:37:59',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '450.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '********213759',
  'Quantity' => '371.901',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '450.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '450.00',
  'TotalAmount' => 450.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 450.0,
  'ExCode' => NULL,
  'TransNumber' => '********213759',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#450#0',
  'ResponseTime' => '2025-07-11 21:37:59',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '59',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-11 21:37:59] local.INFO: transaction13  
[2025-07-11 21:37:59] local.INFO: transaction14  
[2025-07-11 21:37:59] local.INFO: transaction19  
[2025-07-11 21:37:59] local.INFO: transaction19#.  
[2025-07-11 21:37:59] local.INFO: transaction19#.  
[2025-07-11 21:37:59] local.INFO: transaction19#  
[2025-07-11 21:37:59] local.INFO: transaction19##  
[2025-07-11 21:37:59] local.INFO: transaction15  
[2025-07-11 21:38:00] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '450.00',
  'remainAmount' => ********,
  'mallrem' => -1670375,
  'transid' => '7341175',
  'ref_id' => 93637081,
)  
[2025-07-11 22:26:19] local.INFO: header  
[2025-07-11 22:26:19] local.INFO: header after fliter  
[2025-07-11 22:26:19] local.INFO: Body  after fliter  
[2025-07-11 22:26:19] local.INFO: array (
  'Amount' => 540.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-11 22:26:19] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-11 22:26:19] local.ERROR: الكمية  
[2025-07-11 22:26:19] local.ERROR: سعر الوحدة  
[2025-07-11 22:26:19] local.ERROR: المبلغ  
[2025-07-11 22:26:19] local.ERROR: 1.210  
[2025-07-11 22:26:19] local.INFO: 446.28  
[2025-07-11 22:26:19] local.ERROR: مبلغ وقدرة  
[2025-07-11 22:26:19] local.INFO: الكمية  
[2025-07-11 22:26:19] local.INFO: سعر الوحدة  
[2025-07-11 22:26:19] local.INFO: المبلغ  
[2025-07-11 22:26:19] local.INFO: مبلغ وقدرة  
[2025-07-11 22:26:19] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-11 22:26:19] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '446.28',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '540.00',
  ),
)  
[2025-07-11 22:26:19] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '446.28',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '540.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-11 22:26:23] local.INFO: header  
[2025-07-11 22:26:23] local.CRITICAL: ****************************1  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 3  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 3  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 3  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 3  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 3  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 3  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 3  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 3  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 3  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 1  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 1  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 1  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 1  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 1  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 10013  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 10013  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 10013  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 10013  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 1  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 3  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 3  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 40  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 40  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 40  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 40  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 200  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 200  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 200  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 200  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 200  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 200  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 40  
[2025-07-11 22:26:23] local.ALERT: 2  
[2025-07-11 22:26:23] local.ALERT: 10013  
[2025-07-11 22:26:23] local.CRITICAL: ****************************  
[2025-07-11 22:26:23] local.CRITICAL:   
[2025-07-11 22:26:23] local.CRITICAL: ****************************  
[2025-07-11 22:26:23] local.CRITICAL: ****************************2  
[2025-07-11 22:26:23] local.INFO: checkUser 1  
[2025-07-11 22:26:24] local.INFO: {
  "ClientBalanceResult": "36700.3781"
}  
[2025-07-11 22:26:24] local.INFO: checkUser 2  
[2025-07-11 22:26:24] local.INFO: array (
  'ClientBalanceResult' => '36700.3781',
)  
[2025-07-11 22:26:24] local.INFO: 540  
[2025-07-11 22:26:24] local.ALERT: reach here  
[2025-07-11 22:26:24] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-11 22:26:24] local.ERROR: الكمية  
[2025-07-11 22:26:24] local.ERROR: سعر الوحدة  
[2025-07-11 22:26:24] local.ERROR: المبلغ  
[2025-07-11 22:26:24] local.ERROR: 1.210  
[2025-07-11 22:26:24] local.INFO: 1.210  
[2025-07-11 22:26:24] local.INFO: 446.281  
[2025-07-11 22:26:24] local.ERROR: 540.00  
[2025-07-11 22:26:24] local.INFO: checkUser 3  
[2025-07-11 22:26:24] local.INFO: checkUser 3#  
[2025-07-11 22:26:24] local.INFO: checkUser 4  
[2025-07-11 22:26:24] local.INFO: checkUser 4#  
[2025-07-11 22:26:24] local.INFO: checkUser 5  
[2025-07-11 22:26:24] local.DEBUG: lattttef  
[2025-07-11 22:26:24] local.DEBUG: array (
  'ClientBalanceResult' => '36700.3781',
)  
[2025-07-11 22:26:24] local.INFO: transaction1  
[2025-07-11 22:26:24] local.INFO: transaction2  
[2025-07-11 22:26:24] local.INFO: transaction3  
[2025-07-11 22:26:24] local.INFO: transaction4  
[2025-07-11 22:26:24] local.INFO: transaction4  
[2025-07-11 22:26:24] local.INFO: transaction5  
[2025-07-11 22:26:24] local.INFO: transaction6  
[2025-07-11 22:26:24] local.INFO: transaction7  
[2025-07-11 22:26:24] local.DEBUG: array (
  'AMT' => 540.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '446.281',
)  
[2025-07-11 22:26:24] local.INFO: transaction8  
[2025-07-11 22:26:25] local.INFO: transaction9  
[2025-07-11 22:26:25] local.INFO: 540  
[2025-07-11 22:26:25] local.INFO: transaction10  
[2025-07-11 22:26:25] local.INFO: 446.281  
[2025-07-11 22:26:25] local.INFO: 540.00  
[2025-07-11 22:26:25] local.INFO: transaction11  
[2025-07-11 22:26:25] local.INFO: 121  
[2025-07-11 22:26:25] local.INFO: topup1540.00  
[2025-07-11 22:26:25] local.INFO: topup21.210  
[2025-07-11 22:26:25] local.INFO: topup3540.00  
[2025-07-11 22:26:25] local.INFO: topup4540.00  
[2025-07-11 22:26:25] local.INFO: topup5540  
[2025-07-11 22:26:25] local.INFO: topup60  
[2025-07-11 22:26:25] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 540.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-11 22:26:25',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7341202,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-11 22:26:25',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '540.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '********222625',
  'Quantity' => '446.281',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '540.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '540.00',
  'TotalAmount' => 540.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 540.0,
  'ExCode' => NULL,
  'TransNumber' => '********222625',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#540#0',
  'ResponseTime' => '2025-07-11 22:26:25',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '25',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-11 22:26:25] local.INFO: transaction13  
[2025-07-11 22:26:25] local.INFO: transaction14  
[2025-07-11 22:26:25] local.INFO: transaction19  
[2025-07-11 22:26:25] local.INFO: transaction19#.  
[2025-07-11 22:26:25] local.INFO: transaction19#.  
[2025-07-11 22:26:25] local.INFO: transaction19#  
[2025-07-11 22:26:25] local.INFO: transaction19##  
[2025-07-11 22:26:25] local.INFO: transaction15  
[2025-07-11 22:26:26] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '540.00',
  'remainAmount' => ********,
  'mallrem' => -1683616,
  'transid' => '7341202',
  'ref_id' => 93642200,
)  
