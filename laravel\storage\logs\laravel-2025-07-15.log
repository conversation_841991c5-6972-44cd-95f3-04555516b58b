[2025-07-15 01:25:15] local.ERROR: syntax error, unexpected token ")" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \")\" at C:\\xampp\\htdocs\\laravel\\vendor\\symfony\\var-dumper\\Dumper\\AbstractDumper.php:74)
[stacktrace]
#0 C:\\xampp\\htdocs\\laravel\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\xampp\\\\htdocs...')
#1 C:\\xampp\\htdocs\\laravel\\vendor\\symfony\\var-dumper\\Dumper\\CliDumper.php(22): Composer\\Autoload\\ClassLoader->loadClass('Symfony\\\\Compone...')
#2 C:\\xampp\\htdocs\\laravel\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\xampp\\\\htdocs...')
#3 C:\\xampp\\htdocs\\laravel\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\xampp\\\\htdocs...')
#4 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\CliDumper.php(13): Composer\\Autoload\\ClassLoader->loadClass('Symfony\\\\Compone...')
#5 C:\\xampp\\htdocs\\laravel\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\xampp\\\\htdocs...')
#6 C:\\xampp\\htdocs\\laravel\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\xampp\\\\htdocs...')
#7 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php(101): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Foun...')
#8 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php(70): Illuminate\\Foundation\\Providers\\FoundationServiceProvider->registerDumper()
#9 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(689): Illuminate\\Foundation\\Providers\\FoundationServiceProvider->register()
#10 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Illuminate\\Foundation\\Providers\\FoundationServiceProvider))
#11 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(666): Illuminate\\Foundation\\ProviderRepository->load(Array)
#12 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#13 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#14 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#15 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#16 C:\\xampp\\htdocs\\laravel\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 {main}
"} 
[2025-07-15 01:28:22] local.ERROR: syntax error, unexpected token ")" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \")\" at C:\\xampp\\htdocs\\laravel\\vendor\\symfony\\var-dumper\\Dumper\\AbstractDumper.php:74)
[stacktrace]
#0 C:\\xampp\\htdocs\\laravel\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\xampp\\\\htdocs...')
#1 C:\\xampp\\htdocs\\laravel\\vendor\\symfony\\var-dumper\\Dumper\\CliDumper.php(22): Composer\\Autoload\\ClassLoader->loadClass('Symfony\\\\Compone...')
#2 C:\\xampp\\htdocs\\laravel\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\xampp\\\\htdocs...')
#3 C:\\xampp\\htdocs\\laravel\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\xampp\\\\htdocs...')
#4 C:\\xampp\\htdocs\\laravel\\vendor\\symfony\\var-dumper\\Dumper\\HtmlDumper.php(22): Composer\\Autoload\\ClassLoader->loadClass('Symfony\\\\Compone...')
#5 C:\\xampp\\htdocs\\laravel\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\xampp\\\\htdocs...')
#6 C:\\xampp\\htdocs\\laravel\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\xampp\\\\htdocs...')
#7 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\HtmlDumper.php(12): Composer\\Autoload\\ClassLoader->loadClass('Symfony\\\\Compone...')
#8 C:\\xampp\\htdocs\\laravel\\vendor\\composer\\ClassLoader.php(576): include('C:\\\\xampp\\\\htdocs...')
#9 C:\\xampp\\htdocs\\laravel\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\xampp\\\\htdocs...')
#10 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php(101): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Foun...')
#11 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php(70): Illuminate\\Foundation\\Providers\\FoundationServiceProvider->registerDumper()
#12 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(689): Illuminate\\Foundation\\Providers\\FoundationServiceProvider->register()
#13 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Illuminate\\Foundation\\Providers\\FoundationServiceProvider))
#14 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(666): Illuminate\\Foundation\\ProviderRepository->load(Array)
#15 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#16 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#17 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#18 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#19 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\laravel\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#21 {main}
"} 
[2025-07-15 01:28:22] local.ERROR: syntax error, unexpected token ")" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \")\" at C:\\xampp\\htdocs\\laravel\\vendor\\symfony\\error-handler\\ErrorRenderer\\HtmlErrorRenderer.php:51)
[stacktrace]
#0 C:\\xampp\\htdocs\\laravel\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\xampp\\\\htdocs...')
#1 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(610): Composer\\Autoload\\ClassLoader->loadClass('Symfony\\\\Compone...')
#2 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(586): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionWithSymfony(Object(ParseError), true)
#3 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(567): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionContent(Object(ParseError))
#4 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(546): Illuminate\\Foundation\\Exceptions\\Handler->convertExceptionToResponse(Object(ParseError))
#5 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(460): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(ParseError))
#6 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(377): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionResponse(Object(Illuminate\\Http\\Request), Object(ParseError))
#7 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(493): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(ParseError))
#8 C:\\xampp\\htdocs\\laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(138): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(ParseError))
#9 C:\\xampp\\htdocs\\laravel\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#10 {main}
"} 
