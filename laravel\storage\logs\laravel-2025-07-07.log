[2025-07-07 13:41:46] local.INFO: header  
[2025-07-07 13:41:46] local.INFO: header  
[2025-07-07 13:41:46] local.INFO: header  
[2025-07-07 13:41:48] local.INFO: header after fliter  
[2025-07-07 13:41:48] local.CRITICAL: ****************************1  
[2025-07-07 13:41:48] local.INFO: Body  after fliter  
[2025-07-07 13:41:48] local.INFO: Body  after fliter  
[2025-07-07 13:41:48] local.ALERT: reach here  
[2025-07-07 13:41:48] local.INFO: array (
)  
[2025-07-07 13:41:48] local.INFO: array (
)  
[2025-07-07 13:41:48] local.ALERT: array (
  'AMT' => 46000.0,
  'CType' => 0,
  'FID' => 90,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '101936849',
  'State' => 0,
  'lateflog' => '530625',
)  
[2025-07-07 13:41:48] local.INFO: transaction14  
[2025-07-07 13:41:48] local.INFO: first inquery phone = 101936849  
[2025-07-07 13:41:48] local.INFO: first inquery phone = 101936849  
[2025-07-07 13:41:50] local.DEBUG: response querySubBalance  
   'Key' => 'المبلغ',
    'Value' => '46000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ستة وأربعون ألف  ر.ي.',
  ),
)  
[2025-07-07 13:41:50] local.DEBUG: response querySubBalance  
[2025-07-07 13:41:50] local.ERROR: المبلغ  
[2025-07-07 13:41:50] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-07 13:41:50] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-07 13:41:50] local.ERROR: 46000.00  
[2025-07-07 13:41:50] local.ERROR: مبلغ وقدرة  
[2025-07-07 13:41:50] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-07 13:41:50] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-07 13:41:50] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-07 13:41:50] local.DEBUG: print  before faction by provider price  
[2025-07-07 13:41:50] local.DEBUG: print  before faction by provider price  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 3  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 3  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 3  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 3  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 3  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 3  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 3  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 3  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 3  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 2  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 2  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 2  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 2  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 2  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 2  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 2  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 1  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 1  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 1  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 1  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 1  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 2  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 10013  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 10013  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 10013  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 10013  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 1  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 3  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 3  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 40  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 40  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 40  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 40  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.WARNING: 1  
[2025-07-07 13:41:50] local.WARNING: array (
  'ID' => 90,
  'Name' => 'فئة 500 جيجا 46000 ريال',
  'ServiceID' => 200,
  'Price' => 46000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 46000.0,
)  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 40  
[2025-07-07 13:41:50] local.ALERT: 200  
[2025-07-07 13:41:50] local.ALERT: 10013  
[2025-07-07 13:41:50] local.CRITICAL: ****************************  
[2025-07-07 13:41:50] local.CRITICAL:   
[2025-07-07 13:41:50] local.CRITICAL: ****************************  
[2025-07-07 13:41:50] local.CRITICAL: ****************************2  
[2025-07-07 13:41:50] local.INFO: checkUser 1  
[2025-07-07 13:41:50] local.INFO: {
  "ClientBalanceResult": "0.0000"
}  
[2025-07-07 13:41:52] local.DEBUG: print  after faction by provider price  
[2025-07-07 13:41:52] local.INFO: array (
  'ClientBalanceResult' => '0.0000',
)  
[2025-07-07 13:41:52] local.INFO: price less than Balance  
[2025-07-07 13:41:53] local.INFO: thisprice less than Balance  
