<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Journal extends Model
{
    use HasFactory;
    protected $table = 'Journal';
    public $timestamps = false;

    protected $fillable = [ 
        'VoucherID',
        'Number',
        'EntryID',
        'Date',
        'CreatedBy',
        'Debited',
        'CreatedTime',
        'BranchID',
        // 'Year',
        'Status',
        'TotalAmount',
        'SyncJournalID',
        'datestamb',
        'EntrySource',
        'Depended',
        'RefNumber',
        'CurrencyID'
    ];
}
