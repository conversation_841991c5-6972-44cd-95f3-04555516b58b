<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderInfo extends Model
{
    use HasFactory;

    protected $table = 'OrderInfo';
    public $timestamps = false;

    protected $fillable = [
        'RowVersion',
        'Number',
        'ServiceCategoryID',
        'ServiceID',
        'OrderType',
        'Amount',
        'CurrencyID',
        'CreditorAccountID',
        'MCAmount',
        'DebitorAccountID',
        'Description',
        'Date',
        'Channel',
        'Note',
        'ExtraAmount',
        'RefNumber',
        'IsAmountModified',
        'AmountModifiedBy',
        'State',
        'InUse',
        'UsageTime',
        'BindBy',
        'IsDebited',
        'DebitedBy',
        'IsRejected',
        'RejectReason',
        'RejectedBy',
        'EntryID',
        'ServiceEntryID',
        'CreatedBy',
        'BranchID',
        'CreatedTime',
        'Status',
        'AccountID',
        'SubNote',
        'CommissionAmount',
        'CommissionCurrencyID',
    ];

    // public function branch()
    // {
    //     return $this->belongsTo(Branch::class, 'BranchID');
    // }

    public function account()
    {
        return $this->belongsTo(Account::class, 'AccountID');
    }

    // public function serviceCategory()
    // {
    //     return $this->belongsTo(MoneyMaster::class, 'ServiceCategoryID');
    // }

    // public function serviceInfo()
    // {
    //     return $this->belongsTo(ServiceInfo::class, 'ServiceID');
    // }

    public function createdByUser()
    {
        return $this->belongsTo(UserInfo::class, 'CreatedBy');
    }

    public function bindByUser()
    {
        return $this->belongsTo(UserInfo::class, 'BindBy');
    }

    public function debitedByUser()
    {
        return $this->belongsTo(UserInfo::class, 'DebitedBy');
    }

    public function rejectedByUser()
    {
        return $this->belongsTo(UserInfo::class, 'RejectedBy');
    }

    public function journalEntry()
    {
        return $this->belongsTo(Journal::class, 'EntryID');
    }

    // public function paymentEntry()
    // {
    //     return $this->belongsTo(Payment::class, 'ServiceEntryID');
    // }
}
