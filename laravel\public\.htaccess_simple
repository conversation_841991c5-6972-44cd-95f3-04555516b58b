# Laravel Simple .htaccess
# يوجه جميع الطلبات إلى التطبيق المبسط

<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # منع الوصول للملفات الحساسة
    RewriteRule ^(.*/)?\.env$ - [F,L]
    RewriteRule ^(.*/)?composer\.(json|lock)$ - [F,L]
    RewriteRule ^(.*/)?package\.json$ - [F,L]
    
    # توجيه الطلبات للتطبيق المبسط
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !^/(test\.php|simple_laravel\.php|minimal_app\.php|full_laravel\.php)$
    RewriteRule ^(.*)$ full_laravel.php [QSA,L]
    
    # إعادة توجيه الصفحة الرئيسية
    RewriteRule ^$ full_laravel.php [QSA,L]
</IfModule>

# إعدادات الأمان
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.*">
    Order allow,deny
    Deny from all
</Files>

# إعدادات PHP
<IfModule mod_php8.c>
    php_value upload_max_filesize 64M
    php_value post_max_size 64M
    php_value memory_limit 256M
    php_value max_execution_time 300
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache headers
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
</IfModule>
