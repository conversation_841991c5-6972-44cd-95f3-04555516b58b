<?php
/**
 * إصلاح مشاكل PHP 8.0 في Laravel
 * يقوم بإصلاح جميع مشاكل التوافق مع PHP 8.0
 */

echo "🔧 بدء إصلاح مشاكل PHP 8.0...\n";

// قائمة الملفات والإصلاحات المطلوبة
$fixes = [
    // إصلاح enum في AnsiColorMode
    'vendor/symfony/console/Output/AnsiColorMode.php' => [
        'enum AnsiColorMode' => 'class AnsiColorMode',
        'case Ansi4;' => 'const Ansi4 = \'ansi4\';',
        'case Ansi8;' => 'const Ansi8 = \'ansi8\';',
        'case Ansi24;' => 'const Ansi24 = \'ansi24\';',
        'match ($this)' => 'switch ($this->value)',
        'self::Ansi4 =>' => 'case self::Ansi4:',
        'self::Ansi8 =>' => 'case self::Ansi8:',
        'self::Ansi24 =>' => 'case self::Ansi24:',
        'default => throw' => 'default: throw'
    ],
    
    // إصلاح spread operator في Finder
    'vendor/symfony/finder/Finder.php' => [
        'array_map($this->normalizeDir(...), $glob)' => 'array_map([$this, \'normalizeDir\'], $glob)'
    ],
    
    // إصلاح AbstractDumper
    'vendor/symfony/var-dumper/Dumper/AbstractDumper.php' => [
        '$this->echoLine(...)' => '[$this, \'echoLine\']'
    ],
    
    // إصلاح Command
    'vendor/symfony/console/Command/Command.php' => [
        '$code = $code(...);' => '$code = call_user_func($code);'
    ],
    
    // إصلاح platform check
    'vendor/composer/platform_check.php' => [
        'trigger_error(' => '// trigger_error('
    ]
];

$fixedFiles = 0;
$totalFixes = 0;

foreach ($fixes as $file => $replacements) {
    $fullPath = __DIR__ . '/' . $file;
    
    if (!file_exists($fullPath)) {
        echo "⚠️  الملف غير موجود: $file\n";
        continue;
    }
    
    $content = file_get_contents($fullPath);
    $originalContent = $content;
    $fileFixes = 0;
    
    foreach ($replacements as $search => $replace) {
        if (strpos($content, $search) !== false) {
            $content = str_replace($search, $replace, $content);
            $fileFixes++;
            $totalFixes++;
            echo "✅ تم إصلاح: $search في $file\n";
        }
    }
    
    if ($fileFixes > 0) {
        file_put_contents($fullPath, $content);
        $fixedFiles++;
        echo "📝 تم حفظ الملف: $file ($fileFixes إصلاحات)\n";
    }
}

echo "\n🎉 تم الانتهاء من الإصلاح!\n";
echo "📊 الإحصائيات:\n";
echo "   - الملفات المُصلحة: $fixedFiles\n";
echo "   - إجمالي الإصلاحات: $totalFixes\n";

// إضافة constructor للـ AnsiColorMode class
$ansiFile = __DIR__ . '/vendor/symfony/console/Output/AnsiColorMode.php';
if (file_exists($ansiFile)) {
    $content = file_get_contents($ansiFile);
    
    // إضافة constructor وmethods إذا لم تكن موجودة
    if (strpos($content, 'private $value;') === false) {
        $classDefinition = 'class AnsiColorMode
{
    const Ansi4 = \'ansi4\';
    const Ansi8 = \'ansi8\';
    const Ansi24 = \'ansi24\';
    
    private $value;
    
    public function __construct(string $value)
    {
        $this->value = $value;
    }
    
    public static function Ansi4(): self
    {
        return new self(self::Ansi4);
    }
    
    public static function Ansi8(): self
    {
        return new self(self::Ansi8);
    }
    
    public static function Ansi24(): self
    {
        return new self(self::Ansi24);
    }
    
    public function __toString(): string
    {
        return $this->value;
    }';
        
        $content = str_replace('class AnsiColorMode
{
    const Ansi4 = \'ansi4\';', $classDefinition, $content);
        
        file_put_contents($ansiFile, $content);
        echo "✅ تم إضافة constructor لـ AnsiColorMode\n";
    }
}

echo "\n🚀 يمكنك الآن تشغيل Laravel بنجاح!\n";
echo "استخدم: php artisan serve --host=127.0.0.1 --port=8083\n";
?>
