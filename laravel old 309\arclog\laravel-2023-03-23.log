[2023-03-23 00:02:41] local.INFO: header  
[2023-03-23 00:02:41] local.CRITICAL: ****************************1  
[2023-03-23 00:02:41] local.ALERT: reach here  
[2023-03-23 00:02:41] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '26000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ستة وعشرون ألف  ر.ي.',
  ),
)  
[2023-03-23 00:02:41] local.ERROR: المبلغ  
[2023-03-23 00:02:41] local.ERROR: 26,000.00  
[2023-03-23 00:02:41] local.ERROR: مبلغ وقدرة  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 3  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 3  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 3  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 3  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 3  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 3  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 3  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 3  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 3  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 2  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 2  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 2  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 2  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 2  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 2  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 2  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 1  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 1  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 1  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 1  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 1  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 2  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 10013  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 10013  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 10013  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 10013  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 1  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 3  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 3  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 40  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 40  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 40  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 40  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.WARNING: 1  
[2023-03-23 00:02:41] local.WARNING: array (
  'ID' => 89,
  'Name' => 'فئة 250 جيجا 26000 ريال',
  'ServiceID' => 200,
  'Price' => 26000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 26000.0,
)  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 200  
[2023-03-23 00:02:41] local.ALERT: 40  
[2023-03-23 00:02:41] local.CRITICAL: ****************************2  
[2023-03-23 00:02:41] local.CRITICAL: ****************************  
[2023-03-23 00:02:41] local.CRITICAL:   
[2023-03-23 00:02:41] local.CRITICAL: ****************************  
[2023-03-23 00:02:41] local.INFO: {
  "ClientBalanceResult": "26292.5500"
}  
[2023-03-23 00:02:42] local.INFO: array (
  'ClientBalanceResult' => '26292.5500',
)  
[2023-03-23 00:02:42] local.DEBUG: lattttef  
[2023-03-23 00:02:42] local.DEBUG: array (
  'ClientBalanceResult' => '26292.5500',
)  
[2023-03-23 00:02:42] local.INFO: transaction14  
[2023-03-23 00:02:42] local.INFO: first inquery phone = 101014529  
[2023-03-23 00:02:44] local.DEBUG: response querySubBalance  
[2023-03-23 00:02:44] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#0.00 B#24-03-2023#0#0##665.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 00:02:44] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '0.00 B',
  4 => '24-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '665.00',
  9 => '4G 250',
)  
[2023-03-23 00:02:44] local.DEBUG: print  before faction by provider price  
[2023-03-23 00:02:44] local.DEBUG: print  after faction by provider price  
[2023-03-23 00:02:44] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-23 00:02:44] local.DEBUG: print1  
[2023-03-23 00:02:44] local.DEBUG: print  2  
[2023-03-23 00:02:45] local.INFO: transaction1  
[2023-03-23 00:02:45] local.INFO: transaction2  
[2023-03-23 00:02:45] local.INFO: transaction3  
[2023-03-23 00:02:45] local.INFO: transaction4  
[2023-03-23 00:02:45] local.INFO: transaction4  
[2023-03-23 00:02:45] local.INFO: transaction5  
[2023-03-23 00:02:45] local.INFO: transaction6  
[2023-03-23 00:02:45] local.INFO: transaction7  
[2023-03-23 00:02:45] local.DEBUG: array (
  'AMT' => 26000.0,
  'CType' => 0,
  'FID' => 89,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '101014529',
  'State' => 0,
  'lateflog' => '561221',
)  
[2023-03-23 00:02:45] local.INFO: transaction8  
[2023-03-23 00:02:45] local.INFO: transaction9  
[2023-03-23 00:02:45] local.INFO: transaction10  
[2023-03-23 00:02:45] local.INFO: transaction11  
[2023-03-23 00:02:45] local.INFO: 12  
[2023-03-23 00:02:45] local.INFO: transaction13  
[2023-03-23 00:02:45] local.INFO: transaction14  
[2023-03-23 00:02:45] local.INFO: transaction19  
[2023-03-23 00:02:45] local.INFO: transaction15  
[2023-03-23 00:02:45] local.INFO: transaction16  
[2023-03-23 00:02:45] local.INFO: 98#101014529#26000.00#0  
[2023-03-23 00:02:54] local.INFO: transaction18  
[2023-03-23 00:02:54] local.INFO: array (
  0 => 'OK',
  1 => '14,624,262.83',
  2 => 'NONE',
  3 => '60872150',
  4 => '26,000.00',
)  
[2023-03-23 00:08:11] local.INFO: header  
[2023-03-23 00:08:11] local.CRITICAL: ****************************1  
[2023-03-23 00:08:11] local.ALERT: reach here  
[2023-03-23 00:08:11] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-23 00:08:11] local.ERROR: المبلغ  
[2023-03-23 00:08:11] local.ERROR: 4,000.00  
[2023-03-23 00:08:11] local.ERROR: مبلغ وقدرة  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 3  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 3  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 3  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 3  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 3  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 3  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 3  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 3  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 3  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 2  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 2  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 2  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 2  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 2  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 2  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 2  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 1  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 1  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 1  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 1  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 1  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 2  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 10013  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 10013  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 10013  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 10013  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 1  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 3  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 3  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 40  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 40  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 40  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 40  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.WARNING: 1  
[2023-03-23 00:08:11] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 200  
[2023-03-23 00:08:11] local.ALERT: 40  
[2023-03-23 00:08:11] local.CRITICAL: ****************************2  
[2023-03-23 00:08:11] local.CRITICAL: ****************************  
[2023-03-23 00:08:11] local.CRITICAL:   
[2023-03-23 00:08:11] local.CRITICAL: ****************************  
[2023-03-23 00:08:11] local.INFO: {
  "ClientBalanceResult": "-69213.6550"
}  
[2023-03-23 00:08:12] local.INFO: array (
  'ClientBalanceResult' => '-69213.6550',
)  
[2023-03-23 00:08:12] local.INFO: price less than Balance  
[2023-03-23 00:08:27] local.INFO: header  
[2023-03-23 00:08:27] local.INFO: header after fliter  
[2023-03-23 00:08:27] local.INFO: Body  after fliter  
[2023-03-23 00:08:27] local.INFO: array (
)  
[2023-03-23 00:08:27] local.INFO: transaction14  
[2023-03-23 00:08:27] local.INFO: first inquery phone = 101908212  
[2023-03-23 00:08:32] local.DEBUG: response querySubBalance  
[2023-03-23 00:08:32] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#0.00 B#15-04-2023#0#0##500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 00:08:32] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '0.00 B',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '500.00',
  9 => '4G 25',
)  
[2023-03-23 00:08:32] local.DEBUG: print  before faction by provider price  
[2023-03-23 00:08:32] local.DEBUG: print  after faction by provider price  
[2023-03-23 00:08:32] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-23 00:08:32] local.DEBUG: print1  
[2023-03-23 00:08:32] local.DEBUG: print  2  
[2023-03-23 00:08:39] local.INFO: header  
[2023-03-23 00:08:39] local.CRITICAL: ****************************1  
[2023-03-23 00:08:39] local.ALERT: reach here  
[2023-03-23 00:08:39] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-23 00:08:39] local.ERROR: المبلغ  
[2023-03-23 00:08:39] local.ERROR: 4,000.00  
[2023-03-23 00:08:39] local.ERROR: مبلغ وقدرة  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 3  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 3  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 3  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 3  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 3  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 3  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 3  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 3  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 3  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 2  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 2  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 2  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 2  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 2  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 2  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 2  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 1  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 1  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 1  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 1  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 1  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 2  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 10013  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 10013  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 10013  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 10013  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 1  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 3  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 3  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 40  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 40  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 40  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 40  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.WARNING: 1  
[2023-03-23 00:08:39] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 200  
[2023-03-23 00:08:39] local.ALERT: 40  
[2023-03-23 00:08:39] local.CRITICAL: ****************************2  
[2023-03-23 00:08:39] local.CRITICAL: ****************************  
[2023-03-23 00:08:39] local.CRITICAL:   
[2023-03-23 00:08:39] local.CRITICAL: ****************************  
[2023-03-23 00:08:40] local.INFO: {
  "ClientBalanceResult": "-69213.6550"
}  
[2023-03-23 00:08:40] local.INFO: array (
  'ClientBalanceResult' => '-69213.6550',
)  
[2023-03-23 00:08:40] local.INFO: price less than Balance  
[2023-03-23 00:09:11] local.INFO: header  
[2023-03-23 00:09:11] local.CRITICAL: ****************************1  
[2023-03-23 00:09:11] local.ALERT: reach here  
[2023-03-23 00:09:11] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-23 00:09:11] local.ERROR: المبلغ  
[2023-03-23 00:09:11] local.ERROR: 4,000.00  
[2023-03-23 00:09:11] local.ERROR: مبلغ وقدرة  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 3  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 3  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 3  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 3  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 3  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 3  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 3  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 3  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 3  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 2  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 2  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 2  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 2  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 2  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 2  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 2  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 1  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 1  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 1  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 1  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 1  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 2  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 10013  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 10013  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 10013  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 10013  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 1  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 3  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 3  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 40  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 40  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 40  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 40  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.WARNING: 1  
[2023-03-23 00:09:11] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 200  
[2023-03-23 00:09:11] local.ALERT: 40  
[2023-03-23 00:09:11] local.CRITICAL: ****************************2  
[2023-03-23 00:09:11] local.CRITICAL: ****************************  
[2023-03-23 00:09:11] local.CRITICAL:   
[2023-03-23 00:09:11] local.CRITICAL: ****************************  
[2023-03-23 00:09:11] local.INFO: {
  "ClientBalanceResult": "-69213.6550"
}  
[2023-03-23 00:09:11] local.INFO: array (
  'ClientBalanceResult' => '-69213.6550',
)  
[2023-03-23 00:09:11] local.INFO: price less than Balance  
[2023-03-23 00:12:54] local.INFO: header  
[2023-03-23 00:12:54] local.INFO: header after fliter  
[2023-03-23 00:12:54] local.INFO: Body  after fliter  
[2023-03-23 00:12:54] local.INFO: array (
)  
[2023-03-23 00:12:54] local.INFO: transaction14  
[2023-03-23 00:12:54] local.INFO: first inquery phone = 103377914  
[2023-03-23 00:12:57] local.DEBUG: response querySubBalance  
[2023-03-23 00:12:57] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#18.48 GB#28-03-2023#0#0##500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 00:12:57] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '18.48 GB',
  4 => '28-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '500.00',
  9 => '4G 25',
)  
[2023-03-23 00:12:57] local.DEBUG: print  before faction by provider price  
[2023-03-23 00:12:57] local.DEBUG: print  after faction by provider price  
[2023-03-23 00:12:57] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-23 00:12:57] local.DEBUG: print1  
[2023-03-23 00:12:57] local.DEBUG: print  2  
[2023-03-23 00:19:41] local.INFO: header  
[2023-03-23 00:19:41] local.INFO: header after fliter  
[2023-03-23 00:19:41] local.INFO: Body  after fliter  
[2023-03-23 00:19:41] local.INFO: array (
)  
[2023-03-23 00:19:41] local.INFO: transaction14  
[2023-03-23 00:19:41] local.INFO: first inquery phone = 106400860  
[2023-03-23 00:19:44] local.DEBUG: response querySubBalance  
[2023-03-23 00:19:44] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#10.01 GB#22-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 00:19:44] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '10.01 GB',
  4 => '22-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-23 00:19:44] local.DEBUG: print  before faction by provider price  
[2023-03-23 00:19:44] local.DEBUG: print  after faction by provider price  
[2023-03-23 00:19:44] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-23 00:19:44] local.DEBUG: print1  
[2023-03-23 00:19:44] local.DEBUG: print  2  
[2023-03-23 00:19:53] local.INFO: header  
[2023-03-23 00:19:53] local.INFO: header after fliter  
[2023-03-23 00:19:53] local.INFO: Body  after fliter  
[2023-03-23 00:19:53] local.INFO: array (
)  
[2023-03-23 00:19:53] local.INFO: transaction14  
[2023-03-23 00:19:53] local.INFO: first inquery phone = 106400976  
[2023-03-23 00:19:56] local.DEBUG: response querySubBalance  
[2023-03-23 00:19:56] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#96.18 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 00:19:56] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '96.18 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-23 00:19:56] local.DEBUG: print  before faction by provider price  
[2023-03-23 00:19:56] local.DEBUG: print  after faction by provider price  
[2023-03-23 00:19:56] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-23 00:19:56] local.DEBUG: print1  
[2023-03-23 00:19:56] local.DEBUG: print  2  
[2023-03-23 00:22:10] local.INFO: header  
[2023-03-23 00:22:10] local.INFO: header after fliter  
[2023-03-23 00:22:10] local.INFO: Body  after fliter  
[2023-03-23 00:22:10] local.INFO: array (
)  
[2023-03-23 00:22:10] local.INFO: transaction14  
[2023-03-23 00:22:10] local.INFO: first inquery phone = 106400598  
[2023-03-23 00:22:13] local.DEBUG: response querySubBalance  
[2023-03-23 00:22:13] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#194.78 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 00:22:13] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '194.78 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-23 00:22:13] local.DEBUG: print  before faction by provider price  
[2023-03-23 00:22:13] local.DEBUG: print  after faction by provider price  
[2023-03-23 00:22:13] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-23 00:22:13] local.DEBUG: print1  
[2023-03-23 00:22:13] local.DEBUG: print  2  
[2023-03-23 00:33:04] local.INFO: header  
[2023-03-23 00:33:04] local.INFO: header after fliter  
[2023-03-23 00:33:04] local.INFO: Body  after fliter  
[2023-03-23 00:33:04] local.INFO: array (
)  
[2023-03-23 00:33:04] local.INFO: transaction14  
[2023-03-23 00:33:04] local.INFO: first inquery phone = 106400598  
[2023-03-23 00:33:16] local.DEBUG: response querySubBalance  
[2023-03-23 00:33:16] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#194.49 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 00:33:16] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '194.49 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-23 00:33:16] local.DEBUG: print  before faction by provider price  
[2023-03-23 00:33:16] local.DEBUG: print  after faction by provider price  
[2023-03-23 00:33:16] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-23 00:33:16] local.DEBUG: print1  
[2023-03-23 00:33:16] local.DEBUG: print  2  
[2023-03-23 00:48:26] local.INFO: header  
[2023-03-23 00:48:26] local.INFO: header after fliter  
[2023-03-23 00:48:26] local.INFO: Body  after fliter  
[2023-03-23 00:48:26] local.INFO: array (
)  
[2023-03-23 00:48:26] local.INFO: transaction14  
[2023-03-23 00:48:26] local.INFO: first inquery phone = 106323109  
[2023-03-23 00:48:29] local.DEBUG: response querySubBalance  
[2023-03-23 00:48:29] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#47.69 GB#15-04-2023#0#0##2500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 00:48:29] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '47.69 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '2500.00',
  9 => '4G 60',
)  
[2023-03-23 00:48:29] local.DEBUG: print  before faction by provider price  
[2023-03-23 00:48:29] local.DEBUG: print  after faction by provider price  
[2023-03-23 00:48:29] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-23 00:48:29] local.DEBUG: print1  
[2023-03-23 00:48:29] local.DEBUG: print  2  
[2023-03-23 01:13:33] local.INFO: header  
[2023-03-23 01:13:33] local.INFO: header after fliter  
[2023-03-23 01:13:33] local.INFO: Body  after fliter  
[2023-03-23 01:13:33] local.INFO: array (
)  
[2023-03-23 01:13:33] local.INFO: transaction14  
[2023-03-23 01:13:33] local.INFO: first inquery phone = 106400860  
[2023-03-23 01:13:37] local.DEBUG: response querySubBalance  
[2023-03-23 01:13:37] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#8.74 GB#22-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 01:13:37] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '8.74 GB',
  4 => '22-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-23 01:13:37] local.DEBUG: print  before faction by provider price  
[2023-03-23 01:13:37] local.DEBUG: print  after faction by provider price  
[2023-03-23 01:13:37] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-23 01:13:37] local.DEBUG: print1  
[2023-03-23 01:13:37] local.DEBUG: print  2  
[2023-03-23 01:14:53] local.INFO: header  
[2023-03-23 01:14:53] local.INFO: header after fliter  
[2023-03-23 01:14:53] local.INFO: Body  after fliter  
[2023-03-23 01:14:53] local.INFO: array (
)  
[2023-03-23 01:14:53] local.INFO: transaction14  
[2023-03-23 01:14:53] local.INFO: first inquery phone = 106400976  
[2023-03-23 01:14:57] local.DEBUG: response querySubBalance  
[2023-03-23 01:14:57] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#94.86 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 01:14:57] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '94.86 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-23 01:14:57] local.DEBUG: print  before faction by provider price  
[2023-03-23 01:14:57] local.DEBUG: print  after faction by provider price  
[2023-03-23 01:14:57] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-23 01:14:57] local.DEBUG: print1  
[2023-03-23 01:14:57] local.DEBUG: print  2  
[2023-03-23 01:15:20] local.INFO: header  
[2023-03-23 01:15:20] local.INFO: header after fliter  
[2023-03-23 01:15:20] local.INFO: Body  after fliter  
[2023-03-23 01:15:20] local.INFO: array (
)  
[2023-03-23 01:15:20] local.INFO: transaction14  
[2023-03-23 01:15:20] local.INFO: first inquery phone = 106400598  
[2023-03-23 01:15:26] local.DEBUG: response querySubBalance  
[2023-03-23 01:15:26] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#193.51 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 01:15:26] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '193.51 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-23 01:15:26] local.DEBUG: print  before faction by provider price  
[2023-03-23 01:15:26] local.DEBUG: print  after faction by provider price  
[2023-03-23 01:15:26] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-23 01:15:26] local.DEBUG: print1  
[2023-03-23 01:15:26] local.DEBUG: print  2  
[2023-03-23 01:41:08] local.INFO: header  
[2023-03-23 01:41:08] local.INFO: header after fliter  
[2023-03-23 01:41:08] local.INFO: Body  after fliter  
[2023-03-23 01:41:08] local.INFO: array (
)  
[2023-03-23 01:41:08] local.INFO: transaction14  
[2023-03-23 01:41:08] local.INFO: first inquery phone = 106400860  
[2023-03-23 01:41:12] local.DEBUG: response querySubBalance  
[2023-03-23 01:41:12] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#8.33 GB#22-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 01:41:12] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '8.33 GB',
  4 => '22-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-23 01:41:12] local.DEBUG: print  before faction by provider price  
[2023-03-23 01:41:12] local.DEBUG: print  after faction by provider price  
[2023-03-23 01:41:12] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-23 01:41:12] local.DEBUG: print1  
[2023-03-23 01:41:12] local.DEBUG: print  2  
[2023-03-23 01:41:19] local.INFO: header  
[2023-03-23 01:41:19] local.INFO: header after fliter  
[2023-03-23 01:41:19] local.INFO: Body  after fliter  
[2023-03-23 01:41:19] local.INFO: array (
)  
[2023-03-23 01:41:19] local.INFO: transaction14  
[2023-03-23 01:41:19] local.INFO: first inquery phone = 106400976  
[2023-03-23 01:41:22] local.DEBUG: response querySubBalance  
[2023-03-23 01:41:22] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#94.35 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 01:41:22] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '94.35 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-23 01:41:22] local.DEBUG: print  before faction by provider price  
[2023-03-23 01:41:23] local.DEBUG: print  after faction by provider price  
[2023-03-23 01:41:23] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-23 01:41:23] local.DEBUG: print1  
[2023-03-23 01:41:23] local.DEBUG: print  2  
[2023-03-23 01:41:54] local.INFO: header  
[2023-03-23 01:41:54] local.INFO: header after fliter  
[2023-03-23 01:41:54] local.INFO: Body  after fliter  
[2023-03-23 01:41:54] local.INFO: array (
)  
[2023-03-23 01:41:54] local.INFO: transaction14  
[2023-03-23 01:41:54] local.INFO: first inquery phone = 106400598  
[2023-03-23 01:41:56] local.DEBUG: response querySubBalance  
[2023-03-23 01:41:56] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#192.29 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 01:41:56] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '192.29 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-23 01:41:56] local.DEBUG: print  before faction by provider price  
[2023-03-23 01:41:56] local.DEBUG: print  after faction by provider price  
[2023-03-23 01:41:56] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-23 01:41:56] local.DEBUG: print1  
[2023-03-23 01:41:56] local.DEBUG: print  2  
[2023-03-23 02:20:58] local.INFO: header  
[2023-03-23 02:20:58] local.INFO: header after fliter  
[2023-03-23 02:20:58] local.INFO: Body  after fliter  
[2023-03-23 02:20:58] local.INFO: array (
)  
[2023-03-23 02:20:58] local.INFO: transaction14  
[2023-03-23 02:20:58] local.INFO: first inquery phone = 106400860  
[2023-03-23 02:21:01] local.DEBUG: response querySubBalance  
[2023-03-23 02:21:01] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#7.45 GB#22-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 02:21:01] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '7.45 GB',
  4 => '22-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-23 02:21:01] local.DEBUG: print  before faction by provider price  
[2023-03-23 02:21:01] local.DEBUG: print  after faction by provider price  
[2023-03-23 02:21:01] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-23 02:21:01] local.DEBUG: print1  
[2023-03-23 02:21:01] local.DEBUG: print  2  
[2023-03-23 02:21:30] local.INFO: header  
[2023-03-23 02:21:30] local.INFO: header after fliter  
[2023-03-23 02:21:30] local.INFO: Body  after fliter  
[2023-03-23 02:21:30] local.INFO: array (
)  
[2023-03-23 02:21:30] local.INFO: transaction14  
[2023-03-23 02:21:30] local.INFO: first inquery phone = 106400976  
[2023-03-23 02:21:32] local.DEBUG: response querySubBalance  
[2023-03-23 02:21:32] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#93.76 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 02:21:32] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '93.76 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-23 02:21:32] local.DEBUG: print  before faction by provider price  
[2023-03-23 02:21:32] local.DEBUG: print  after faction by provider price  
[2023-03-23 02:21:32] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-23 02:21:32] local.DEBUG: print1  
[2023-03-23 02:21:32] local.DEBUG: print  2  
[2023-03-23 02:23:01] local.INFO: header  
[2023-03-23 02:23:01] local.INFO: header after fliter  
[2023-03-23 02:23:01] local.INFO: Body  after fliter  
[2023-03-23 02:23:01] local.INFO: array (
)  
[2023-03-23 02:23:01] local.INFO: transaction14  
[2023-03-23 02:23:01] local.INFO: first inquery phone = 106400598  
[2023-03-23 02:23:03] local.DEBUG: response querySubBalance  
[2023-03-23 02:23:03] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#190.43 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 02:23:03] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '190.43 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-23 02:23:03] local.DEBUG: print  before faction by provider price  
[2023-03-23 02:23:03] local.DEBUG: print  after faction by provider price  
[2023-03-23 02:23:03] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-23 02:23:03] local.DEBUG: print1  
[2023-03-23 02:23:03] local.DEBUG: print  2  
[2023-03-23 06:58:34] local.INFO: header  
[2023-03-23 06:58:34] local.INFO: header after fliter  
[2023-03-23 06:58:34] local.INFO: Body  after fliter  
[2023-03-23 06:58:34] local.INFO: array (
)  
[2023-03-23 06:58:34] local.INFO: transaction14  
[2023-03-23 06:58:34] local.INFO: first inquery phone = 103330532  
[2023-03-23 06:58:46] local.DEBUG: response querySubBalance  
[2023-03-23 06:58:46] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#26.02 GB#18-04-2023#0#0##13265.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 06:58:46] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '26.02 GB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '13265.00',
  9 => '4G 60',
)  
[2023-03-23 06:58:46] local.DEBUG: print  before faction by provider price  
[2023-03-23 06:58:46] local.DEBUG: print  after faction by provider price  
[2023-03-23 06:58:46] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-23 06:58:46] local.DEBUG: print1  
[2023-03-23 06:58:46] local.DEBUG: print  2  
[2023-03-23 06:59:19] local.INFO: header  
[2023-03-23 06:59:19] local.CRITICAL: ****************************1  
[2023-03-23 06:59:19] local.ALERT: reach here  
[2023-03-23 06:59:20] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-23 06:59:20] local.ERROR: المبلغ  
[2023-03-23 06:59:20] local.ERROR: 8,000.00  
[2023-03-23 06:59:20] local.ERROR: مبلغ وقدرة  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 3  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 3  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 3  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 3  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 3  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 3  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 3  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 3  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 3  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 2  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 2  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 2  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 2  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 2  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 2  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 2  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 1  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 1  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 1  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 1  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 1  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 2  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 10013  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 10013  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 10013  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 10013  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 1  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 3  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 3  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 40  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 40  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 40  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 40  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.WARNING: 1  
[2023-03-23 06:59:20] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 200  
[2023-03-23 06:59:20] local.ALERT: 40  
[2023-03-23 06:59:20] local.CRITICAL: ****************************2  
[2023-03-23 06:59:20] local.CRITICAL: ****************************  
[2023-03-23 06:59:20] local.CRITICAL:   
[2023-03-23 06:59:20] local.CRITICAL: ****************************  
[2023-03-23 06:59:21] local.INFO: {
  "ClientBalanceResult": "42725.1538"
}  
[2023-03-23 06:59:21] local.INFO: array (
  'ClientBalanceResult' => '42725.1538',
)  
[2023-03-23 06:59:21] local.DEBUG: lattttef  
[2023-03-23 06:59:21] local.DEBUG: array (
  'ClientBalanceResult' => '42725.1538',
)  
[2023-03-23 06:59:21] local.INFO: transaction14  
[2023-03-23 06:59:21] local.INFO: first inquery phone = 103330532  
[2023-03-23 06:59:23] local.DEBUG: response querySubBalance  
[2023-03-23 06:59:23] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#26.02 GB#18-04-2023#0#0##13265.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 06:59:23] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '26.02 GB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '13265.00',
  9 => '4G 60',
)  
[2023-03-23 06:59:23] local.DEBUG: print  before faction by provider price  
[2023-03-23 06:59:23] local.DEBUG: print  after faction by provider price  
[2023-03-23 06:59:23] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-23 06:59:23] local.DEBUG: print1  
[2023-03-23 06:59:23] local.DEBUG: print  2  
[2023-03-23 06:59:23] local.INFO: transaction1  
[2023-03-23 06:59:23] local.INFO: transaction2  
[2023-03-23 06:59:23] local.INFO: transaction3  
[2023-03-23 06:59:23] local.INFO: transaction4  
[2023-03-23 06:59:23] local.INFO: transaction4  
[2023-03-23 06:59:23] local.INFO: transaction7  
[2023-03-23 06:59:23] local.DEBUG: array (
  'AMT' => 8000.0,
  'CType' => 0,
  'FID' => 87,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103330532',
  'State' => 0,
  'lateflog' => '327014',
)  
[2023-03-23 06:59:23] local.INFO: transaction8  
[2023-03-23 06:59:23] local.INFO: transaction9  
[2023-03-23 06:59:23] local.INFO: transaction10  
[2023-03-23 06:59:23] local.INFO: transaction11  
[2023-03-23 06:59:23] local.INFO: 12  
[2023-03-23 06:59:23] local.INFO: transaction13  
[2023-03-23 06:59:23] local.INFO: transaction14  
[2023-03-23 06:59:23] local.INFO: transaction19  
[2023-03-23 06:59:23] local.INFO: transaction15  
[2023-03-23 06:59:23] local.INFO: transaction16  
[2023-03-23 06:59:23] local.INFO: 98#103330532#8000.00#0  
[2023-03-23 06:59:34] local.INFO: transaction18  
[2023-03-23 06:59:34] local.INFO: array (
  0 => 'OK',
  1 => '14,262,894.83',
  2 => 'NONE',
  3 => '60884053',
  4 => '8,000.00',
)  
[2023-03-23 16:13:24] local.INFO: header  
[2023-03-23 16:13:24] local.INFO: header after fliter  
[2023-03-23 16:13:24] local.INFO: Body  after fliter  
[2023-03-23 16:13:24] local.INFO: array (
)  
[2023-03-23 16:13:24] local.INFO: transaction14  
[2023-03-23 16:13:24] local.INFO: first inquery phone = 103330532  
[2023-03-23 16:13:27] local.DEBUG: response querySubBalance  
[2023-03-23 16:13:27] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#83.76 GB#23-04-2023#0#0##13765.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 16:13:27] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '83.76 GB',
  4 => '23-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '13765.00',
  9 => '4G 60',
)  
[2023-03-23 16:13:27] local.DEBUG: print  before faction by provider price  
[2023-03-23 16:13:28] local.DEBUG: print  after faction by provider price  
[2023-03-23 16:13:28] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-23 16:13:28] local.DEBUG: print1  
[2023-03-23 16:13:28] local.DEBUG: print  2  
[2023-03-23 16:28:46] local.INFO: header  
[2023-03-23 16:28:46] local.INFO: header after fliter  
[2023-03-23 16:28:46] local.INFO: Body  after fliter  
[2023-03-23 16:28:46] local.INFO: array (
)  
[2023-03-23 16:28:46] local.INFO: transaction14  
[2023-03-23 16:28:46] local.INFO: first inquery phone = 101032151  
[2023-03-23 16:28:48] local.DEBUG: response querySubBalance  
[2023-03-23 16:28:48] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#2.27 GB#12-04-2023#0#0##2000.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 16:28:48] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '2.27 GB',
  4 => '12-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '2000.00',
  9 => '4G 25',
)  
[2023-03-23 16:28:48] local.DEBUG: print  before faction by provider price  
[2023-03-23 16:28:48] local.DEBUG: print  after faction by provider price  
[2023-03-23 16:28:48] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-23 16:28:48] local.DEBUG: print1  
[2023-03-23 16:28:48] local.DEBUG: print  2  
[2023-03-23 22:21:58] local.INFO: header  
[2023-03-23 22:21:59] local.INFO: header after fliter  
[2023-03-23 22:21:59] local.INFO: Body  after fliter  
[2023-03-23 22:21:59] local.INFO: array (
)  
[2023-03-23 22:21:59] local.INFO: transaction14  
[2023-03-23 22:21:59] local.INFO: first inquery phone = 106400860  
[2023-03-23 22:22:03] local.DEBUG: response querySubBalance  
[2023-03-23 22:22:03] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#3.33 MB#22-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 22:22:03] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '3.33 MB',
  4 => '22-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-23 22:22:03] local.DEBUG: print  before faction by provider price  
[2023-03-23 22:22:03] local.DEBUG: print  after faction by provider price  
[2023-03-23 22:22:03] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-23 22:22:03] local.DEBUG: print1  
[2023-03-23 22:22:03] local.DEBUG: print  2  
[2023-03-23 22:23:02] local.INFO: header  
[2023-03-23 22:23:02] local.INFO: header after fliter  
[2023-03-23 22:23:02] local.INFO: Body  after fliter  
[2023-03-23 22:23:02] local.INFO: array (
)  
[2023-03-23 22:23:02] local.INFO: transaction14  
[2023-03-23 22:23:02] local.INFO: first inquery phone = 106400976  
[2023-03-23 22:23:04] local.DEBUG: response querySubBalance  
[2023-03-23 22:23:04] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#82.92 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 22:23:04] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '82.92 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-23 22:23:04] local.DEBUG: print  before faction by provider price  
[2023-03-23 22:23:04] local.DEBUG: print  after faction by provider price  
[2023-03-23 22:23:04] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-23 22:23:04] local.DEBUG: print1  
[2023-03-23 22:23:04] local.DEBUG: print  2  
[2023-03-23 22:23:25] local.INFO: header  
[2023-03-23 22:23:25] local.INFO: header after fliter  
[2023-03-23 22:23:25] local.INFO: Body  after fliter  
[2023-03-23 22:23:25] local.INFO: array (
)  
[2023-03-23 22:23:25] local.INFO: transaction14  
[2023-03-23 22:23:25] local.INFO: first inquery phone = 106400598  
[2023-03-23 22:23:27] local.DEBUG: response querySubBalance  
[2023-03-23 22:23:27] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#170.78 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 22:23:27] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '170.78 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-23 22:23:27] local.DEBUG: print  before faction by provider price  
[2023-03-23 22:23:27] local.DEBUG: print  after faction by provider price  
[2023-03-23 22:23:27] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-23 22:23:27] local.DEBUG: print1  
[2023-03-23 22:23:27] local.DEBUG: print  2  
[2023-03-23 23:52:21] local.INFO: header  
[2023-03-23 23:52:21] local.CRITICAL: ****************************1  
[2023-03-23 23:52:21] local.ALERT: reach here  
[2023-03-23 23:52:22] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2023-03-23 23:52:22] local.ERROR: المبلغ  
[2023-03-23 23:52:22] local.ERROR: 2,400.00  
[2023-03-23 23:52:22] local.ERROR: مبلغ وقدرة  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 3  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 3  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 3  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 3  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 3  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 3  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 3  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 3  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 3  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 2  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 2  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 2  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 2  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 2  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 2  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 2  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 1  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 1  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 1  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 1  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 1  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 2  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 10013  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 10013  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 10013  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 10013  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 1  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 3  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 3  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 40  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 40  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 40  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 40  
[2023-03-23 23:52:22] local.WARNING: 1  
[2023-03-23 23:52:22] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 200  
[2023-03-23 23:52:22] local.ALERT: 40  
[2023-03-23 23:52:22] local.CRITICAL: ****************************2  
[2023-03-23 23:52:22] local.CRITICAL: ****************************  
[2023-03-23 23:52:22] local.CRITICAL:   
[2023-03-23 23:52:22] local.CRITICAL: ****************************  
[2023-03-23 23:52:22] local.INFO: {
  "ClientBalanceResult": "6075.0500"
}  
[2023-03-23 23:52:22] local.INFO: array (
  'ClientBalanceResult' => '6075.0500',
)  
[2023-03-23 23:52:22] local.DEBUG: lattttef  
[2023-03-23 23:52:22] local.DEBUG: array (
  'ClientBalanceResult' => '6075.0500',
)  
[2023-03-23 23:52:22] local.INFO: transaction14  
[2023-03-23 23:52:22] local.INFO: first inquery phone = 103377746  
[2023-03-23 23:52:24] local.DEBUG: response querySubBalance  
[2023-03-23 23:52:24] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00##22-03-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-23 23:52:24] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '',
  4 => '22-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-23 23:52:24] local.DEBUG: print  before faction by provider price  
[2023-03-23 23:52:24] local.DEBUG: print  after faction by provider price  
[2023-03-23 23:52:24] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-23 23:52:24] local.DEBUG: print1  
[2023-03-23 23:52:24] local.DEBUG: print  2  
[2023-03-23 23:52:24] local.INFO: transaction1  
[2023-03-23 23:52:24] local.INFO: transaction2  
[2023-03-23 23:52:24] local.INFO: transaction3  
[2023-03-23 23:52:24] local.INFO: transaction4  
[2023-03-23 23:52:24] local.INFO: transaction4  
[2023-03-23 23:52:24] local.INFO: transaction5  
[2023-03-23 23:52:24] local.INFO: transaction6  
[2023-03-23 23:52:24] local.INFO: transaction7  
[2023-03-23 23:52:24] local.DEBUG: array (
  'AMT' => 2400.0,
  'CType' => 0,
  'FID' => 85,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103377746',
  'State' => 0,
  'lateflog' => '584849',
)  
[2023-03-23 23:52:24] local.INFO: transaction8  
[2023-03-23 23:52:25] local.INFO: transaction9  
[2023-03-23 23:52:25] local.INFO: transaction10  
[2023-03-23 23:52:25] local.INFO: transaction11  
[2023-03-23 23:52:25] local.INFO: 12  
[2023-03-23 23:52:25] local.INFO: transaction13  
[2023-03-23 23:52:25] local.INFO: transaction14  
[2023-03-23 23:52:25] local.INFO: transaction19  
[2023-03-23 23:52:25] local.INFO: transaction15  
[2023-03-23 23:52:25] local.INFO: transaction16  
[2023-03-23 23:52:25] local.INFO: 98#103377746#2400.00#0  
[2023-03-23 23:52:34] local.INFO: transaction18  
[2023-03-23 23:52:34] local.INFO: array (
  0 => 'OK',
  1 => '12,965,781.83',
  2 => 'NONE',
  3 => '60956247',
  4 => '2,400.00',
)  
