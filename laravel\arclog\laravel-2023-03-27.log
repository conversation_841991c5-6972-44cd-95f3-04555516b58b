[2023-03-27 00:37:34] local.INFO: header  
[2023-03-27 00:37:34] local.INFO: header after fliter  
[2023-03-27 00:37:34] local.INFO: Body  after fliter  
[2023-03-27 00:37:34] local.INFO: array (
)  
[2023-03-27 00:37:34] local.INFO: transaction14  
[2023-03-27 00:37:34] local.INFO: first inquery phone = 106400860  
[2023-03-27 00:37:36] local.DEBUG: response querySubBalance  
[2023-03-27 00:37:36] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#804.49 MB#25-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 00:37:36] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '804.49 MB',
  4 => '25-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-27 00:37:36] local.DEBUG: print  before faction by provider price  
[2023-03-27 00:37:36] local.DEBUG: print  after faction by provider price  
[2023-03-27 00:37:36] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-27 00:37:36] local.DEBUG: print1  
[2023-03-27 00:37:36] local.DEBUG: print  2  
[2023-03-27 01:37:51] local.INFO: header  
[2023-03-27 01:37:51] local.INFO: header after fliter  
[2023-03-27 01:37:51] local.INFO: Body  after fliter  
[2023-03-27 01:37:51] local.INFO: array (
)  
[2023-03-27 01:37:51] local.INFO: transaction14  
[2023-03-27 01:37:51] local.INFO: first inquery phone = 106400860  
[2023-03-27 01:37:56] local.DEBUG: response querySubBalance  
[2023-03-27 01:37:56] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#4.49 MB#25-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 01:37:56] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '4.49 MB',
  4 => '25-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-27 01:37:56] local.DEBUG: print  before faction by provider price  
[2023-03-27 01:37:56] local.DEBUG: print  after faction by provider price  
[2023-03-27 01:37:56] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-27 01:37:56] local.DEBUG: print1  
[2023-03-27 01:37:56] local.DEBUG: print  2  
[2023-03-27 01:40:32] local.INFO: header  
[2023-03-27 01:40:32] local.INFO: header after fliter  
[2023-03-27 01:40:32] local.INFO: Body  after fliter  
[2023-03-27 01:40:32] local.INFO: array (
)  
[2023-03-27 01:40:32] local.INFO: transaction14  
[2023-03-27 01:40:32] local.INFO: first inquery phone = 106400976  
[2023-03-27 01:40:36] local.DEBUG: response querySubBalance  
[2023-03-27 01:40:36] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#7.88 GB#15-04-2023#0#0##10501.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 01:40:36] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '7.88 GB',
  4 => '15-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10501.00',
  9 => '4G 250',
)  
[2023-03-27 01:40:36] local.DEBUG: print  before faction by provider price  
[2023-03-27 01:40:36] local.DEBUG: print  after faction by provider price  
[2023-03-27 01:40:36] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-27 01:40:36] local.DEBUG: print1  
[2023-03-27 01:40:36] local.DEBUG: print  2  
[2023-03-27 01:43:17] local.INFO: header  
[2023-03-27 01:43:17] local.INFO: header after fliter  
[2023-03-27 01:43:17] local.INFO: Body  after fliter  
[2023-03-27 01:43:17] local.INFO: array (
)  
[2023-03-27 01:43:17] local.INFO: transaction14  
[2023-03-27 01:43:17] local.INFO: first inquery phone = 106400860  
[2023-03-27 01:43:23] local.DEBUG: response querySubBalance  
[2023-03-27 01:43:23] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#14.90 GB#27-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 01:43:23] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '14.90 GB',
  4 => '27-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-27 01:43:23] local.DEBUG: print  before faction by provider price  
[2023-03-27 01:43:23] local.DEBUG: print  after faction by provider price  
[2023-03-27 01:43:23] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-27 01:43:23] local.DEBUG: print1  
[2023-03-27 01:43:23] local.DEBUG: print  2  
[2023-03-27 01:43:56] local.INFO: header  
[2023-03-27 01:43:56] local.INFO: header after fliter  
[2023-03-27 01:43:56] local.INFO: Body  after fliter  
[2023-03-27 01:43:56] local.INFO: array (
)  
[2023-03-27 01:43:56] local.INFO: transaction14  
[2023-03-27 01:43:56] local.INFO: first inquery phone = 106400860  
[2023-03-27 01:44:00] local.DEBUG: response querySubBalance  
[2023-03-27 01:44:00] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#14.85 GB#27-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 01:44:00] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '14.85 GB',
  4 => '27-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-27 01:44:00] local.DEBUG: print  before faction by provider price  
[2023-03-27 01:44:01] local.DEBUG: print  after faction by provider price  
[2023-03-27 01:44:01] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-27 01:44:01] local.DEBUG: print1  
[2023-03-27 01:44:01] local.DEBUG: print  2  
[2023-03-27 02:00:27] local.INFO: header  
[2023-03-27 02:00:27] local.INFO: header after fliter  
[2023-03-27 02:00:27] local.INFO: Body  after fliter  
[2023-03-27 02:00:27] local.INFO: array (
)  
[2023-03-27 02:00:27] local.INFO: transaction14  
[2023-03-27 02:00:27] local.INFO: first inquery phone = 101034508  
[2023-03-27 02:00:32] local.DEBUG: response querySubBalance  
[2023-03-27 02:00:32] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#25.11 GB#11-04-2023#0#0##1500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 02:00:32] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '25.11 GB',
  4 => '11-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1500.00',
  9 => '4G 25',
)  
[2023-03-27 02:00:32] local.DEBUG: print  before faction by provider price  
[2023-03-27 02:00:32] local.DEBUG: print  after faction by provider price  
[2023-03-27 02:00:32] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-27 02:00:32] local.DEBUG: print1  
[2023-03-27 02:00:32] local.DEBUG: print  2  
[2023-03-27 02:02:08] local.INFO: header  
[2023-03-27 02:02:08] local.CRITICAL: ****************************1  
[2023-03-27 02:02:08] local.ALERT: reach here  
[2023-03-27 02:02:08] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-27 02:02:08] local.ERROR: المبلغ  
[2023-03-27 02:02:08] local.ERROR: 4,000.00  
[2023-03-27 02:02:08] local.ERROR: مبلغ وقدرة  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 3  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 3  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 3  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 3  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 3  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 3  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 3  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 3  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 3  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 2  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 2  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 2  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 2  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 2  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 2  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 2  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 1  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 1  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 1  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 1  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 1  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 2  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 10013  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 10013  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 10013  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 10013  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 1  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 3  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 3  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 40  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 40  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 40  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 40  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.WARNING: 1  
[2023-03-27 02:02:08] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 200  
[2023-03-27 02:02:08] local.ALERT: 40  
[2023-03-27 02:02:08] local.CRITICAL: ****************************2  
[2023-03-27 02:02:08] local.CRITICAL: ****************************  
[2023-03-27 02:02:08] local.CRITICAL:   
[2023-03-27 02:02:08] local.CRITICAL: ****************************  
[2023-03-27 02:02:08] local.INFO: {
  "ClientBalanceResult": "22264.9300"
}  
[2023-03-27 02:02:08] local.INFO: array (
  'ClientBalanceResult' => '22264.9300',
)  
[2023-03-27 02:02:08] local.DEBUG: lattttef  
[2023-03-27 02:02:08] local.DEBUG: array (
  'ClientBalanceResult' => '22264.9300',
)  
[2023-03-27 02:02:08] local.INFO: transaction14  
[2023-03-27 02:02:08] local.INFO: first inquery phone = 103344681  
[2023-03-27 02:02:12] local.DEBUG: response querySubBalance  
[2023-03-27 02:02:12] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#0.00 B#21-04-2023#0#0##2500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 02:02:12] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '0.00 B',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '2500.00',
  9 => '4G 25',
)  
[2023-03-27 02:02:12] local.DEBUG: print  before faction by provider price  
[2023-03-27 02:02:12] local.DEBUG: print  after faction by provider price  
[2023-03-27 02:02:12] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-27 02:02:12] local.DEBUG: print1  
[2023-03-27 02:02:12] local.DEBUG: print  2  
[2023-03-27 02:02:12] local.INFO: transaction1  
[2023-03-27 02:02:12] local.INFO: transaction2  
[2023-03-27 02:02:12] local.INFO: transaction3  
[2023-03-27 02:02:12] local.INFO: transaction4  
[2023-03-27 02:02:12] local.INFO: transaction4  
[2023-03-27 02:02:12] local.INFO: transaction5  
[2023-03-27 02:02:12] local.INFO: transaction6  
[2023-03-27 02:02:12] local.INFO: transaction7  
[2023-03-27 02:02:12] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103344681',
  'State' => 0,
  'lateflog' => '588030',
)  
[2023-03-27 02:02:12] local.INFO: transaction8  
[2023-03-27 02:02:12] local.INFO: transaction9  
[2023-03-27 02:02:12] local.INFO: transaction10  
[2023-03-27 02:02:12] local.INFO: transaction11  
[2023-03-27 02:02:12] local.INFO: 12  
[2023-03-27 02:02:12] local.INFO: transaction13  
[2023-03-27 02:02:12] local.INFO: transaction14  
[2023-03-27 02:02:12] local.INFO: transaction19  
[2023-03-27 02:02:12] local.INFO: transaction15  
[2023-03-27 02:02:12] local.INFO: transaction16  
[2023-03-27 02:02:12] local.INFO: 98#103344681#4000.00#0  
[2023-03-27 02:02:20] local.INFO: transaction18  
[2023-03-27 02:02:20] local.INFO: array (
  0 => 'OK',
  1 => '7,023,952.83',
  2 => 'NONE',
  3 => '61273844',
  4 => '4,000.00',
)  
[2023-03-27 03:17:00] local.INFO: header  
[2023-03-27 03:17:00] local.CRITICAL: ****************************1  
[2023-03-27 03:17:00] local.ALERT: reach here  
[2023-03-27 03:17:00] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2023-03-27 03:17:00] local.ERROR: المبلغ  
[2023-03-27 03:17:00] local.ERROR: 2,400.00  
[2023-03-27 03:17:00] local.ERROR: مبلغ وقدرة  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 3  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 3  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 3  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 3  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 3  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 3  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 3  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 3  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 3  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 2  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 2  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 2  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 2  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 2  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 2  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 2  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 1  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 1  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 1  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 1  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 1  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 2  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 10013  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 10013  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 10013  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 10013  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 1  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 3  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 3  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 40  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 40  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 40  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 40  
[2023-03-27 03:17:00] local.WARNING: 1  
[2023-03-27 03:17:00] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 200  
[2023-03-27 03:17:00] local.ALERT: 40  
[2023-03-27 03:17:00] local.CRITICAL: ****************************2  
[2023-03-27 03:17:00] local.CRITICAL: ****************************  
[2023-03-27 03:17:00] local.CRITICAL:   
[2023-03-27 03:17:00] local.CRITICAL: ****************************  
[2023-03-27 03:17:01] local.INFO: {
  "ClientBalanceResult": "12966.1500"
}  
[2023-03-27 03:17:01] local.INFO: array (
  'ClientBalanceResult' => '12966.1500',
)  
[2023-03-27 03:17:01] local.DEBUG: lattttef  
[2023-03-27 03:17:01] local.DEBUG: array (
  'ClientBalanceResult' => '12966.1500',
)  
[2023-03-27 03:17:01] local.INFO: transaction14  
[2023-03-27 03:17:01] local.INFO: first inquery phone = 103323097  
[2023-03-27 03:17:05] local.DEBUG: response querySubBalance  
[2023-03-27 03:17:05] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#0.00 B#25-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 03:17:05] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '0.00 B',
  4 => '25-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-27 03:17:05] local.DEBUG: print  before faction by provider price  
[2023-03-27 03:17:05] local.DEBUG: print  after faction by provider price  
[2023-03-27 03:17:05] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-27 03:17:05] local.DEBUG: print1  
[2023-03-27 03:17:05] local.DEBUG: print  2  
[2023-03-27 03:17:05] local.INFO: transaction1  
[2023-03-27 03:17:05] local.INFO: transaction2  
[2023-03-27 03:17:05] local.INFO: transaction3  
[2023-03-27 03:17:05] local.INFO: transaction4  
[2023-03-27 03:17:05] local.INFO: transaction4  
[2023-03-27 03:17:05] local.INFO: transaction5  
[2023-03-27 03:17:05] local.INFO: transaction6  
[2023-03-27 03:17:05] local.INFO: transaction7  
[2023-03-27 03:17:05] local.DEBUG: array (
  'AMT' => 2400.0,
  'CType' => 0,
  'FID' => 85,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103323097',
  'State' => 0,
  'lateflog' => '509760',
)  
[2023-03-27 03:17:05] local.INFO: transaction8  
[2023-03-27 03:17:05] local.INFO: transaction9  
[2023-03-27 03:17:05] local.INFO: transaction10  
[2023-03-27 03:17:05] local.INFO: transaction11  
[2023-03-27 03:17:05] local.INFO: 12  
[2023-03-27 03:17:05] local.INFO: transaction13  
[2023-03-27 03:17:05] local.INFO: transaction14  
[2023-03-27 03:17:05] local.INFO: transaction19  
[2023-03-27 03:17:05] local.INFO: transaction15  
[2023-03-27 03:17:05] local.INFO: transaction16  
[2023-03-27 03:17:05] local.INFO: 98#103323097#2400.00#0  
[2023-03-27 03:17:17] local.INFO: transaction18  
[2023-03-27 03:17:17] local.INFO: array (
  0 => 'OK',
  1 => '6,905,119.83',
  2 => 'NONE',
  3 => '61282513',
  4 => '2,400.00',
)  
[2023-03-27 19:26:39] local.INFO: header  
[2023-03-27 19:26:39] local.CRITICAL: ****************************1  
[2023-03-27 19:26:39] local.ALERT: reach here  
[2023-03-27 19:26:39] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '6000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ستة ألف  ر.ي.',
  ),
)  
[2023-03-27 19:26:39] local.ERROR: المبلغ  
[2023-03-27 19:26:39] local.ERROR: 6,000.00  
[2023-03-27 19:26:39] local.ERROR: مبلغ وقدرة  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 3  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 3  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 3  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 3  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 3  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 3  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 3  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 3  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 3  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 2  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 2  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 2  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 2  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 2  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 2  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 2  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 1  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 1  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 1  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 1  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 1  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 2  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 10013  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 10013  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 10013  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 10013  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 1  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 3  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 3  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.WARNING: 1  
[2023-03-27 19:26:39] local.WARNING: array (
  'ID' => 82,
  'Name' => 'باقة 40جيجا 6000 ريال',
  'ServiceID' => 40,
  'Price' => 6000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '6000',
  'PersonnalPrice' => 6000.0,
)  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 200  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 200  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 200  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 200  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 200  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 200  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.ALERT: 40  
[2023-03-27 19:26:39] local.CRITICAL: ****************************2  
[2023-03-27 19:26:39] local.CRITICAL: ****************************  
[2023-03-27 19:26:39] local.CRITICAL:   
[2023-03-27 19:26:39] local.CRITICAL: ****************************  
[2023-03-27 19:26:40] local.INFO: {
  "ClientBalanceResult": "215771.4367"
}  
[2023-03-27 19:26:40] local.INFO: array (
  'ClientBalanceResult' => '215771.4367',
)  
[2023-03-27 19:26:40] local.DEBUG: lattttef  
[2023-03-27 19:26:40] local.DEBUG: array (
  'ClientBalanceResult' => '215771.4367',
)  
[2023-03-27 19:26:40] local.INFO: transaction1  
[2023-03-27 19:26:40] local.INFO: transaction2  
[2023-03-27 19:26:40] local.INFO: transaction3  
[2023-03-27 19:26:40] local.INFO: transaction4  
[2023-03-27 19:26:40] local.INFO: transaction4  
[2023-03-27 19:26:40] local.INFO: transaction5  
[2023-03-27 19:26:40] local.INFO: transaction6  
[2023-03-27 19:26:40] local.INFO: transaction7  
[2023-03-27 19:26:40] local.DEBUG: array (
  'AMT' => 6000.0,
  'CType' => 0,
  'FID' => 82,
  'LType' => '1',
  'SID' => 40,
  'SNO' => '798768305',
  'State' => 0,
  'lateflog' => '70760',
)  
[2023-03-27 19:26:40] local.INFO: transaction8  
[2023-03-27 19:26:41] local.INFO: transaction9  
[2023-03-27 19:26:41] local.INFO: transaction10  
[2023-03-27 19:26:41] local.INFO: transaction11  
[2023-03-27 19:26:41] local.INFO: 12  
[2023-03-27 19:26:41] local.INFO: transaction13  
[2023-03-27 19:26:41] local.INFO: transaction14  
[2023-03-27 19:26:41] local.INFO: transaction19  
[2023-03-27 19:26:41] local.INFO: transaction15  
[2023-03-27 19:26:49] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '6030',
  'remainAmount' => 26872823,
  'mallrem' => -23127177,
  'transid' => '4409333',
  'ref_id' => 26141712,
)  
[2023-03-27 19:34:59] local.INFO: header  
[2023-03-27 19:34:59] local.INFO: header after fliter  
[2023-03-27 19:34:59] local.INFO: Body  after fliter  
[2023-03-27 19:34:59] local.INFO: array (
)  
[2023-03-27 19:34:59] local.INFO: transaction14  
[2023-03-27 19:34:59] local.INFO: first inquery phone = 103322124  
[2023-03-27 19:35:02] local.DEBUG: response querySubBalance  
[2023-03-27 19:35:02] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#4.33 MB#17-04-2023#0#0##1852.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 19:35:02] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '4.33 MB',
  4 => '17-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1852.00',
  9 => '4G 25',
)  
[2023-03-27 19:35:02] local.DEBUG: print  before faction by provider price  
[2023-03-27 19:35:02] local.DEBUG: print  after faction by provider price  
[2023-03-27 19:35:02] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-27 19:35:02] local.DEBUG: print1  
[2023-03-27 19:35:02] local.DEBUG: print  2  
[2023-03-27 19:35:15] local.INFO: header  
[2023-03-27 19:35:15] local.CRITICAL: ****************************1  
[2023-03-27 19:35:15] local.ALERT: reach here  
[2023-03-27 19:35:15] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-27 19:35:15] local.ERROR: المبلغ  
[2023-03-27 19:35:15] local.ERROR: 4,000.00  
[2023-03-27 19:35:15] local.ERROR: مبلغ وقدرة  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 3  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 3  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 3  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 3  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 3  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 3  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 3  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 3  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 3  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 2  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 2  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 2  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 2  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 2  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 2  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 2  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 1  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 1  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 1  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 1  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 1  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 2  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 10013  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 10013  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 10013  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 10013  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 1  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 3  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 3  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 40  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 40  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 40  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 40  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.WARNING: 1  
[2023-03-27 19:35:15] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 200  
[2023-03-27 19:35:15] local.ALERT: 40  
[2023-03-27 19:35:15] local.CRITICAL: ****************************2  
[2023-03-27 19:35:15] local.CRITICAL: ****************************  
[2023-03-27 19:35:15] local.CRITICAL:   
[2023-03-27 19:35:15] local.CRITICAL: ****************************  
[2023-03-27 19:35:16] local.INFO: {
  "ClientBalanceResult": "283874.0700"
}  
[2023-03-27 19:35:16] local.INFO: array (
  'ClientBalanceResult' => '283874.0700',
)  
[2023-03-27 19:35:16] local.DEBUG: lattttef  
[2023-03-27 19:35:16] local.DEBUG: array (
  'ClientBalanceResult' => '283874.0700',
)  
[2023-03-27 19:35:16] local.INFO: transaction14  
[2023-03-27 19:35:16] local.INFO: first inquery phone = 103322124  
[2023-03-27 19:35:20] local.DEBUG: response querySubBalance  
[2023-03-27 19:35:20] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#4.33 MB#17-04-2023#0#0##1852.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 19:35:20] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '4.33 MB',
  4 => '17-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1852.00',
  9 => '4G 25',
)  
[2023-03-27 19:35:20] local.DEBUG: print  before faction by provider price  
[2023-03-27 19:35:20] local.DEBUG: print  after faction by provider price  
[2023-03-27 19:35:20] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-27 19:35:20] local.DEBUG: print1  
[2023-03-27 19:35:20] local.DEBUG: print  2  
[2023-03-27 19:35:20] local.INFO: transaction1  
[2023-03-27 19:35:20] local.INFO: transaction2  
[2023-03-27 19:35:20] local.INFO: transaction3  
[2023-03-27 19:35:20] local.INFO: transaction4  
[2023-03-27 19:35:20] local.INFO: transaction4  
[2023-03-27 19:35:20] local.INFO: transaction5  
[2023-03-27 19:35:20] local.INFO: transaction6  
[2023-03-27 19:35:20] local.INFO: transaction7  
[2023-03-27 19:35:20] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103322124',
  'State' => 0,
  'lateflog' => '337620',
)  
[2023-03-27 19:35:20] local.INFO: transaction8  
[2023-03-27 19:35:21] local.INFO: transaction9  
[2023-03-27 19:35:21] local.INFO: transaction10  
[2023-03-27 19:35:21] local.INFO: transaction11  
[2023-03-27 19:35:21] local.INFO: 12  
[2023-03-27 19:35:21] local.INFO: transaction13  
[2023-03-27 19:35:21] local.INFO: transaction14  
[2023-03-27 19:35:21] local.INFO: transaction19  
[2023-03-27 19:35:21] local.INFO: transaction15  
[2023-03-27 19:35:21] local.INFO: transaction16  
[2023-03-27 19:35:21] local.INFO: 98#103322124#4000.00#0  
[2023-03-27 19:35:33] local.INFO: transaction18  
[2023-03-27 19:35:33] local.INFO: array (
  0 => 'OK',
  1 => '6,294,873.83',
  2 => 'NONE',
  3 => '61327468',
  4 => '4,000.00',
)  
[2023-03-27 19:36:18] local.INFO: header  
[2023-03-27 19:36:18] local.INFO: header after fliter  
[2023-03-27 19:36:18] local.INFO: Body  after fliter  
[2023-03-27 19:36:18] local.INFO: array (
)  
[2023-03-27 19:36:18] local.INFO: transaction14  
[2023-03-27 19:36:18] local.INFO: first inquery phone = 103322124  
[2023-03-27 19:36:21] local.DEBUG: response querySubBalance  
[2023-03-27 19:36:21] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#25.00 GB#27-04-2023#0#0##2352.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 19:36:21] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '25.00 GB',
  4 => '27-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '2352.00',
  9 => '4G 25',
)  
[2023-03-27 19:36:21] local.DEBUG: print  before faction by provider price  
[2023-03-27 19:36:21] local.DEBUG: print  after faction by provider price  
[2023-03-27 19:36:21] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-27 19:36:21] local.DEBUG: print1  
[2023-03-27 19:36:21] local.DEBUG: print  2  
[2023-03-27 21:07:50] local.INFO: header  
[2023-03-27 21:07:50] local.INFO: header after fliter  
[2023-03-27 21:07:50] local.INFO: Body  after fliter  
[2023-03-27 21:07:50] local.INFO: array (
)  
[2023-03-27 21:07:50] local.INFO: transaction14  
[2023-03-27 21:07:50] local.INFO: first inquery phone = 106400363  
[2023-03-27 21:07:57] local.DEBUG: response querySubBalance  
[2023-03-27 21:07:57] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#24.46 GB#27-04-2023#0#0##9000.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 21:07:57] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '24.46 GB',
  4 => '27-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9000.00',
  9 => '4G 25',
)  
[2023-03-27 21:07:57] local.DEBUG: print  before faction by provider price  
[2023-03-27 21:07:57] local.DEBUG: print  after faction by provider price  
[2023-03-27 21:07:57] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-27 21:07:57] local.DEBUG: print1  
[2023-03-27 21:07:57] local.DEBUG: print  2  
[2023-03-27 21:27:08] local.INFO: header  
[2023-03-27 21:27:09] local.INFO: header after fliter  
[2023-03-27 21:27:09] local.INFO: Body  after fliter  
[2023-03-27 21:27:09] local.INFO: array (
)  
[2023-03-27 21:27:09] local.INFO: transaction14  
[2023-03-27 21:27:09] local.INFO: first inquery phone = *********  
[2023-03-27 21:27:11] local.DEBUG: response querySubBalance  
[2023-03-27 21:27:11] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#4.46 MB#24-04-2023#0#0##10822.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 21:27:11] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '4.46 MB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10822.00',
  9 => '4G 25',
)  
[2023-03-27 21:27:11] local.DEBUG: print  before faction by provider price  
[2023-03-27 21:27:12] local.DEBUG: print  after faction by provider price  
[2023-03-27 21:27:12] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-27 21:27:12] local.DEBUG: print1  
[2023-03-27 21:27:12] local.DEBUG: print  2  
[2023-03-27 21:27:20] local.INFO: header  
[2023-03-27 21:27:20] local.CRITICAL: ****************************1  
[2023-03-27 21:27:20] local.ALERT: reach here  
[2023-03-27 21:27:20] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-27 21:27:20] local.ERROR: المبلغ  
[2023-03-27 21:27:20] local.ERROR: 4,000.00  
[2023-03-27 21:27:20] local.ERROR: مبلغ وقدرة  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 3  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 3  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 3  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 3  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 3  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 3  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 3  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 3  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 3  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 2  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 2  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 2  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 2  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 2  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 2  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 2  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 1  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 1  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 1  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 1  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 1  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 2  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 10013  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 10013  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 10013  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 10013  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 1  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 3  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 3  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 40  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 40  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 40  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 40  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.WARNING: 1  
[2023-03-27 21:27:20] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 200  
[2023-03-27 21:27:20] local.ALERT: 40  
[2023-03-27 21:27:20] local.CRITICAL: ****************************2  
[2023-03-27 21:27:20] local.CRITICAL: ****************************  
[2023-03-27 21:27:20] local.CRITICAL:   
[2023-03-27 21:27:20] local.CRITICAL: ****************************  
[2023-03-27 21:27:21] local.INFO: {
  "ClientBalanceResult": "279874.0700"
}  
[2023-03-27 21:27:21] local.INFO: array (
  'ClientBalanceResult' => '279874.0700',
)  
[2023-03-27 21:27:21] local.DEBUG: lattttef  
[2023-03-27 21:27:21] local.DEBUG: array (
  'ClientBalanceResult' => '279874.0700',
)  
[2023-03-27 21:27:21] local.INFO: transaction14  
[2023-03-27 21:27:21] local.INFO: first inquery phone = *********  
[2023-03-27 21:27:23] local.DEBUG: response querySubBalance  
[2023-03-27 21:27:23] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#4.46 MB#24-04-2023#0#0##10822.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 21:27:23] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '4.46 MB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10822.00',
  9 => '4G 25',
)  
[2023-03-27 21:27:23] local.DEBUG: print  before faction by provider price  
[2023-03-27 21:27:23] local.DEBUG: print  after faction by provider price  
[2023-03-27 21:27:23] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-27 21:27:23] local.DEBUG: print1  
[2023-03-27 21:27:23] local.DEBUG: print  2  
[2023-03-27 21:27:23] local.INFO: transaction1  
[2023-03-27 21:27:23] local.INFO: transaction2  
[2023-03-27 21:27:23] local.INFO: transaction3  
[2023-03-27 21:27:23] local.INFO: transaction4  
[2023-03-27 21:27:23] local.INFO: transaction4  
[2023-03-27 21:27:23] local.INFO: transaction5  
[2023-03-27 21:27:23] local.INFO: transaction6  
[2023-03-27 21:27:23] local.INFO: transaction7  
[2023-03-27 21:27:23] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '337620',
)  
[2023-03-27 21:27:23] local.INFO: transaction8  
[2023-03-27 21:27:24] local.INFO: transaction9  
[2023-03-27 21:27:24] local.INFO: transaction10  
[2023-03-27 21:27:24] local.INFO: transaction11  
[2023-03-27 21:27:24] local.INFO: 12  
[2023-03-27 21:27:24] local.INFO: transaction13  
[2023-03-27 21:27:24] local.INFO: transaction14  
[2023-03-27 21:27:24] local.INFO: transaction19  
[2023-03-27 21:27:24] local.INFO: transaction15  
[2023-03-27 21:27:24] local.INFO: transaction16  
[2023-03-27 21:27:24] local.INFO: 98#*********#4000.00#0  
[2023-03-27 21:27:34] local.DEBUG: {"Number":********,"ServiceID":200,"NetworkID":null,"SubscriberNumber":"*********","Amount":4000,"FactionID":86,"RegionID":null,"LineType":"\u063a\u064a\u0631 \u0645\u0639\u0631\u0648\u0641","Date":"2023-03-27 21:27:24","Status":2,"Note":null,"CreditorAccountID":379015,"CurrencyID":1,"DebitorAccountID":"337620","AgentID":null,"RefNumber":null,"TransactionID":********,"ProviderID":10028,"EntryID":4410420,"PaymentEntryID":null,"Channel":2,"CreatedBy":"335360","BranchBy":null,"CreatedTime":"2023-03-27 21:27:24","BranchID":"1","ProviderRM":"","ProviderPrice":"4000.00","SubNote":null,"Datestamb":"********","UniqueNo":"**************","Quantity":"4000.0000","UnitPrice":1,"UnitCost":1,"CostAmount":"4000.00","DifferentialAmount":0,"CommissionAmount":0,"Discount":0,"TotalCost":4000,"TotalAmount":4000,"Profits":0,"Method":2,"Type":0,"Class":0,"LType":"1","OperatorID":1,"AppTechApi":0,"BillNumber":"20000********","BillState":0,"Debited":1,"ByChild":0,"IsDirect":1,"BundleName":"\u0641\u0626\u0629 25 \u062c\u064a\u062c\u0627 4000 \u0631\u064a\u0627\u0644","BundleCode":"30","ExCode":null,"TransNumber":"**************","OperationID":0,"AccountID":"337620","State":0,"StateClass":"","Identifier":"b388e55bb399bca5","AdminNote":"","AccountNote":"","Description":null,"Responded":0,"RequestInfo":"200#*********#4000#86","ResponseTime":"2023-03-27 21:27:24","ResponseStatus":0,"ExecutionPeroid":"24","FaildRequest":0,"FailedReason":null,"FailedType":0,"Cured":0,"CuredBy":null,"CuredInfo":null,"InspectInfo":null,"Flag":2,"Action":0,"QuotaionID":0,"SyncID":0,"id":4233254}  
[2023-03-27 21:28:35] local.INFO: header  
[2023-03-27 21:28:35] local.INFO: header after fliter  
[2023-03-27 21:28:35] local.INFO: Body  after fliter  
[2023-03-27 21:28:35] local.INFO: array (
)  
[2023-03-27 21:28:35] local.INFO: transaction14  
[2023-03-27 21:28:35] local.INFO: first inquery phone = *********  
[2023-03-27 21:28:37] local.DEBUG: response querySubBalance  
[2023-03-27 21:28:37] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#4.46 MB#24-04-2023#0#0##10822.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 21:28:37] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '4.46 MB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10822.00',
  9 => '4G 25',
)  
[2023-03-27 21:28:37] local.DEBUG: print  before faction by provider price  
[2023-03-27 21:28:37] local.DEBUG: print  after faction by provider price  
[2023-03-27 21:28:37] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-27 21:28:37] local.DEBUG: print1  
[2023-03-27 21:28:37] local.DEBUG: print  2  
[2023-03-27 21:29:15] local.INFO: header  
[2023-03-27 21:29:16] local.CRITICAL: ****************************1  
[2023-03-27 21:29:16] local.ALERT: reach here  
[2023-03-27 21:29:16] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-27 21:29:16] local.ERROR: المبلغ  
[2023-03-27 21:29:16] local.ERROR: 4,000.00  
[2023-03-27 21:29:16] local.ERROR: مبلغ وقدرة  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 3  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 3  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 3  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 3  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 3  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 3  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 3  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 3  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 3  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 2  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 2  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 2  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 2  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 2  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 2  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 2  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 1  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 1  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 1  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 1  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 1  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 2  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 10013  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 10013  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 10013  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 10013  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 1  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 3  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 3  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 40  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 40  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 40  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 40  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.WARNING: 1  
[2023-03-27 21:29:16] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 200  
[2023-03-27 21:29:16] local.ALERT: 40  
[2023-03-27 21:29:16] local.CRITICAL: ****************************2  
[2023-03-27 21:29:16] local.CRITICAL: ****************************  
[2023-03-27 21:29:16] local.CRITICAL:   
[2023-03-27 21:29:16] local.CRITICAL: ****************************  
[2023-03-27 21:29:17] local.INFO: {
  "ClientBalanceResult": "279874.0700"
}  
[2023-03-27 21:29:17] local.INFO: array (
  'ClientBalanceResult' => '279874.0700',
)  
[2023-03-27 21:29:17] local.DEBUG: lattttef  
[2023-03-27 21:29:17] local.DEBUG: array (
  'ClientBalanceResult' => '279874.0700',
)  
[2023-03-27 21:29:17] local.INFO: transaction14  
[2023-03-27 21:29:17] local.INFO: first inquery phone = *********  
[2023-03-27 21:29:21] local.DEBUG: response querySubBalance  
[2023-03-27 21:29:21] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#4.46 MB#24-04-2023#0#0##10822.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 21:29:21] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '4.46 MB',
  4 => '24-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '10822.00',
  9 => '4G 25',
)  
[2023-03-27 21:29:21] local.DEBUG: print  before faction by provider price  
[2023-03-27 21:29:21] local.DEBUG: print  after faction by provider price  
[2023-03-27 21:29:21] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-27 21:29:21] local.DEBUG: print1  
[2023-03-27 21:29:21] local.DEBUG: print  2  
[2023-03-27 21:29:21] local.INFO: transaction1  
[2023-03-27 21:29:21] local.INFO: transaction2  
[2023-03-27 21:29:21] local.INFO: transaction3  
[2023-03-27 21:29:21] local.INFO: transaction4  
[2023-03-27 21:29:21] local.INFO: transaction4  
[2023-03-27 21:29:21] local.INFO: transaction5  
[2023-03-27 21:29:21] local.INFO: transaction6  
[2023-03-27 21:29:21] local.INFO: transaction7  
[2023-03-27 21:29:21] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '337620',
)  
[2023-03-27 21:29:21] local.INFO: transaction8  
[2023-03-27 21:29:21] local.INFO: transaction9  
[2023-03-27 21:29:21] local.INFO: transaction10  
[2023-03-27 21:29:21] local.INFO: transaction11  
[2023-03-27 21:29:21] local.INFO: 12  
[2023-03-27 21:29:21] local.INFO: transaction13  
[2023-03-27 21:29:21] local.INFO: transaction14  
[2023-03-27 21:29:21] local.INFO: transaction19  
[2023-03-27 21:29:21] local.INFO: transaction15  
[2023-03-27 21:29:21] local.INFO: transaction16  
[2023-03-27 21:29:21] local.INFO: 98#*********#4000.00#0  
[2023-03-27 21:29:35] local.INFO: transaction18  
[2023-03-27 21:29:35] local.INFO: array (
  0 => 'OK',
  1 => '5,984,713.83',
  2 => 'NONE',
  3 => '61346906',
  4 => '4,000.00',
)  
[2023-03-27 22:20:30] local.INFO: header  
[2023-03-27 22:20:30] local.INFO: header after fliter  
[2023-03-27 22:20:30] local.INFO: Body  after fliter  
[2023-03-27 22:20:30] local.INFO: array (
)  
[2023-03-27 22:20:30] local.INFO: transaction14  
[2023-03-27 22:20:30] local.INFO: first inquery phone = 103366845  
[2023-03-27 22:20:34] local.DEBUG: response querySubBalance  
[2023-03-27 22:20:34] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#0.00 B#11-04-2023#0#0##506.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 22:20:34] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '0.00 B',
  4 => '11-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '506.00',
  9 => '4G 25',
)  
[2023-03-27 22:20:34] local.DEBUG: print  before faction by provider price  
[2023-03-27 22:20:34] local.DEBUG: print  after faction by provider price  
[2023-03-27 22:20:34] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-27 22:20:34] local.DEBUG: print1  
[2023-03-27 22:20:34] local.DEBUG: print  2  
[2023-03-27 22:21:13] local.INFO: header  
[2023-03-27 22:21:13] local.CRITICAL: ****************************1  
[2023-03-27 22:21:13] local.ALERT: reach here  
[2023-03-27 22:21:13] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-27 22:21:13] local.ERROR: المبلغ  
[2023-03-27 22:21:13] local.ERROR: 4,000.00  
[2023-03-27 22:21:13] local.ERROR: مبلغ وقدرة  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 3  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 3  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 3  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 3  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 3  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 3  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 3  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 3  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 3  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 2  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 2  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 2  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 2  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 2  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 2  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 2  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 1  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 1  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 1  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 1  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 1  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 2  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 10013  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 10013  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 10013  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 10013  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 1  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 3  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 3  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 40  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 40  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 40  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 40  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.WARNING: 1  
[2023-03-27 22:21:13] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 200  
[2023-03-27 22:21:13] local.ALERT: 40  
[2023-03-27 22:21:13] local.CRITICAL: ****************************2  
[2023-03-27 22:21:13] local.CRITICAL: ****************************  
[2023-03-27 22:21:13] local.CRITICAL:   
[2023-03-27 22:21:13] local.CRITICAL: ****************************  
[2023-03-27 22:21:13] local.INFO: {
  "ClientBalanceResult": "45566.3700"
}  
[2023-03-27 22:21:13] local.INFO: array (
  'ClientBalanceResult' => '45566.3700',
)  
[2023-03-27 22:21:13] local.DEBUG: lattttef  
[2023-03-27 22:21:13] local.DEBUG: array (
  'ClientBalanceResult' => '45566.3700',
)  
[2023-03-27 22:21:13] local.INFO: transaction14  
[2023-03-27 22:21:13] local.INFO: first inquery phone = 103366845  
[2023-03-27 22:21:15] local.DEBUG: response querySubBalance  
[2023-03-27 22:21:15] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#0.00 B#11-04-2023#0#0##506.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 22:21:15] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '0.00 B',
  4 => '11-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '506.00',
  9 => '4G 25',
)  
[2023-03-27 22:21:15] local.DEBUG: print  before faction by provider price  
[2023-03-27 22:21:15] local.DEBUG: print  after faction by provider price  
[2023-03-27 22:21:15] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-27 22:21:15] local.DEBUG: print1  
[2023-03-27 22:21:15] local.DEBUG: print  2  
[2023-03-27 22:21:15] local.INFO: transaction1  
[2023-03-27 22:21:15] local.INFO: transaction2  
[2023-03-27 22:21:15] local.INFO: transaction3  
[2023-03-27 22:21:15] local.INFO: transaction4  
[2023-03-27 22:21:15] local.INFO: transaction4  
[2023-03-27 22:21:15] local.INFO: transaction5  
[2023-03-27 22:21:15] local.INFO: transaction6  
[2023-03-27 22:21:15] local.INFO: transaction7  
[2023-03-27 22:21:15] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103366845',
  'State' => 0,
  'lateflog' => '222600',
)  
[2023-03-27 22:21:15] local.INFO: transaction8  
[2023-03-27 22:21:15] local.INFO: transaction9  
[2023-03-27 22:21:15] local.INFO: transaction10  
[2023-03-27 22:21:15] local.INFO: transaction11  
[2023-03-27 22:21:15] local.INFO: 12  
[2023-03-27 22:21:15] local.INFO: transaction13  
[2023-03-27 22:21:15] local.INFO: transaction14  
[2023-03-27 22:21:15] local.INFO: transaction19  
[2023-03-27 22:21:15] local.INFO: transaction15  
[2023-03-27 22:21:15] local.INFO: transaction16  
[2023-03-27 22:21:15] local.INFO: 98#103366845#4000.00#0  
[2023-03-27 22:21:25] local.INFO: transaction18  
[2023-03-27 22:21:25] local.INFO: array (
  0 => 'OK',
  1 => '5,738,468.83',
  2 => 'NONE',
  3 => '61357847',
  4 => '4,000.00',
)  
[2023-03-27 23:18:22] local.INFO: header  
[2023-03-27 23:18:22] local.CRITICAL: ****************************1  
[2023-03-27 23:18:22] local.ALERT: reach here  
[2023-03-27 23:18:22] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2023-03-27 23:18:22] local.ERROR: المبلغ  
[2023-03-27 23:18:22] local.ERROR: 2,400.00  
[2023-03-27 23:18:22] local.ERROR: مبلغ وقدرة  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 3  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 3  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 3  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 3  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 3  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 3  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 3  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 3  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 3  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 2  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 2  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 2  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 2  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 2  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 2  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 2  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 1  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 1  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 1  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 1  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 1  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 2  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 10013  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 10013  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 10013  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 10013  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 1  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 3  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 3  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 40  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 40  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 40  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 40  
[2023-03-27 23:18:22] local.WARNING: 1  
[2023-03-27 23:18:22] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 200  
[2023-03-27 23:18:22] local.ALERT: 40  
[2023-03-27 23:18:22] local.CRITICAL: ****************************2  
[2023-03-27 23:18:22] local.CRITICAL: ****************************  
[2023-03-27 23:18:22] local.CRITICAL:   
[2023-03-27 23:18:22] local.CRITICAL: ****************************  
[2023-03-27 23:18:22] local.INFO: {
  "ClientBalanceResult": "171358.7150"
}  
[2023-03-27 23:18:22] local.INFO: array (
  'ClientBalanceResult' => '171358.7150',
)  
[2023-03-27 23:18:22] local.DEBUG: lattttef  
[2023-03-27 23:18:22] local.DEBUG: array (
  'ClientBalanceResult' => '171358.7150',
)  
[2023-03-27 23:18:22] local.INFO: transaction14  
[2023-03-27 23:18:22] local.INFO: first inquery phone = 101013261  
[2023-03-27 23:18:26] local.DEBUG: response querySubBalance  
[2023-03-27 23:18:26] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#0.00 B#01-04-2023#0#0##500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 23:18:26] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '0.00 B',
  4 => '01-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '500.00',
  9 => '4G 60',
)  
[2023-03-27 23:18:26] local.DEBUG: print  before faction by provider price  
[2023-03-27 23:18:26] local.DEBUG: print  after faction by provider price  
[2023-03-27 23:18:26] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-27 23:18:26] local.DEBUG: print1  
[2023-03-27 23:18:26] local.DEBUG: print  2  
[2023-03-27 23:20:51] local.INFO: header  
[2023-03-27 23:20:51] local.CRITICAL: ****************************1  
[2023-03-27 23:20:51] local.ALERT: reach here  
[2023-03-27 23:20:51] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '8000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ثمانية ألف  ر.ي.',
  ),
)  
[2023-03-27 23:20:51] local.ERROR: المبلغ  
[2023-03-27 23:20:51] local.ERROR: 8,000.00  
[2023-03-27 23:20:51] local.ERROR: مبلغ وقدرة  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 3  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 3  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 3  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 3  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 3  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 3  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 3  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 3  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 3  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 2  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 2  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 2  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 2  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 2  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 2  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 2  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:51] local.ALERT: 1  
[2023-03-27 23:20:51] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 1  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 1  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 1  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 1  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 2  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 10013  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 10013  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 10013  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 10013  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 1  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 3  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 3  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 40  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 40  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 40  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 40  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.WARNING: 1  
[2023-03-27 23:20:52] local.WARNING: array (
  'ID' => 87,
  'Name' => 'فئة 60 جيجا 8000 ريال',
  'ServiceID' => 200,
  'Price' => 8000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 8000.0,
)  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 200  
[2023-03-27 23:20:52] local.ALERT: 40  
[2023-03-27 23:20:52] local.CRITICAL: ****************************2  
[2023-03-27 23:20:52] local.CRITICAL: ****************************  
[2023-03-27 23:20:52] local.CRITICAL:   
[2023-03-27 23:20:52] local.CRITICAL: ****************************  
[2023-03-27 23:20:52] local.INFO: {
  "ClientBalanceResult": "171358.7150"
}  
[2023-03-27 23:20:52] local.INFO: array (
  'ClientBalanceResult' => '171358.7150',
)  
[2023-03-27 23:20:52] local.DEBUG: lattttef  
[2023-03-27 23:20:52] local.DEBUG: array (
  'ClientBalanceResult' => '171358.7150',
)  
[2023-03-27 23:20:52] local.INFO: transaction14  
[2023-03-27 23:20:52] local.INFO: first inquery phone = 101013261  
[2023-03-27 23:20:58] local.DEBUG: response querySubBalance  
[2023-03-27 23:20:58] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#0.00 B#01-04-2023#0#0##500.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 23:20:58] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '0.00 B',
  4 => '01-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '500.00',
  9 => '4G 60',
)  
[2023-03-27 23:20:58] local.DEBUG: print  before faction by provider price  
[2023-03-27 23:20:58] local.DEBUG: print  after faction by provider price  
[2023-03-27 23:20:58] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-27 23:20:58] local.DEBUG: print1  
[2023-03-27 23:20:58] local.DEBUG: print  2  
[2023-03-27 23:20:58] local.INFO: transaction1  
[2023-03-27 23:20:58] local.INFO: transaction2  
[2023-03-27 23:20:58] local.INFO: transaction3  
[2023-03-27 23:20:58] local.INFO: transaction4  
[2023-03-27 23:20:58] local.INFO: transaction4  
[2023-03-27 23:20:58] local.INFO: transaction7  
[2023-03-27 23:20:58] local.DEBUG: array (
  'AMT' => 8000.0,
  'CType' => 0,
  'FID' => 87,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '101013261',
  'State' => 0,
  'lateflog' => '368822',
)  
[2023-03-27 23:20:58] local.INFO: transaction8  
[2023-03-27 23:20:59] local.INFO: transaction9  
[2023-03-27 23:20:59] local.INFO: transaction10  
[2023-03-27 23:20:59] local.INFO: transaction11  
[2023-03-27 23:20:59] local.INFO: 12  
[2023-03-27 23:20:59] local.INFO: transaction13  
[2023-03-27 23:20:59] local.INFO: transaction14  
[2023-03-27 23:20:59] local.INFO: transaction19  
[2023-03-27 23:20:59] local.INFO: transaction15  
[2023-03-27 23:20:59] local.INFO: transaction16  
[2023-03-27 23:20:59] local.INFO: 98#101013261#8000.00#0  
[2023-03-27 23:21:17] local.INFO: transaction18  
[2023-03-27 23:21:17] local.INFO: array (
  0 => 'OK',
  1 => '5,536,675.83',
  2 => 'NONE',
  3 => '61373190',
  4 => '8,000.00',
)  
[2023-03-27 23:41:04] local.INFO: header  
[2023-03-27 23:41:04] local.INFO: header after fliter  
[2023-03-27 23:41:04] local.INFO: Body  after fliter  
[2023-03-27 23:41:04] local.INFO: array (
)  
[2023-03-27 23:41:04] local.INFO: transaction14  
[2023-03-27 23:41:04] local.INFO: first inquery phone = 106400860  
[2023-03-27 23:41:07] local.DEBUG: response querySubBalance  
[2023-03-27 23:41:07] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#10.00 GB#27-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 23:41:07] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '10.00 GB',
  4 => '27-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-27 23:41:07] local.DEBUG: print  before faction by provider price  
[2023-03-27 23:41:07] local.DEBUG: print  after faction by provider price  
[2023-03-27 23:41:07] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-27 23:41:07] local.DEBUG: print1  
[2023-03-27 23:41:07] local.DEBUG: print  2  
[2023-03-27 23:41:23] local.INFO: header  
[2023-03-27 23:41:23] local.CRITICAL: ****************************1  
[2023-03-27 23:41:23] local.ALERT: reach here  
[2023-03-27 23:41:23] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2023-03-27 23:41:23] local.ERROR: المبلغ  
[2023-03-27 23:41:23] local.ERROR: 2,400.00  
[2023-03-27 23:41:23] local.ERROR: مبلغ وقدرة  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 3  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 3  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 3  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 3  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 3  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 3  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 3  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 3  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 3  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 2  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 2  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 2  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 2  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 2  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 2  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 2  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 1  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 1  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 1  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 1  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 1  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 2  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 10013  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 10013  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 10013  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 10013  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 1  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 3  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 3  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 40  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 40  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 40  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 40  
[2023-03-27 23:41:23] local.WARNING: 1  
[2023-03-27 23:41:23] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 200  
[2023-03-27 23:41:23] local.ALERT: 40  
[2023-03-27 23:41:23] local.CRITICAL: ****************************2  
[2023-03-27 23:41:23] local.CRITICAL: ****************************  
[2023-03-27 23:41:23] local.CRITICAL:   
[2023-03-27 23:41:23] local.CRITICAL: ****************************  
[2023-03-27 23:41:23] local.INFO: {
  "ClientBalanceResult": "51.6550"
}  
[2023-03-27 23:41:23] local.INFO: array (
  'ClientBalanceResult' => '51.6550',
)  
[2023-03-27 23:41:23] local.INFO: price less than Balance  
[2023-03-27 23:41:45] local.INFO: header  
[2023-03-27 23:41:45] local.INFO: header after fliter  
[2023-03-27 23:41:45] local.INFO: Body  after fliter  
[2023-03-27 23:41:45] local.INFO: array (
)  
[2023-03-27 23:41:45] local.INFO: transaction14  
[2023-03-27 23:41:45] local.INFO: first inquery phone = 106400363  
[2023-03-27 23:41:48] local.DEBUG: response querySubBalance  
[2023-03-27 23:41:48] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#19.92 GB#27-04-2023#0#0##9000.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 23:41:48] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '19.92 GB',
  4 => '27-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9000.00',
  9 => '4G 25',
)  
[2023-03-27 23:41:48] local.DEBUG: print  before faction by provider price  
[2023-03-27 23:41:48] local.DEBUG: print  after faction by provider price  
[2023-03-27 23:41:48] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-27 23:41:48] local.DEBUG: print1  
[2023-03-27 23:41:48] local.DEBUG: print  2  
[2023-03-27 23:42:49] local.INFO: header  
[2023-03-27 23:42:49] local.INFO: header after fliter  
[2023-03-27 23:42:49] local.INFO: Body  after fliter  
[2023-03-27 23:42:49] local.INFO: array (
)  
[2023-03-27 23:42:49] local.INFO: transaction14  
[2023-03-27 23:42:49] local.INFO: first inquery phone = 106400598  
[2023-03-27 23:42:51] local.DEBUG: response querySubBalance  
[2023-03-27 23:42:51] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#60.11 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 23:42:51] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '60.11 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-27 23:42:51] local.DEBUG: print  before faction by provider price  
[2023-03-27 23:42:51] local.DEBUG: print  after faction by provider price  
[2023-03-27 23:42:51] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-27 23:42:51] local.DEBUG: print1  
[2023-03-27 23:42:51] local.DEBUG: print  2  
[2023-03-27 23:44:55] local.INFO: header  
[2023-03-27 23:44:55] local.INFO: header after fliter  
[2023-03-27 23:44:55] local.INFO: Body  after fliter  
[2023-03-27 23:44:55] local.INFO: array (
)  
[2023-03-27 23:44:55] local.INFO: transaction14  
[2023-03-27 23:44:55] local.INFO: first inquery phone = 106400598  
[2023-03-27 23:45:00] local.DEBUG: response querySubBalance  
[2023-03-27 23:45:00] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#60.07 GB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-27 23:45:00] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '60.07 GB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-27 23:45:00] local.DEBUG: print  before faction by provider price  
[2023-03-27 23:45:00] local.DEBUG: print  after faction by provider price  
[2023-03-27 23:45:00] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-27 23:45:00] local.DEBUG: print1  
[2023-03-27 23:45:00] local.DEBUG: print  2  
