<?php

namespace App\Http\Controllers;

namespace App\Repositories;
use App\Models\Account;
use App\Models\AccountUser;
use App\Models\Agent;
use App\Models\ApiTransaction;
use App\Models\Client;
use App\Models\Faction;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Topup;
use App\Models\Voucher;
use App\Services\NetCoinService;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;
use App\Services\TwasulService;

class TopupRepository
{
    public $header;
    public $body;
    public $journal;
    public $topup;
    public $accountUser;
    public $client;

    public function setHeader($header): self
    {
        $this->header = $header;
        return $this;
    }
    public function setBody($body): self
    {
        $this->body = $body;
        return $this;
    }

    public function __construct()
    {
    }

   public function inquery($phone)
    {
        $netCoin= new NetCoinService($this->topup,$this->journal);
        $confrm=$netCoin->inquery($phone);
        return $confrm;

    }

    //

    public function checkUser()
    {

        $response = Http::withHeaders(
            $this->header
        )
            ->get('http://abuosama.ddns.net:7652/PaymentService/api/v1/balance/'.$this->body['lateflog']);
            Log::info($response);

        if (($response->ok() || $response->successful()) && $response->json('ClientBalanceResult')) {
 $faction = Faction::where('ServiceID', $this->body['SID'])->where('ID',$this->body['FID'])->first();
            Log::info($response->json());            
					

			$AccountSlating=DB::table ('AccountSlating') ->where('AccountID',$this->body['lateflog'])->where('CurrencyID',1)->first();
			if(($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT']) > $response->json('ClientBalanceResult') ){


			            
		Log::info("price less than Balance");
                throw ValidationException::withMessages([
                    'user' => ['price less than Balance'],
                ]);
             }elseif($AccountSlating?($AccountSlating->Amount + ($response->json('ClientBalanceResult')))<(($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT'])):false ){
                         
		Log::info("price less than Balance");
        throw ValidationException::withMessages([
            'user' => ['price less than Balance'],
        ]);
             }else{
                return $response->json();
             }
        } else {
			Log::info("ttttttttttf");
            throw ValidationException::withMessages([
                'user' => ['try again'],
            ]);
        }
    }

    public function checkFaction()
    {
        Log::alert('reach here');
        $GetTopupQuota = Http::withHeaders(
            $this->header
        )->post('http://abuosama.ddns.net:7652/PaymentService/api/v1/GetTopupQuota',
        [
            'Amount'=>$this->body["AMT"],
            "FactionID"=>$this->body["FID"],
            "LType"=>$this->body["LType"],
            "NetworkID"=>0,
            "ServiceID"=>$this->body['SID'],
            "State"=>0
        ]);
        Log::error($GetTopupQuota->json()['Items']);
        foreach($GetTopupQuota->json()['Items'] as $data){
            Log::error($data['Key']);
            if($data['Key']=='المبلغ'){
                Log::error( (number_format(floatval($data['Value']),2)));

                if( (number_format(floatval($data['Value']),2))!=(number_format(floatval($this->body['AMT']),2))){
                    Log::error( (number_format(floatval($this->body['AMT']),2)));

                    throw ValidationException::withMessages([
                        'try' => ["فشل في تنفيذ العملية"],
                    ]);
            }

        }
    }
      //  $GetTopupQuota
        $response = Http::withHeaders(
            $this->header
        )->post('http://abuosama.ddns.net:7652/PaymentService/api/v1/CacheList',
        [
            'CacheInfo'=>[]
        ]);

        // Log::warning($response->json()['CacheData'][3]); 
        if (($response->ok() || $response->successful())  && $response->json('CacheData') !=Null ) {
            $jj=  $response->collect(); 

         //   $jj=json_decode($response->getContent(),true);
        //    Log::alert($jj);
            //Log::alert('reach here2');
          //  Log::alert($jj);

          
          //  explode()
        //    implode('here wtire the text',$fList)
           if (count($jj)>=3){

            $fList =json_decode($jj["CacheData"][3],true);
           // Log::alert($fList[0]);
         //  Log::alert(count($fList));

          foreach($fList as $data)
           {
                 if($this->body['FID']==$data["ID"] && $this->body['SID']==$data["ServiceID"] )
                 {
                    if(($data["Price"]>$data["PersonnalPrice"]?$data["Price"]:$data["PersonnalPrice"])<=$this->body["AMT"]){
                        log::warning(1);
                      
                    }else{
                        log::warning(0);
                        throw ValidationException::withMessages([
                            'user' => ['try again'],
                        ]);                    }
                    log::warning($data);
                 }
               log::alert($this->body['SID']);
                log::alert($data["ServiceID"]);
              
                //$collec=collect($data);
                      //  log::alert((string) $data);
                // log::alert( $data ,Str::contains("\"ServiceID\":"));
                // if($this->body['FID']!=Null && $this->body['SID']!=Null && $data['ServiceID'] !=Null)
                // {
                    
                //     if($this->body['FID']==$data['id'])
                //     {
                //         log::warning($data);
                //     }
                    
                // }

       }
        
			            
		
      }
     //   Log::alert('reach here4');
        
    }}

    public function checkBalance(Request $request)
    {


        // $header['authorization'] = "Bearer " . $token->token;
        //http://abuosama.ddns.net:9802/api/v1/CheckYmLoan/1/1'

        // 'http://abuosama.ddns.net:7652/PaymentService/api/v1/balance/587445',

        //         > POST /PaymentService/api/v1/ExecuteTopup HTTP/1.1

        // working

        //  dd($this->header);
        $response = Http::withHeaders(
            [$this->header]
        )
            ->get(
                'http://abuosama.ddns.net:7652/PaymentService/api/v1/balance/587445'
            );
        //
        dd($response->json());
    }

    public function transaction(Request $request)
    {
        // here transaction and validate
        DB::transaction(function ()  {
        Log::info("transaction1");
        //User
        $this->accountUser = AccountUser::where('AccountID', $this->body['lateflog'])->first();
		Log::info("transaction2");
        $account = $this->accountUser->account()->first();
		Log::info("transaction3");
        $userInfo = $this->accountUser->userInfo()->first();
		Log::info("transaction4");

        $this->client = Client::where('AccountID', $this->body['lateflog'])->first();
		Log::info("transaction4");
        //Parent
 $agent = null;
            $AgentAccount = null;
            $tAccountID= $this->accountUser->AccountID;
            $tByChild=0;
if( $this->client){
    $tAccountID= $this->client->ParentAccountID ?? $this->client->AccountID ;
    $tByChild=$this->client->ParentAccountID ? 1 : 0;
        $agent=Agent::where("ID",$this->client->AgentID)->first();
		Log::info("transaction5");
        // dd($agent);
        $AgentAccount=$agent ?  Account::where("ID",$agent->AccountID)->first() :  null ;
		Log::info("transaction6");
}
        $faction = Faction::where('ServiceID', $this->body['SID'])->where('ID',$this->body['FID'])->first();
		Log::info("transaction7");


        // $parentAccountUser = AccountUser::where('UserID',$this->accountUser->ParentID)->first();

        // $parentUserInfo = $parentAccountUser->userInfo()->first() ?? null; //check
        // $parentAccount = $parentAccountUser->account()->first() ?? null; //check

       // get provider by serviec
       $provider=2;
       Log::debug($this->body);

$apiTransaction = ApiTransaction::create([
            'Number'=>date('YmdHis')  ,
            'UserID'=> $userInfo->ID,
            'UserType'=>$userInfo->Type,
            'Identifier'=>$this->header['latefidentifier'],
            'TransactionID'=>date('YmdHis'),
            'Channel'=>2,
            'VC'=>5,
            'SessionID'=>0,
            'SessionNo'=>null,
            'EntityName'=>"Topup",
            'ApiType'=> "Execute",
            'DeviceInfo'=> json_encode($this->body),
            'UserAgent'=>$this->header['user-agent'],
            'Host'=>'abuosama.ddns.net:9802',
            'IpAddress'=>'0.0.0.0',
            'Type'=>0,
            'Status'=>0,
            'Location'=>null,
            'Note'=>null,
            'CreatedTime'=>date('Y-m-d H:i:s')
        ]);
Log::info("transaction8");

        // $voucher = Voucher::create([

        //     'Name' => "dddd",
        //     'Entity' => 1,
        //     'Note' => "ddd",
        //     'CreatedBy' => 1,
        //     'BranchID' => 1,
        //     'CreatedTime' => date('Y-m-d H:i:s'),
        //     'Active' => 1,
        //     'CrOrDr' => 1,
        //     'Module' => "11"


        // ]);



        $this->journal = Journal::create([
            'VoucherID' => 7,
            'Number' => Journal::max('Number')+1,
            'EntryID' => 00 ,
            'Date' => date('Y-m-d H:i:s'),
            'CreatedBy' => $userInfo->ID,
            'Debited' => 1, // pending
            'CreatedTime' => date('Y-m-d H:i:s'),
            'BranchID' =>  $this->accountUser->BranchID,
            // 'Year' => null,
            'Status' => 1 , // pending
            'TotalAmount' => null,
            'SyncJournalID' => null,
            'datestamb' => ((int) date('Ymd')),
            'EntrySource' => 1,
            'Depended' => 0,
            'RefNumber' => null,
            'CurrencyID' => null
        ]);
 Log::info("transaction9");
        $journalEntry1 = JournalEntry::create([
            'ParentID' => $this->journal->id,
            'Amount' =>-1* ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT']),
            'CurrencyID' => 1,
            'DCAmount' =>-1* ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT']),
            'AccountID' => $account->ID,
            'CostCenterID' => null,
            'Note' => (in_array($faction->ServiceID,[40]) ? 'عدن نت' : 'يمن فورجي').$this->body['SNO'],
            'Datestamp' =>((int) date('Ymd')),
            'ExchangeRate' => 1.00,
            'SyncEntryID' => null
        ]);
Log::info("transaction10");

        $journalEntry2 = JournalEntry::create([
            'ParentID' => $this->journal->id,
            'Amount' => $faction->ProviderPrice,
            'CurrencyID' => 1,
            'DCAmount' => $faction->ProviderPrice,
            'AccountID' =>in_array($faction->ServiceID,[40])  ? 316581 : 379015,
            'CostCenterID' => null,
            'Note' => (in_array($faction->ServiceID,[40])  ? 'عدن نت' : 'يمن فورجي').$this->body['SNO'],
            'Datestamp' =>((int) date('Ymd')),
            'ExchangeRate' => 1.00,
            'SyncEntryID' => null
        ]);
		Log::info("transaction11");
        $acounter=($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT']) -$faction->ProviderPrice;
        if($acounter>0){

            $journalEntry3= JournalEntry::create([
                'ParentID' => $this->journal->id,
                'Amount' => $acounter,
                'CurrencyID' => 1,
                'DCAmount' => $acounter,
                'AccountID' =>  222839  ,
                'CostCenterID' => null,
                'Note' => 'فارق تحصيل عملية رقم'.$journalEntry1->ID,
                'Datestamp' =>((int) date('Ymd')),
                'ExchangeRate' => 1.00,
                'SyncEntryID' => null
            ]);

        }
	//	Log::info("".this->client? $this->client->ParentAccountID ? 1 : 0:0 );
        //  dd($faction->ServiceID.$Journal->Number);
        Log::info("12");
        $this->topup = Topup::create([
            'Number' =>$this->journal->Number,
            'ServiceID' => $this->body['SID'],
            'NetworkID' => null,
            'SubscriberNumber' => $this->body['SNO'],
            'Amount' => $this->body['AMT'],
            'FactionID' => $this->body['FID'],
            'RegionID' => null, // default
            'LineType' => 'غير معروف', // default
            'Date' => date('Y-m-d H:i:s'),
            // 'Year' => null,
            'Status' => 2,   // 0 faild  1 complited  // 2 hold // 5 time out // 6 reverse
            'Note' => null,
            'CreditorAccountID' => in_array($faction->ServiceID,[40])  ? 316581: 379015, // default  , // default
            'CurrencyID' => 1,
            'DebitorAccountID' =>$this->accountUser->AccountID ,
            'AgentID' => null,
            'RefNumber' => null,
            'TransactionID' => $this->journal->Number,
            'ProviderID' => in_array($faction->ServiceID,[40])  ? 10018: 10028,
            'EntryID' => $this->journal->id,
            'PaymentEntryID' => null,
            'Channel' => 2,
            'CreatedBy' => $userInfo->ID ,
            'BranchBy' => null,
            'CreatedTime' =>  date('Y-m-d H:i:s'),
            // 'RowVersion' => null,
            'BranchID' => $this->accountUser->BranchID,
            'ProviderRM' => '',
            'ProviderPrice' => $faction->ProviderPrice,
            'SubNote' => null,
            'Datestamb' => date('Ymd'),
            'UniqueNo' =>  date('YmdHis'),
            'Quantity' => $faction->Quantity,
            'UnitPrice' => ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT'])/$faction->Quantity,
            'UnitCost' => $faction->ProviderPrice/$faction->Quantity,
            'CostAmount' => $faction->ProviderPrice,
            'DifferentialAmount' => 0.00,
            'CommissionAmount' => 0.00,
            'Discount' => 0.00,
            'TotalCost' =>  ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT']),
            'TotalAmount' =>  ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT']),
            'Profits' => ($faction->Price > $this->body['AMT'] ? $faction->Price : $this->body['AMT'])-$faction->ProviderPrice,
            'Method' => 2, // default
            'Type' => 0, // default
            'Class' => 0, // default
            'LType' => $this->body['LType'], // default
            'OperatorID' => 1, // default
            'AppTechApi' => 0, // default
            'BillNumber' => $this->body['SID'].'00'.$this->journal->Number,
            'BillState' => 0, // default
            'Debited' => 1, // default
            'ByChild' =>$tByChild,//this->client?( $this->client->ParentAccountID ? 1 : 0):0, // default
            'IsDirect' => 1, // default
            'BundleName' => $faction->Name, // default
            'BundleCode' => $faction->ProviderCode, // default
            'ExCode' => null, // default
            'TransNumber' => date('YmdHis'), // default
            'OperationID' => 0, // default
            'AccountID' =>$tAccountID,//this->client?($this->client->ParentAccountID ?? $this->client->AccountID ):$this->accountUser->AccountID,
            'State' => 0,
            'StateClass' => '', // default
            'Identifier' => $this->header['latefidentifier'], // default
            'AdminNote' => '', // default
            'AccountNote' => '', // default
            'Description' => null, // default
            'Responded' => 0, // default
            'RequestInfo' => $this->body['SID'].'#'.$this->body['SNO'].'#'.$this->body['AMT'].'#'.$this->body['FID'], // default
            // 'ResponseInfo' => 2, // default
            'ResponseTime' =>date('Y-m-d H:i:s') ,
            'ResponseStatus' => 0, // default
            // 'ResponseReference' => 0, // default
            'ExecutionPeroid' => date('s'), // default
            'FaildRequest' => 0, // default
            'FailedReason' => NULL, // default
            'FailedType' => 0, // default
            'Cured' => 0, // default
            'CuredBy' => NULL, // default
            'CuredInfo' => NULL, // default
            'InspectInfo' =>NULL,
            'Flag' => 2,
            'Action' => 0, // default
            'QuotaionID' => 0, // default
            'SyncID' =>0 // default
        ]);
		Log::info("transaction13");
 // change to journal  Debited and Status
    });
    if($this->body['SID']==40){
        $twasulService= new TwasulService($this->topup,$this->journal);
        $confrm=$twasulService->confirm();
        
        return $confrm;
    }
    else {
        $netCoin= new NetCoinService($this->topup,$this->journal);
        $confrm=$netCoin->confirm();
        return $confrm;
    }

    }
}
