[2025-07-13 15:11:02] local.INFO: header  
[2025-07-13 15:11:02] local.CRITICAL: ****************************1  
[2025-07-13 15:11:02] local.ALERT: reach here  
[2025-07-13 15:11:02] local.ALERT: array (
  'AMT' => 2400.0,
  'CType' => 0,
  'FID' => 85,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '106448604',
  'State' => 0,
  'lateflog' => '587380',
)  
[2025-07-13 15:11:02] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2025-07-13 15:11:02] local.ERROR: المبلغ  
[2025-07-13 15:11:02] local.ERROR: 2400.00  
[2025-07-13 15:11:02] local.ERROR: مبلغ وقدرة  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 3  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 3  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 3  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 3  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 3  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 3  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 3  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 3  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 3  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 2  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 2  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 2  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 2  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 2  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 2  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 2  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 1  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 1  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 1  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 1  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 1  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 2  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 10013  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 10013  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 10013  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 10013  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 1  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 3  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 3  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 40  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 40  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 40  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 40  
[2025-07-13 15:11:02] local.WARNING: 1  
[2025-07-13 15:11:02] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 40  
[2025-07-13 15:11:02] local.ALERT: 200  
[2025-07-13 15:11:02] local.ALERT: 10013  
[2025-07-13 15:11:02] local.CRITICAL: ****************************  
[2025-07-13 15:11:02] local.CRITICAL:   
[2025-07-13 15:11:02] local.CRITICAL: ****************************  
[2025-07-13 15:11:02] local.CRITICAL: ****************************2  
[2025-07-13 15:11:02] local.INFO: checkUser 1  
[2025-07-13 15:11:02] local.INFO: {
  "ClientBalanceResult": "25344.1789"
}  
[2025-07-13 15:11:02] local.INFO: array (
  'ClientBalanceResult' => '25344.1789',
)  
[2025-07-13 15:11:03] local.DEBUG: lattttef  
[2025-07-13 15:11:03] local.DEBUG: array (
  'ClientBalanceResult' => '25344.1789',
)  
[2025-07-13 15:11:03] local.INFO: transaction14  
[2025-07-13 15:11:03] local.INFO: first inquery phone = 106448604  
[2025-07-13 15:11:03] local.DEBUG: response querySubBalance  
[2025-07-13 15:11:03] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-13 15:11:03] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-13 15:11:03] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-13 15:11:03] local.DEBUG: print  before faction by provider price  
[2025-07-13 15:11:03] local.DEBUG: print  after faction by provider price  
[2025-07-13 15:11:03] local.INFO: thisAttempt to read property "Name" on null  
[2025-07-13 15:11:35] local.INFO: header  
[2025-07-13 15:11:35] local.INFO: header after fliter  
[2025-07-13 15:11:35] local.INFO: Body  after fliter  
[2025-07-13 15:11:35] local.INFO: array (
)  
[2025-07-13 15:11:35] local.INFO: transaction14  
[2025-07-13 15:11:35] local.INFO: first inquery phone = 106448604  
[2025-07-13 15:11:35] local.DEBUG: response querySubBalance  
[2025-07-13 15:11:35] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-13 15:11:35] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-13 15:11:35] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-13 15:11:35] local.DEBUG: print  before faction by provider price  
[2025-07-13 15:11:35] local.DEBUG: print  after faction by provider price  
[2025-07-13 15:11:39] local.INFO: header  
[2025-07-13 15:11:39] local.CRITICAL: ****************************1  
[2025-07-13 15:11:39] local.ALERT: reach here  
[2025-07-13 15:11:39] local.ALERT: array (
  'AMT' => 2400.0,
  'CType' => 0,
  'FID' => 85,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '106448604',
  'State' => 0,
  'lateflog' => '587380',
)  
[2025-07-13 15:11:39] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2025-07-13 15:11:39] local.ERROR: المبلغ  
[2025-07-13 15:11:39] local.ERROR: 2400.00  
[2025-07-13 15:11:39] local.ERROR: مبلغ وقدرة  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 3  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 3  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 3  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 3  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 3  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 3  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 3  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 3  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 3  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 2  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 2  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 2  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 2  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 2  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 2  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 2  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 1  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 1  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 1  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 1  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 1  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 2  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 10013  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 10013  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 10013  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 10013  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 1  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 3  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 3  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 40  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 40  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 40  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 40  
[2025-07-13 15:11:39] local.WARNING: 1  
[2025-07-13 15:11:39] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 40  
[2025-07-13 15:11:39] local.ALERT: 200  
[2025-07-13 15:11:39] local.ALERT: 10013  
[2025-07-13 15:11:39] local.CRITICAL: ****************************  
[2025-07-13 15:11:39] local.CRITICAL:   
[2025-07-13 15:11:39] local.CRITICAL: ****************************  
[2025-07-13 15:11:39] local.CRITICAL: ****************************2  
[2025-07-13 15:11:39] local.INFO: checkUser 1  
[2025-07-13 15:11:39] local.INFO: {
  "ClientBalanceResult": "25344.1789"
}  
[2025-07-13 15:11:39] local.INFO: array (
  'ClientBalanceResult' => '25344.1789',
)  
[2025-07-13 15:11:39] local.DEBUG: lattttef  
[2025-07-13 15:11:39] local.DEBUG: array (
  'ClientBalanceResult' => '25344.1789',
)  
[2025-07-13 15:11:39] local.INFO: transaction14  
[2025-07-13 15:11:39] local.INFO: first inquery phone = 106448604  
[2025-07-13 15:11:39] local.DEBUG: response querySubBalance  
[2025-07-13 15:11:39] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-13 15:11:39] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-13 15:11:39] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-13 15:11:39] local.DEBUG: print  before faction by provider price  
[2025-07-13 15:11:39] local.DEBUG: print  after faction by provider price  
[2025-07-13 15:11:39] local.INFO: thisAttempt to read property "Name" on null  
[2025-07-13 15:11:47] local.INFO: header  
[2025-07-13 15:11:47] local.CRITICAL: ****************************1  
[2025-07-13 15:11:47] local.ALERT: reach here  
[2025-07-13 15:11:47] local.ALERT: array (
  'AMT' => 2400.0,
  'CType' => 0,
  'FID' => 85,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '106448604',
  'State' => 0,
  'lateflog' => '587380',
)  
[2025-07-13 15:11:47] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2025-07-13 15:11:47] local.ERROR: المبلغ  
[2025-07-13 15:11:47] local.ERROR: 2400.00  
[2025-07-13 15:11:47] local.ERROR: مبلغ وقدرة  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 3  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 3  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 3  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 3  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 3  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 3  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 3  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 3  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 3  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 2  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 2  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 2  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 2  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 2  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 2  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 2  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 1  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 1  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 1  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 1  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 1  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 2  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 10013  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 10013  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 10013  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 10013  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 1  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 3  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 3  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 40  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 40  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 40  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 40  
[2025-07-13 15:11:47] local.WARNING: 1  
[2025-07-13 15:11:47] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 40  
[2025-07-13 15:11:47] local.ALERT: 200  
[2025-07-13 15:11:47] local.ALERT: 10013  
[2025-07-13 15:11:47] local.CRITICAL: ****************************  
[2025-07-13 15:11:47] local.CRITICAL:   
[2025-07-13 15:11:47] local.CRITICAL: ****************************  
[2025-07-13 15:11:47] local.CRITICAL: ****************************2  
[2025-07-13 15:11:47] local.INFO: checkUser 1  
[2025-07-13 15:11:47] local.INFO: {
  "ClientBalanceResult": "25344.1789"
}  
[2025-07-13 15:11:47] local.INFO: array (
  'ClientBalanceResult' => '25344.1789',
)  
[2025-07-13 15:11:47] local.DEBUG: lattttef  
[2025-07-13 15:11:47] local.DEBUG: array (
  'ClientBalanceResult' => '25344.1789',
)  
[2025-07-13 15:11:47] local.INFO: transaction14  
[2025-07-13 15:11:47] local.INFO: first inquery phone = 106448604  
[2025-07-13 15:11:47] local.DEBUG: response querySubBalance  
[2025-07-13 15:11:47] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-13 15:11:47] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-13 15:11:47] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-13 15:11:47] local.DEBUG: print  before faction by provider price  
[2025-07-13 15:11:47] local.DEBUG: print  after faction by provider price  
[2025-07-13 15:11:47] local.INFO: thisAttempt to read property "Name" on null  
[2025-07-13 23:40:00] local.INFO: header  
[2025-07-13 23:40:01] local.INFO: header after fliter  
[2025-07-13 23:40:01] local.INFO: Body  after fliter  
[2025-07-13 23:40:01] local.INFO: array (
  'Amount' => 155.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-13 23:40:02] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-13 23:40:02] local.ERROR: الكمية  
[2025-07-13 23:40:02] local.ERROR: سعر الوحدة  
[2025-07-13 23:40:02] local.ERROR: المبلغ  
[2025-07-13 23:40:02] local.ERROR: 1.210  
[2025-07-13 23:40:02] local.INFO: 128.10  
[2025-07-13 23:40:02] local.ERROR: مبلغ وقدرة  
[2025-07-13 23:40:02] local.INFO: الكمية  
[2025-07-13 23:40:02] local.INFO: سعر الوحدة  
[2025-07-13 23:40:02] local.INFO: المبلغ  
[2025-07-13 23:40:02] local.INFO: مبلغ وقدرة  
[2025-07-13 23:40:02] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-13 23:40:02] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '128.10',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '155.00',
  ),
)  
[2025-07-13 23:40:02] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '128.10',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '155.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-13 23:40:22] local.INFO: header  
[2025-07-13 23:40:22] local.CRITICAL: ****************************1  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 3  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 3  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 3  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 3  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 3  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 3  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 3  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 3  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 3  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 1  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 1  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 1  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 1  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 1  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 10013  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 10013  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 10013  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 10013  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 1  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 3  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 3  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 40  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 40  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 40  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 40  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 200  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 200  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 200  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 200  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 200  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 200  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 40  
[2025-07-13 23:40:22] local.ALERT: 2  
[2025-07-13 23:40:22] local.ALERT: 10013  
[2025-07-13 23:40:22] local.CRITICAL: ****************************  
[2025-07-13 23:40:22] local.CRITICAL:   
[2025-07-13 23:40:22] local.CRITICAL: ****************************  
[2025-07-13 23:40:22] local.CRITICAL: ****************************2  
[2025-07-13 23:40:22] local.INFO: checkUser 1  
[2025-07-13 23:40:23] local.INFO: {
  "ClientBalanceResult": "95941.0681"
}  
[2025-07-13 23:40:23] local.INFO: checkUser 2  
[2025-07-13 23:40:23] local.INFO: array (
  'ClientBalanceResult' => '95941.0681',
)  
[2025-07-13 23:40:23] local.INFO: 155  
[2025-07-13 23:40:23] local.ALERT: reach here  
[2025-07-13 23:40:23] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-13 23:40:23] local.ERROR: الكمية  
[2025-07-13 23:40:23] local.ERROR: سعر الوحدة  
[2025-07-13 23:40:23] local.ERROR: المبلغ  
[2025-07-13 23:40:23] local.ERROR: 1.210  
[2025-07-13 23:40:23] local.INFO: 1.210  
[2025-07-13 23:40:23] local.INFO: 128.099  
[2025-07-13 23:40:23] local.ERROR: 155.00  
[2025-07-13 23:40:23] local.INFO: checkUser 3  
[2025-07-13 23:40:23] local.INFO: checkUser 3#  
[2025-07-13 23:40:23] local.INFO: checkUser 4  
[2025-07-13 23:40:24] local.INFO: checkUser 4#  
[2025-07-13 23:40:24] local.INFO: checkUser 5  
[2025-07-13 23:40:24] local.DEBUG: lattttef  
[2025-07-13 23:40:24] local.DEBUG: array (
  'ClientBalanceResult' => '95941.0681',
)  
[2025-07-13 23:40:24] local.INFO: transaction1  
[2025-07-13 23:40:25] local.INFO: transaction2  
[2025-07-13 23:40:25] local.INFO: transaction3  
[2025-07-13 23:40:25] local.INFO: transaction4  
[2025-07-13 23:40:25] local.INFO: transaction4  
[2025-07-13 23:40:25] local.INFO: transaction5  
[2025-07-13 23:40:25] local.INFO: transaction6  
[2025-07-13 23:40:25] local.INFO: transaction7  
[2025-07-13 23:40:25] local.DEBUG: array (
  'AMT' => 155.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '128.099',
)  
[2025-07-13 23:40:25] local.INFO: transaction8  
[2025-07-13 23:40:25] local.INFO: transaction9  
[2025-07-13 23:40:25] local.INFO: 155  
[2025-07-13 23:40:25] local.INFO: transaction10  
[2025-07-13 23:40:25] local.INFO: 128.099  
[2025-07-13 23:40:25] local.INFO: 155.00  
[2025-07-13 23:40:25] local.INFO: transaction11  
[2025-07-13 23:40:25] local.INFO: 121  
[2025-07-13 23:40:25] local.INFO: topup1155.00  
[2025-07-13 23:40:25] local.INFO: topup21.210  
[2025-07-13 23:40:25] local.INFO: topup3155.00  
[2025-07-13 23:40:25] local.INFO: topup4155.00  
[2025-07-13 23:40:25] local.INFO: topup5155  
[2025-07-13 23:40:25] local.INFO: topup60  
[2025-07-13 23:40:25] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 155.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-13 23:40:25',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7342827,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-13 23:40:25',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '155.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '********234025',
  'Quantity' => '128.099',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '155.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '155.00',
  'TotalAmount' => 155.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 155.0,
  'ExCode' => NULL,
  'TransNumber' => '********234025',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#155#0',
  'ResponseTime' => '2025-07-13 23:40:25',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '25',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-13 23:40:25] local.INFO: transaction13  
[2025-07-13 23:40:25] local.INFO: transaction14  
[2025-07-13 23:40:25] local.INFO: transaction19  
[2025-07-13 23:40:25] local.INFO: transaction19#.  
[2025-07-13 23:40:25] local.INFO: transaction19#.  
[2025-07-13 23:40:25] local.INFO: transaction19#  
[2025-07-13 23:40:25] local.INFO: transaction19##  
[2025-07-13 23:40:25] local.INFO: transaction15  
[2025-07-13 23:40:29] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '155.00',
  'remainAmount' => 78010900,
  'mallrem' => -1989100,
  'transid' => '7342827',
  'ref_id' => 93857792,
)  
