<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Client extends Model
{
    use HasFactory;
    protected $table = 'Client';

    public $timestamps = false;

    protected $fillable = [
        'ID',
        'RowVersion',
        'Number',
        'Name',
        'AccountID',
        'Username',
        'Password',
        'ShopName',
        'PhoneNumber',
        'Address',
        'IsActive',
        'CreatedBy',
        'BranchID',
        'CreatedTime',
        'BirthDate',
        'ContactNumber',
        'Email',
        'CardType',
        'CardNumber',
        'CardIssuePlace',
        'CardIssueDate',
        'ImageName',
        'ActivateBy',
        'Channel',
        'DeviceID',
        'Type',
        'Note',
        'Status',
        'PhoneNumberConfirmed',
        'TwoFactorEnabled',
        'AgentID',
        'SyncAccountID',
        'LastSyncedTime',
        'DistributorID',
        'ParentAccountID',
        'ParentType',
        'GroupID',
        'Token',
    ];
}
