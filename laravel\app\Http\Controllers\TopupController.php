<?php

namespace App\Http\Controllers;

use App\Models\Faction;
use App\Models\Topup;
use App\Repositories\TopupRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Http;
use App\Models\OrderInfo;

class TopupController extends Controller
{
    private $repository;

    public function __construct()
    {
        Log::info("header");


        $this->repository = new TopupRepository();
    }
    public function GetQuota(Request $request){
        try{
            $request->headers->set('Host', 'abuosama.ddns.net:7652');
            $headers = collect($request->header())->transform(function ($item) {
                return $item[0];
            });
            Log::info("header after fliter");


            // $headers =  $headers->forget('host');
            $headers =  $headers->forget('content-length');
            $headers = $headers->toArray();


            $body = $request->all();
            $body2 = $request->all();
            $body2["Amount"]=1000;
            Log::info("Body  after fliter");
            Log::info($body );
            $GetTopupQuota = Http::withHeaders(
                $headers
            )->post('http://abuosama.ddns.net:7652/PaymentService/api/v1/GetTopupQuota',
           $body2);
           Log::info(($GetTopupQuota ->body()));

            $res = $GetTopupQuota->json();

                $items = [];
                $ratio ="";
                $quantity="";
                $amount="";
                foreach($res['Items'] as $data){
                    //add $data to $items array
                    Log::error($data['Key']);
                    if($data['Key']=='المبلغ'){
                        Log::error( (number_format(floatval($data['Value'])/floatval(1000),3,".","")));
                        if($data['Value']!=null){
                            if($data['Value']>0){
                               $ratio =floatval($data['Value'])/floatval(1000);
                                if($ratio>1.21){
                                    $quantity = number_format(floatval($body['Amount'])/floatval(1.21),2,".","");
                                    Log::info("check". $quantity.'this'.$ratio.'amount'.((floatval($body['Amount'])/floatval(1.21))*floatval($ratio)));
                                    $amount =number_format((floatval($body['Amount'])/floatval(1.21))*floatval($ratio),2,".","");
                                    Log::info('a'.$amount);
                                }else{
                                    $quantity = number_format(floatval($body['Amount'])/floatval($ratio),2,".","");
                                    Log::info( $quantity);
                                    $amount =number_format(floatval($body['Amount']),2,".","");
                                }
                               
                        }
                    }
            }
        }
        //مبلغ وقدرة
            foreach($res['Items'] as $data){
                Log::info( $data['Key']);
                if($data['Key']=='الكمية'&&$quantity!=""){
                    $data['Value'] = $quantity;
                    array_push($items, $data);

                }else if($data['Key']=='المبلغ'&&$amount!=""){
                    $data['Value'] = $amount;
                    array_push($items, $data);
                }else if($data['Key']=='سعر الوحدة'&&$ratio!=""){
                    $data['Value'] = $ratio;
                    array_push($items, $data);
                }else if($data['Key']=='مبلغ وقدرة'){
                   
                }else{
                array_push($items, $data);
            }
            }
            Log::info( $res['Items']);
            $res['Items'] =$items;
            Log::info( $items);
            Log::info( $res);

            return response()->json($res, 200);
    
}
            catch (\Exception  $th) {
                Log::error($th->getMessage());
                return response()->json([
                    "Success" => false,
                    "Result" => "يرجى المحاولة لاحقا وفي حال استمرار المشكلة يرجى التواصل مع خدمة العملاء",
                    "Error" => [
                        "Code" => "1002",
                        "Message" => "يرجى المحاولة لاحقا وفي حال استمرار المشكلة يرجى التواصل مع خدمة العملاء",
                        "Detail" => null,
                        "Type" => 0
                    ]
                ], 200);
            }

    }
 public function Inquery(Request $request,$services,$array,$phone){
     
         try{
            $request->headers->set('Host', 'abuosama.ddns.net:7652');
            $headers = collect($request->header())->transform(function ($item) {
                return $item[0];
            });
            Log::info("header after fliter");
           
           
            // $headers =  $headers->forget('host');
            $headers =  $headers->forget('content-length');
            $headers = $headers->toArray();
          
 
            $body = $request->all();
            Log::info("Body  after fliter");
            Log::info($body );
            $checkFromUser = $this->repository
            ->setHeader($headers)
            ->setBody($body)
            ->inquery($phone);
            return response()->json(
                $checkFromUser
            , 200);
             
         
        } catch (\Exception  $th) {
            return response()->json([
                "Success" => false,
                "Result" => "يرجى المحاولة لاحقا وفي حال استمرار المشكلة يرجى التواصل مع خدمة العملاء",
                "Error" => [
                    "Code" => "1002",
                    "Message" => "يرجى المحاولة لاحقا وفي حال استمرار المشكلة يرجى التواصل مع خدمة العملاء",
                    "Detail" => null,
                    "Type" => 0
                ]
            ], 200);
        }

           

         

    }
    public function topup(Request $request)
    {

        try {
            $response = [];
            // Log::info("header");
            // Log::info($request->header());
            // Log::info("all");
            // Log::info($request->all());
            // Log::info("method");
            // Log::info($request->method());
            // Log::info("ip");
            // Log::info($request->ip());
            // Log::info("schemeAndHttpHost");
            // Log::info($request->schemeAndHttpHost());
            // Log::info("getAcceptableContentTypes");
            // Log::info($request->getAcceptableContentTypes());
            $request->headers->set('Host', 'abuosama.ddns.net:7652');
            $headers = collect($request->header())->transform(function ($item) {
                return $item[0];
            });
           

            // $headers =  $headers->forget('host');
            $headers =  $headers->forget('content-length');
            $headers = $headers->toArray();


            // when error


            // return response()->json([
            //     "Success"=> false,
            //     "Result"=> "اقل مبلغ للسداد 21 ريال يمني",
            //     "Error"=> [
            //         "Code"=> "1002",
            //         "Message"=> "اقل مبلغ للسداد 21 ريال يمني",
            //         "Detail"=> null,
            //         "Type"=> 0  
            //     ]
            // ],200);
            $body = $request->all();
                    

            log::Critical ('****************************1');
                $checkFromFaction = $this->repository
                ->setHeader($headers)
                ->setBody($body)
                ->checkFaction();

                log::Critical ('****************************');
                log::Critical ($checkFromFaction);
                log::Critical ('****************************');

            
            log::Critical ('****************************2');

            $checkFromUser = $this->repository
                ->setHeader($headers)
                ->setBody($body)
                ->checkUser();
            Log::debug("lattttef");
            Log::debug($checkFromUser);

            if (self::isInqueryBundle($body['SID'])) {
               
                $inquery = self::checkFromBundle($this->repository, $headers, $body);
            
                if ($inquery && $inquery->Success) {
            
                    $faction = Faction::where('ServiceID', $body['SID'])->where('ID', $body['FID'])->first();
            
            
                    if ($faction && ($faction->ProviderPrice == $inquery->mybondle)) {
                        $confrim = $this->repository->transaction($request);
                    } else {
                        return response()->json([
                            "Success" => false,
                            "Result" => "الباقة الذي قمت باختيارها لاتتناسب مع حسابك",
                            "Error" => [
                                "Code" => "1002",
                                "Message" => "الباقة الذي قمت باختيارها لاتتناسب مع حسابك",
                                "Detail" => null,
                                "Type" => 0
                            ]
                        ], 200);
                    }
                } else {
                    throw ValidationException::withMessages([
                        'try' => ["فشل في تنفيذ العملية"],
                    ]);
                }
            }
            else {
               
                $confrim = $this->repository->transaction($request);
            }
            if ($confrim == 0 && $body['SID']==40) {
                
                $balance = Topup::where('DebitorAccountID', $this->repository->accountUser->AccountID)->get()->last();
                throw ValidationException::withMessages([
                    'try' => [$balance ? $balance->ProviderRM : "فشل في تنفيذ العملية"],
                ]);
            }
            if ($confrim == 0) {
                throw ValidationException::withMessages([
                    'try' => ["فشل في تنفيذ العملية"],
                ]);
            } else {
                $balance = Topup::where('DebitorAccountID', $this->repository->accountUser->AccountID)->get()->last();

                return response()->json([
                    "Success" => true,
                    "Result" => "{\"ID\":" . $balance->ID . ",\"BAL\":" . $balance->UnitPrice . ',\"MSG\":null}',
                    "Error" => null
                ], 200);
            }
        } catch (\Exception  $th) {
            Log::info('this'.$th->getMessage());
            if($th instanceof ValidationException){
                $message=$th->getMessage();
            }
            else {
                $message="يرجى المحاولة لاحقا وفي حال استمرار المشكلة يرجى التواصل مع خدمة العملاء";
            }

            return response()->json([
                "Success" => false,
                "Result" => $message,
                "Error" => [
                    "Code" => "1002",
                    "Message" => $message,
                    "Detail" => null,
                    "Type" => 0
                ]
            ], 200);
            //throw $th;
        }
    }

    public function gameAndCard(Request $request)
    {

        try {
          
          
            $request->headers->set('Host', 'abuosama.ddns.net:7652');
            $headers = collect($request->header())->transform(function ($item) {
                return $item[0];
            });
           

            // $headers =  $headers->forget('host');
            $headers =  $headers->forget('content-length');
            $headers = $headers->toArray();


             
            $body = $request->all();
            $body['Entity']=collect(json_decode($body['Entity'],true));
                log::info('print test ');
                    log::info($body['Entity']);
                    log::info($body['Entity']['CardTypeID']);
                // $checkFromFaction = $this->repository
                // ->setHeader($headers)
                // ->setBody($body)
                // ->checkFactionGame();
               
                log::Critical ('****************************');
                // log::Critical ($checkFromFaction);
                log::Critical ('****************************');

            
            log::Critical ('****************************2');

            $checkFromUser = $this->repository
                ->setHeader($headers)
                ->setBody($body)
                 ->checkUserGame();
                
                
                $confrim = $this->repository->transactionGame($request); 
            Log::debug("lattttef Game");
            Log::debug($checkFromUser);
           
            if ($confrim == 0) {
                throw ValidationException::withMessages([
                    'try' => ["فشل في تنفيذ العملية"],
                ]);
            } else {
                $balance = OrderInfo::where('DebitorAccountID', $this->repository->accountUser->AccountID)->get()->last();

                return response()->json([
                    "Success" => true,
                    "Result" => "{\"ID\":" . $balance->ID . ",\"BAL\":" . $balance->Amount . ',\"MSG\":null}',
                    "Error" => null
                ], 200);
            }
        } catch (\Exception  $th) {
            Log::info('this'.$th->getMessage());
            if($th instanceof ValidationException){
                $message=$th->getMessage();
            }
            else {
                $message="يرجى المحاولة لاحقا وفي حال استمرار المشكلة يرجى التواصل مع خدمة العملاء";
            }

            return response()->json([
                "Success" => false,
                "Result" => $message,
                "Error" => [
                    "Code" => "1002",
                    "Message" => $message,
                    "Detail" => null,
                    "Type" => 0
                ]
            ], 200);
            //throw $th;
        }
    }

    public function checkFromBundle($repositoryPar, $headersPar, $bodyPar)
    {

        $inquery = $repositoryPar
            ->setHeader($headersPar)
            ->setBody($bodyPar)
            ->inquery($bodyPar["SNO"]);
        return $inquery;
    }

    public function isInqueryBundle($SID)
    {
        
        switch ($SID) {
            case 200:
                return true;
                break;

            case 40:
                return false;
                break;
                case 2:
                    return false;
                    break;
            default:
                return false;
                break;
        }
    }

        // update payment api
        public function updatePayment(Request $request) 
        {
            Log::info("data when call api from provider 0");

            $request->headers->set('Host', 'abuosama.ddns.net:7652');
                $headers = collect($request->header())->transform(function ($item) {
                    return $item[0];
                });
                Log::info("data when call api from provider");
               
               
                // $headers =  $headers->forget('host');
                $headers =  $headers->forget('content-length');
                $headers = $headers->toArray();
              
                 
                Log::info("data when call api from provider 2");
                
               return  $this->repository
                ->setHeader($headers)
                ->updatePayment( $request);
                Log::info("end call api from provider 3");
        }
}
