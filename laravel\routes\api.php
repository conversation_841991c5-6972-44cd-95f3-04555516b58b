<?php

use App\Http\Controllers\TopupController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::post('/api/v1/ExecuteTopup',[TopupController::class,'topup']);
Route::get('/api/v1/Inquery/{services}/{array}/{phone}',[TopupController::class,'Inquery']);
Route::post('/api/v1/GetTopupQuota',[TopupController::class,'GetQuota']);
Route::post('/api/v1/execute',[TopupController::class,'gameAndCard']);
Route::get('/web-hook-payment', [TopupController::class, 'updatePayment']);

// Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
//     return $request->user();
// });
