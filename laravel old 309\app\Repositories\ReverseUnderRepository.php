<?php

namespace App\Repositories;

use App\Models\JournalEntry;
use App\Repositories;

class ReverseUnderRepository
{
    public  $topup;
    public  $Journal;
    public  $exeption;
    public function __construct( $Journal)
    {
        
        $this->Journal = $Journal;
    }
    public function revers()
    {
        $this->Journal->Status = 0;
        $this->Journal->Debited = 0;
        $this->Journal->save();
    }
}
