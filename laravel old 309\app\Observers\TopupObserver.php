<?php

namespace App\Observers;

use App\Models\Journal;
use App\Models\JournalEntry;
use Illuminate\Support\Str;
use App\Models\Topup;
use Illuminate\Support\Facades\Log;

class TopupObserver
{
    /**
     * Handle the Topup "created" event.
     *
     * @param  \App\Models\Topup  $topup
     * @return void
     */
    public function created(Topup $topup)
    {
        //
    }

    /**
     * Handle the Topup "updated" event.
     *
     * @param  \App\Models\Topup  $topup
     * @return void
     */
    public function updated(Topup $topup)
    {

        // try {
        //     Log::debug("topup observe");
        //     if ($topup && (strlen($topup->Datestamb) <= 8)) {
        //         Log::debug("topup observe2");
        //         $topup->Datestamb = $topup->UniqueNo;
        //         $topup->save();
        //         Log::debug("topup observe 3");
        //         $journal = Journal::where('id', $topup->EntryID)->first();
        //         if ($journal && (strlen($topup->Datestamb) <= 8) ) {
        //             $journal->datestamb = $topup->UniqueNo;
        //         }
        //         if ((strlen($topup->Datestamb) <= 8)){
        //             $journalEntity = JournalEntry::where('ParentID', $journal->ID)->get();
        //             foreach ($journalEntity as $key => $value) {
        //                 $value->Datestamp = $topup->UniqueNo;
        //                 $value->save();
        //             }
        //         }

                
        //     }
        // } catch (\Throwable $th) {
        //     Log::error("error when update date ");
        //     Log::error($th->getMessage());
        // }


        //
    }

    /**
     * Handle the Topup "deleted" event.
     *
     * @param  \App\Models\Topup  $topup
     * @return void
     */
    public function deleted(Topup $topup)
    {
        //
    }

    /**
     * Handle the Topup "restored" event.
     *
     * @param  \App\Models\Topup  $topup
     * @return void
     */
    public function restored(Topup $topup)
    {
        //
    }

    /**
     * Handle the Topup "force deleted" event.
     *
     * @param  \App\Models\Topup  $topup
     * @return void
     */
    public function forceDeleted(Topup $topup)
    {
        //
    }
}

