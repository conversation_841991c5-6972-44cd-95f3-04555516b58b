<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Process\Process;

class ServeExternal extends Command
{
    protected $signature = 'serve:external {--host=0.0.0.0} {--port=8081}';
    protected $description = 'Start Laravel development server accessible from external connections';

    public function handle()
    {
        $host = $this->option('host');
        $port = $this->option('port');
        
        $this->info('Starting Laravel server...');
        
        // تشغيل الخادم
        $process = new Process(['php', 'artisan', 'serve', "--host={$host}", "--port={$port}"]);
        $process->setTimeout(null);
        
        $this->info("Server started:");
        $this->line("Local: http://localhost:{$port}");
        $this->line("External: http://{$host}:{$port}");
        
        $process->run(function ($type, $buffer) {
            echo $buffer;
        });
    }
}

