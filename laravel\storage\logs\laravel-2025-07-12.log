[2025-07-12 02:14:51] local.INFO: header  
[2025-07-12 02:14:52] local.INFO: header after fliter  
[2025-07-12 02:14:52] local.INFO: Body  after fliter  
[2025-07-12 02:14:52] local.INFO: array (
  'Amount' => 450.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-12 02:14:52] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-12 02:14:52] local.ERROR: الكمية  
[2025-07-12 02:14:52] local.ERROR: سعر الوحدة  
[2025-07-12 02:14:52] local.ERROR: المبلغ  
[2025-07-12 02:14:52] local.ERROR: 1.210  
[2025-07-12 02:14:52] local.INFO: 371.90  
[2025-07-12 02:14:52] local.ERROR: مبلغ وقدرة  
[2025-07-12 02:14:52] local.INFO: الكمية  
[2025-07-12 02:14:52] local.INFO: سعر الوحدة  
[2025-07-12 02:14:52] local.INFO: المبلغ  
[2025-07-12 02:14:52] local.INFO: مبلغ وقدرة  
[2025-07-12 02:14:52] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-12 02:14:52] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '371.90',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '450.00',
  ),
)  
[2025-07-12 02:14:52] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '371.90',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '450.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-12 02:14:54] local.INFO: header  
[2025-07-12 02:14:54] local.CRITICAL: ****************************1  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 3  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 3  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 3  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 3  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 3  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 3  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 3  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 3  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 3  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 1  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 1  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 1  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 1  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 1  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 10013  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 10013  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 10013  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 10013  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 1  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 3  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 3  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 40  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 40  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 40  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 40  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 200  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 200  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 200  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 200  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 200  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 200  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 40  
[2025-07-12 02:14:54] local.ALERT: 2  
[2025-07-12 02:14:54] local.ALERT: 10013  
[2025-07-12 02:14:54] local.CRITICAL: ****************************  
[2025-07-12 02:14:54] local.CRITICAL:   
[2025-07-12 02:14:54] local.CRITICAL: ****************************  
[2025-07-12 02:14:54] local.CRITICAL: ****************************2  
[2025-07-12 02:14:54] local.INFO: checkUser 1  
[2025-07-12 02:14:55] local.INFO: {
  "ClientBalanceResult": "33118.1281"
}  
[2025-07-12 02:14:55] local.INFO: checkUser 2  
[2025-07-12 02:14:55] local.INFO: array (
  'ClientBalanceResult' => '33118.1281',
)  
[2025-07-12 02:14:55] local.INFO: 450  
[2025-07-12 02:14:55] local.ALERT: reach here  
[2025-07-12 02:14:56] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-12 02:14:56] local.ERROR: الكمية  
[2025-07-12 02:14:56] local.ERROR: سعر الوحدة  
[2025-07-12 02:14:56] local.ERROR: المبلغ  
[2025-07-12 02:14:56] local.ERROR: 1.210  
[2025-07-12 02:14:56] local.INFO: 1.210  
[2025-07-12 02:14:56] local.INFO: 371.901  
[2025-07-12 02:14:56] local.ERROR: 450.00  
[2025-07-12 02:14:56] local.INFO: checkUser 3  
[2025-07-12 02:14:56] local.INFO: checkUser 3#  
[2025-07-12 02:14:56] local.INFO: checkUser 4  
[2025-07-12 02:14:56] local.INFO: checkUser 4#  
[2025-07-12 02:14:56] local.INFO: checkUser 5  
[2025-07-12 02:14:56] local.DEBUG: lattttef  
[2025-07-12 02:14:56] local.DEBUG: array (
  'ClientBalanceResult' => '33118.1281',
)  
[2025-07-12 02:14:56] local.INFO: transaction1  
[2025-07-12 02:14:56] local.INFO: transaction2  
[2025-07-12 02:14:56] local.INFO: transaction3  
[2025-07-12 02:14:56] local.INFO: transaction4  
[2025-07-12 02:14:56] local.INFO: transaction4  
[2025-07-12 02:14:56] local.INFO: transaction5  
[2025-07-12 02:14:56] local.INFO: transaction6  
[2025-07-12 02:14:56] local.INFO: transaction7  
[2025-07-12 02:14:56] local.DEBUG: array (
  'AMT' => 450.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '371.901',
)  
[2025-07-12 02:14:56] local.INFO: transaction8  
[2025-07-12 02:14:56] local.INFO: transaction9  
[2025-07-12 02:14:56] local.INFO: 450  
[2025-07-12 02:14:56] local.INFO: transaction10  
[2025-07-12 02:14:56] local.INFO: 371.901  
[2025-07-12 02:14:56] local.INFO: 450.00  
[2025-07-12 02:14:56] local.INFO: transaction11  
[2025-07-12 02:14:56] local.INFO: 121  
[2025-07-12 02:14:56] local.INFO: topup1450.00  
[2025-07-12 02:14:56] local.INFO: topup21.210  
[2025-07-12 02:14:56] local.INFO: topup3450.00  
[2025-07-12 02:14:56] local.INFO: topup4450.00  
[2025-07-12 02:14:56] local.INFO: topup5450  
[2025-07-12 02:14:56] local.INFO: topup60  
[2025-07-12 02:14:56] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 450.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-12 02:14:56',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7341245,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-12 02:14:56',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '450.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '********021456',
  'Quantity' => '371.901',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '450.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '450.00',
  'TotalAmount' => 450.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 450.0,
  'ExCode' => NULL,
  'TransNumber' => '********021456',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#450#0',
  'ResponseTime' => '2025-07-12 02:14:56',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '56',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-12 02:14:56] local.INFO: transaction13  
[2025-07-12 02:14:56] local.INFO: transaction14  
[2025-07-12 02:14:56] local.INFO: transaction19  
[2025-07-12 02:14:56] local.INFO: transaction19#.  
[2025-07-12 02:14:56] local.INFO: transaction19#.  
[2025-07-12 02:14:56] local.INFO: transaction19#  
[2025-07-12 02:14:56] local.INFO: transaction19##  
[2025-07-12 02:14:56] local.INFO: transaction15  
[2025-07-12 02:15:01] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '450.00',
  'remainAmount' => ********,
  'mallrem' => -1705329,
  'transid' => '7341245',
  'ref_id' => 93651688,
)  
[2025-07-12 05:22:18] local.INFO: header  
[2025-07-12 05:22:18] local.CRITICAL: ****************************1  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 3  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 3  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 3  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 3  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 3  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 3  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 3  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 3  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 3  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 1  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 1  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 1  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 1  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 1  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 10013  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 10013  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 10013  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 10013  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 1  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 3  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 3  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 40  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 40  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 40  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 40  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 200  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 200  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 200  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 200  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 200  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 200  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 40  
[2025-07-12 05:22:18] local.ALERT: 2  
[2025-07-12 05:22:18] local.ALERT: 10013  
[2025-07-12 05:22:18] local.CRITICAL: ****************************  
[2025-07-12 05:22:18] local.CRITICAL:   
[2025-07-12 05:22:18] local.CRITICAL: ****************************  
[2025-07-12 05:22:18] local.CRITICAL: ****************************2  
[2025-07-12 05:22:18] local.INFO: checkUser 1  
[2025-07-12 05:22:20] local.INFO: {
  "ClientBalanceResult": "28580.1281"
}  
[2025-07-12 05:22:20] local.INFO: checkUser 2  
[2025-07-12 05:22:20] local.INFO: array (
  'ClientBalanceResult' => '28580.1281',
)  
[2025-07-12 05:22:20] local.INFO: 450  
[2025-07-12 05:22:20] local.ALERT: reach here  
[2025-07-12 05:22:20] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-12 05:22:20] local.ERROR: الكمية  
[2025-07-12 05:22:20] local.ERROR: سعر الوحدة  
[2025-07-12 05:22:20] local.ERROR: المبلغ  
[2025-07-12 05:22:20] local.ERROR: 1.210  
[2025-07-12 05:22:20] local.INFO: 1.210  
[2025-07-12 05:22:20] local.INFO: 371.901  
[2025-07-12 05:22:20] local.ERROR: 450.00  
[2025-07-12 05:22:20] local.INFO: checkUser 3  
[2025-07-12 05:22:20] local.INFO: checkUser 3#  
[2025-07-12 05:22:20] local.INFO: checkUser 4  
[2025-07-12 05:22:20] local.INFO: checkUser 4#  
[2025-07-12 05:22:20] local.INFO: checkUser 5  
[2025-07-12 05:22:20] local.DEBUG: lattttef  
[2025-07-12 05:22:20] local.DEBUG: array (
  'ClientBalanceResult' => '28580.1281',
)  
[2025-07-12 05:22:20] local.INFO: transaction1  
[2025-07-12 05:22:20] local.INFO: transaction2  
[2025-07-12 05:22:20] local.INFO: transaction3  
[2025-07-12 05:22:20] local.INFO: transaction4  
[2025-07-12 05:22:20] local.INFO: transaction4  
[2025-07-12 05:22:20] local.INFO: transaction5  
[2025-07-12 05:22:20] local.INFO: transaction6  
[2025-07-12 05:22:20] local.INFO: transaction7  
[2025-07-12 05:22:20] local.DEBUG: array (
  'AMT' => 450.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '371.901',
)  
[2025-07-12 05:22:20] local.INFO: transaction8  
[2025-07-12 05:22:21] local.INFO: transaction9  
[2025-07-12 05:22:21] local.INFO: 450  
[2025-07-12 05:22:21] local.INFO: transaction10  
[2025-07-12 05:22:21] local.INFO: 371.901  
[2025-07-12 05:22:21] local.INFO: 450.00  
[2025-07-12 05:22:21] local.INFO: transaction11  
[2025-07-12 05:22:21] local.INFO: 121  
[2025-07-12 05:22:21] local.INFO: topup1450.00  
[2025-07-12 05:22:21] local.INFO: topup21.210  
[2025-07-12 05:22:21] local.INFO: topup3450.00  
[2025-07-12 05:22:21] local.INFO: topup4450.00  
[2025-07-12 05:22:21] local.INFO: topup5450  
[2025-07-12 05:22:21] local.INFO: topup60  
[2025-07-12 05:22:21] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 450.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-12 05:22:21',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7341267,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-12 05:22:21',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '450.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '********052221',
  'Quantity' => '371.901',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '450.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '450.00',
  'TotalAmount' => 450.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 450.0,
  'ExCode' => NULL,
  'TransNumber' => '********052221',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#450#0',
  'ResponseTime' => '2025-07-12 05:22:21',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '21',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-12 05:22:21] local.INFO: transaction13  
[2025-07-12 05:22:21] local.INFO: transaction14  
[2025-07-12 05:22:21] local.INFO: transaction19  
[2025-07-12 05:22:21] local.INFO: transaction19#.  
[2025-07-12 05:22:21] local.INFO: transaction19#.  
[2025-07-12 05:22:21] local.INFO: transaction19#  
[2025-07-12 05:22:21] local.INFO: transaction19##  
[2025-07-12 05:22:21] local.INFO: transaction15  
[2025-07-12 05:22:24] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '450.00',
  'remainAmount' => ********,
  'mallrem' => -1716689,
  'transid' => '7341267',
  'ref_id' => 93652699,
)  
[2025-07-12 16:56:39] local.INFO: header  
[2025-07-12 16:56:39] local.INFO: header after fliter  
[2025-07-12 16:56:39] local.INFO: Body  after fliter  
[2025-07-12 16:56:39] local.INFO: array (
)  
[2025-07-12 16:56:39] local.INFO: transaction14  
[2025-07-12 16:56:39] local.INFO: first inquery phone = 103325837  
[2025-07-12 16:56:42] local.DEBUG: response querySubBalance  
[2025-07-12 16:56:42] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-12 16:56:42] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-12 16:56:42] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-12 16:56:42] local.DEBUG: print  before faction by provider price  
[2025-07-12 16:56:43] local.DEBUG: print  after faction by provider price  
[2025-07-12 18:20:25] local.INFO: header  
[2025-07-12 18:20:25] local.INFO: header after fliter  
[2025-07-12 18:20:25] local.INFO: Body  after fliter  
[2025-07-12 18:20:25] local.INFO: array (
)  
[2025-07-12 18:20:25] local.INFO: transaction14  
[2025-07-12 18:20:25] local.INFO: first inquery phone = 107206034  
[2025-07-12 18:20:26] local.DEBUG: response querySubBalance  
[2025-07-12 18:20:26] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-12 18:20:26] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-12 18:20:26] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-12 18:20:26] local.DEBUG: print  before faction by provider price  
[2025-07-12 18:20:26] local.DEBUG: print  after faction by provider price  
