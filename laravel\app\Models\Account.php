<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Account extends Model
{
    use HasFactory;
    protected $table = 'Account';
    public $timestamps = false;

    protected $fillable = [
        'ID',
        'ParentNumber',
        'Name',
        'Number',
        'Type',
        'CreatedBy',
        'CreatedTime',
        'RowVersion',
        'BranchID',
        'Status',
        'AccountLedgerID',
        'Description',
        'IsCash',
        'IsContraAccount',
        'IsParty'
    ];
}
