[2023-03-30 01:32:15] local.INFO: header  
[2023-03-30 01:32:15] local.INFO: header after fliter  
[2023-03-30 01:32:15] local.INFO: Body  after fliter  
[2023-03-30 01:32:15] local.INFO: array (
)  
[2023-03-30 01:32:15] local.INFO: transaction14  
[2023-03-30 01:32:15] local.INFO: first inquery phone = 103335606  
[2023-03-30 01:44:40] local.INFO: header  
[2023-03-30 01:44:40] local.CRITICAL: ****************************1  
[2023-03-30 01:44:40] local.ALERT: reach here  
[2023-03-30 01:44:40] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-30 01:44:40] local.ERROR: المبلغ  
[2023-03-30 01:44:40] local.ERROR: 4,000.00  
[2023-03-30 01:44:40] local.ERROR: مبلغ وقدرة  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 3  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 3  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 3  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 3  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 3  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 3  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 3  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 3  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 3  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 2  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 2  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 2  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 2  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 2  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 2  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 2  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 1  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 1  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 1  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 1  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 1  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 2  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 10013  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 10013  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 10013  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 10013  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 1  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 3  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 3  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 40  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 40  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 40  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 40  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.WARNING: 1  
[2023-03-30 01:44:40] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 200  
[2023-03-30 01:44:40] local.ALERT: 40  
[2023-03-30 01:44:40] local.CRITICAL: ****************************2  
[2023-03-30 01:44:40] local.CRITICAL: ****************************  
[2023-03-30 01:44:40] local.CRITICAL:   
[2023-03-30 01:44:40] local.CRITICAL: ****************************  
[2023-03-30 01:44:41] local.INFO: {
  "ClientBalanceResult": "48825.8100"
}  
[2023-03-30 01:44:41] local.INFO: array (
  'ClientBalanceResult' => '48825.8100',
)  
[2023-03-30 01:44:41] local.DEBUG: lattttef  
[2023-03-30 01:44:41] local.DEBUG: array (
  'ClientBalanceResult' => '48825.8100',
)  
[2023-03-30 01:44:41] local.INFO: transaction14  
[2023-03-30 01:44:41] local.INFO: first inquery phone = 107233867  
[2023-03-30 01:45:53] local.INFO: header  
[2023-03-30 01:45:53] local.CRITICAL: ****************************1  
[2023-03-30 01:45:53] local.ALERT: reach here  
[2023-03-30 01:45:53] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-30 01:45:53] local.ERROR: المبلغ  
[2023-03-30 01:45:53] local.ERROR: 4,000.00  
[2023-03-30 01:45:53] local.ERROR: مبلغ وقدرة  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 3  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 3  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 3  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 3  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 3  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 3  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 3  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 3  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 3  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 2  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 2  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 2  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 2  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 2  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 2  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 2  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 1  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 1  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 1  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 1  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 1  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 2  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 10013  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 10013  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 10013  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 10013  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 1  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 3  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 3  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 40  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 40  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 40  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 40  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.WARNING: 1  
[2023-03-30 01:45:53] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 200  
[2023-03-30 01:45:53] local.ALERT: 40  
[2023-03-30 01:45:53] local.CRITICAL: ****************************2  
[2023-03-30 01:45:53] local.CRITICAL: ****************************  
[2023-03-30 01:45:53] local.CRITICAL:   
[2023-03-30 01:45:53] local.CRITICAL: ****************************  
[2023-03-30 01:45:54] local.INFO: {
  "ClientBalanceResult": "48825.8100"
}  
[2023-03-30 01:45:54] local.INFO: array (
  'ClientBalanceResult' => '48825.8100',
)  
[2023-03-30 01:45:54] local.DEBUG: lattttef  
[2023-03-30 01:45:54] local.DEBUG: array (
  'ClientBalanceResult' => '48825.8100',
)  
[2023-03-30 01:45:54] local.INFO: transaction14  
[2023-03-30 01:45:54] local.INFO: first inquery phone = 107233867  
[2023-03-30 01:52:06] local.INFO: header  
[2023-03-30 01:52:06] local.INFO: header after fliter  
[2023-03-30 01:52:06] local.INFO: Body  after fliter  
[2023-03-30 01:52:06] local.INFO: array (
)  
[2023-03-30 01:52:06] local.INFO: transaction14  
[2023-03-30 01:52:06] local.INFO: first inquery phone = 107233867  
[2023-03-30 01:52:42] local.INFO: header  
[2023-03-30 01:52:42] local.INFO: header after fliter  
[2023-03-30 01:52:42] local.INFO: Body  after fliter  
[2023-03-30 01:52:42] local.INFO: array (
)  
[2023-03-30 01:52:42] local.INFO: transaction14  
[2023-03-30 01:52:42] local.INFO: first inquery phone = 107233867  
[2023-03-30 01:53:17] local.INFO: header  
[2023-03-30 01:53:17] local.INFO: header after fliter  
[2023-03-30 01:53:17] local.INFO: Body  after fliter  
[2023-03-30 01:53:17] local.INFO: array (
)  
[2023-03-30 01:53:17] local.INFO: transaction14  
[2023-03-30 01:53:17] local.INFO: first inquery phone = 107233867  
[2023-03-30 01:58:22] local.INFO: header  
[2023-03-30 01:58:23] local.INFO: header after fliter  
[2023-03-30 01:58:23] local.INFO: Body  after fliter  
[2023-03-30 01:58:23] local.INFO: array (
)  
[2023-03-30 01:58:23] local.INFO: transaction14  
[2023-03-30 01:58:23] local.INFO: first inquery phone = 107233867  
[2023-03-30 01:58:58] local.INFO: header  
[2023-03-30 01:58:58] local.INFO: header after fliter  
[2023-03-30 01:58:58] local.INFO: Body  after fliter  
[2023-03-30 01:58:58] local.INFO: array (
)  
[2023-03-30 01:58:58] local.INFO: transaction14  
[2023-03-30 01:58:58] local.INFO: first inquery phone = 107233867  
[2023-03-30 01:59:33] local.INFO: header  
[2023-03-30 01:59:33] local.INFO: header after fliter  
[2023-03-30 01:59:33] local.INFO: Body  after fliter  
[2023-03-30 01:59:33] local.INFO: array (
)  
[2023-03-30 01:59:33] local.INFO: transaction14  
[2023-03-30 01:59:33] local.INFO: first inquery phone = 107233867  
[2023-03-30 02:28:34] local.INFO: header  
[2023-03-30 02:28:34] local.INFO: header after fliter  
[2023-03-30 02:28:34] local.INFO: Body  after fliter  
[2023-03-30 02:28:34] local.INFO: array (
)  
[2023-03-30 02:28:34] local.INFO: transaction14  
[2023-03-30 02:28:34] local.INFO: first inquery phone = 101034508  
[2023-03-30 02:29:09] local.INFO: header  
[2023-03-30 02:29:09] local.INFO: header after fliter  
[2023-03-30 02:29:09] local.INFO: Body  after fliter  
[2023-03-30 02:29:09] local.INFO: array (
)  
[2023-03-30 02:29:09] local.INFO: transaction14  
[2023-03-30 02:29:09] local.INFO: first inquery phone = 101034508  
[2023-03-30 02:29:45] local.INFO: header  
[2023-03-30 02:29:45] local.CRITICAL: ****************************1  
[2023-03-30 02:29:45] local.ALERT: reach here  
[2023-03-30 02:29:45] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2023-03-30 02:29:45] local.ERROR: المبلغ  
[2023-03-30 02:29:45] local.ERROR: 2,400.00  
[2023-03-30 02:29:45] local.ERROR: مبلغ وقدرة  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 3  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 3  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 3  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 3  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 3  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 3  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 3  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 3  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 3  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 2  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 2  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 2  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 2  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 2  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 2  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 2  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 1  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 1  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 1  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 1  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 1  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 2  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 10013  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 10013  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 10013  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 10013  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 1  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 3  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 3  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 40  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 40  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 40  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 40  
[2023-03-30 02:29:45] local.WARNING: 1  
[2023-03-30 02:29:45] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 200  
[2023-03-30 02:29:45] local.ALERT: 40  
[2023-03-30 02:29:45] local.CRITICAL: ****************************2  
[2023-03-30 02:29:45] local.CRITICAL: ****************************  
[2023-03-30 02:29:45] local.CRITICAL:   
[2023-03-30 02:29:45] local.CRITICAL: ****************************  
[2023-03-30 02:29:45] local.INFO: {
  "ClientBalanceResult": "1095.1500"
}  
[2023-03-30 02:29:45] local.INFO: array (
  'ClientBalanceResult' => '1095.1500',
)  
[2023-03-30 02:29:45] local.INFO: price less than Balance  
[2023-03-30 02:29:46] local.INFO: header  
[2023-03-30 02:29:46] local.INFO: header after fliter  
[2023-03-30 02:29:46] local.INFO: Body  after fliter  
[2023-03-30 02:29:46] local.INFO: array (
)  
[2023-03-30 02:29:46] local.INFO: transaction14  
[2023-03-30 02:29:46] local.INFO: first inquery phone = 101034508  
[2023-03-30 02:30:22] local.INFO: header  
[2023-03-30 02:30:22] local.INFO: header after fliter  
[2023-03-30 02:30:22] local.INFO: Body  after fliter  
[2023-03-30 02:30:22] local.INFO: array (
)  
[2023-03-30 02:30:22] local.INFO: transaction14  
[2023-03-30 02:30:22] local.INFO: first inquery phone = 101034508  
[2023-03-30 02:30:57] local.INFO: header  
[2023-03-30 02:30:57] local.INFO: header after fliter  
[2023-03-30 02:30:57] local.INFO: Body  after fliter  
[2023-03-30 02:30:57] local.INFO: array (
)  
[2023-03-30 02:30:57] local.INFO: transaction14  
[2023-03-30 02:30:57] local.INFO: first inquery phone = 101034508  
[2023-03-30 02:31:33] local.INFO: header  
[2023-03-30 02:31:33] local.INFO: header after fliter  
[2023-03-30 02:31:33] local.INFO: Body  after fliter  
[2023-03-30 02:31:33] local.INFO: array (
)  
[2023-03-30 02:31:33] local.INFO: transaction14  
[2023-03-30 02:31:33] local.INFO: first inquery phone = 101034508  
[2023-03-30 02:32:08] local.INFO: header  
[2023-03-30 02:32:08] local.INFO: header after fliter  
[2023-03-30 02:32:08] local.INFO: Body  after fliter  
[2023-03-30 02:32:08] local.INFO: array (
)  
[2023-03-30 02:32:08] local.INFO: transaction14  
[2023-03-30 02:32:08] local.INFO: first inquery phone = 101035776  
[2023-03-30 02:32:43] local.INFO: header  
[2023-03-30 02:32:43] local.INFO: header after fliter  
[2023-03-30 02:32:43] local.INFO: Body  after fliter  
[2023-03-30 02:32:43] local.INFO: array (
)  
[2023-03-30 02:32:43] local.INFO: transaction14  
[2023-03-30 02:32:43] local.INFO: first inquery phone = 101035776  
[2023-03-30 02:33:19] local.INFO: header  
[2023-03-30 02:33:19] local.INFO: header after fliter  
[2023-03-30 02:33:19] local.INFO: Body  after fliter  
[2023-03-30 02:33:19] local.INFO: array (
)  
[2023-03-30 02:33:19] local.INFO: transaction14  
[2023-03-30 02:33:19] local.INFO: first inquery phone = 101034508  
[2023-03-30 02:33:54] local.INFO: header  
[2023-03-30 02:33:54] local.INFO: header after fliter  
[2023-03-30 02:33:54] local.INFO: Body  after fliter  
[2023-03-30 02:33:54] local.INFO: array (
)  
[2023-03-30 02:33:54] local.INFO: transaction14  
[2023-03-30 02:33:54] local.INFO: first inquery phone = 101034508  
[2023-03-30 02:34:30] local.INFO: header  
[2023-03-30 02:34:30] local.INFO: header after fliter  
[2023-03-30 02:34:30] local.INFO: Body  after fliter  
[2023-03-30 02:34:30] local.INFO: array (
)  
[2023-03-30 02:34:30] local.INFO: transaction14  
[2023-03-30 02:34:30] local.INFO: first inquery phone = 101035776  
[2023-03-30 02:35:05] local.INFO: header  
[2023-03-30 02:35:05] local.INFO: header after fliter  
[2023-03-30 02:35:05] local.INFO: Body  after fliter  
[2023-03-30 02:35:05] local.INFO: array (
)  
[2023-03-30 02:35:05] local.INFO: transaction14  
[2023-03-30 02:35:05] local.INFO: first inquery phone = 101034508  
[2023-03-30 03:12:45] local.INFO: header  
[2023-03-30 03:12:45] local.INFO: header after fliter  
[2023-03-30 03:12:45] local.INFO: Body  after fliter  
[2023-03-30 03:12:45] local.INFO: array (
)  
[2023-03-30 03:12:45] local.INFO: transaction14  
[2023-03-30 03:12:45] local.INFO: first inquery phone = 103377914  
[2023-03-30 03:12:49] local.DEBUG: response querySubBalance  
[2023-03-30 03:12:49] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00##28-03-2023#0#0##.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 03:12:49] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '',
  4 => '28-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 25',
)  
[2023-03-30 03:12:49] local.DEBUG: print  before faction by provider price  
[2023-03-30 03:12:49] local.DEBUG: print  after faction by provider price  
[2023-03-30 03:12:49] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-30 03:12:49] local.DEBUG: print1  
[2023-03-30 03:12:49] local.DEBUG: print  2  
[2023-03-30 03:13:56] local.INFO: header  
[2023-03-30 03:13:56] local.CRITICAL: ****************************1  
[2023-03-30 03:13:56] local.ALERT: reach here  
[2023-03-30 03:13:56] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '4000.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'أربعة ألف  ر.ي.',
  ),
)  
[2023-03-30 03:13:56] local.ERROR: المبلغ  
[2023-03-30 03:13:56] local.ERROR: 4,000.00  
[2023-03-30 03:13:56] local.ERROR: مبلغ وقدرة  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 3  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 3  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 3  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 3  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 3  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 3  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 3  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 3  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 3  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 2  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 2  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 2  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 2  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 2  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 2  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 2  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 1  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 1  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 1  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 1  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 1  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 2  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 10013  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 10013  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 10013  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 10013  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 1  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 3  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 3  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 40  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 40  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 40  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 40  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.WARNING: 1  
[2023-03-30 03:13:56] local.WARNING: array (
  'ID' => 86,
  'Name' => 'فئة 25 جيجا 4000 ريال',
  'ServiceID' => 200,
  'Price' => 4000.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 4000.0,
)  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 200  
[2023-03-30 03:13:56] local.ALERT: 40  
[2023-03-30 03:13:56] local.CRITICAL: ****************************2  
[2023-03-30 03:13:56] local.CRITICAL: ****************************  
[2023-03-30 03:13:56] local.CRITICAL:   
[2023-03-30 03:13:56] local.CRITICAL: ****************************  
[2023-03-30 03:13:56] local.INFO: {
  "ClientBalanceResult": "9101.2000"
}  
[2023-03-30 03:13:56] local.INFO: array (
  'ClientBalanceResult' => '9101.2000',
)  
[2023-03-30 03:13:56] local.DEBUG: lattttef  
[2023-03-30 03:13:56] local.DEBUG: array (
  'ClientBalanceResult' => '9101.2000',
)  
[2023-03-30 03:13:56] local.INFO: transaction14  
[2023-03-30 03:13:56] local.INFO: first inquery phone = 103377914  
[2023-03-30 03:13:58] local.DEBUG: response querySubBalance  
[2023-03-30 03:13:58] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00##28-03-2023#0#0##.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 03:13:58] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '',
  4 => '28-03-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 25',
)  
[2023-03-30 03:13:58] local.DEBUG: print  before faction by provider price  
[2023-03-30 03:13:58] local.DEBUG: print  after faction by provider price  
[2023-03-30 03:13:58] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-30 03:13:58] local.DEBUG: print1  
[2023-03-30 03:13:58] local.DEBUG: print  2  
[2023-03-30 03:13:58] local.INFO: transaction1  
[2023-03-30 03:13:58] local.INFO: transaction2  
[2023-03-30 03:13:58] local.INFO: transaction3  
[2023-03-30 03:13:58] local.INFO: transaction4  
[2023-03-30 03:13:58] local.INFO: transaction4  
[2023-03-30 03:13:58] local.INFO: transaction5  
[2023-03-30 03:13:58] local.INFO: transaction6  
[2023-03-30 03:13:58] local.INFO: transaction7  
[2023-03-30 03:13:58] local.DEBUG: array (
  'AMT' => 4000.0,
  'CType' => 0,
  'FID' => 86,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103377914',
  'State' => 0,
  'lateflog' => '584586',
)  
[2023-03-30 03:13:58] local.INFO: transaction8  
[2023-03-30 03:13:59] local.INFO: transaction9  
[2023-03-30 03:13:59] local.INFO: transaction10  
[2023-03-30 03:13:59] local.INFO: transaction11  
[2023-03-30 03:13:59] local.INFO: 12  
[2023-03-30 03:13:59] local.INFO: transaction13  
[2023-03-30 03:13:59] local.INFO: transaction14  
[2023-03-30 03:13:59] local.INFO: transaction19  
[2023-03-30 03:13:59] local.INFO: transaction15  
[2023-03-30 03:13:59] local.INFO: transaction16  
[2023-03-30 03:13:59] local.INFO: 98#103377914#4000.00#0  
[2023-03-30 03:14:07] local.INFO: transaction18  
[2023-03-30 03:14:07] local.INFO: array (
  0 => 'OK',
  1 => '989,124.83',
  2 => 'NONE',
  3 => '61650437',
  4 => '4,000.00',
)  
[2023-03-30 03:42:42] local.INFO: header  
[2023-03-30 03:42:42] local.INFO: header after fliter  
[2023-03-30 03:42:42] local.INFO: Body  after fliter  
[2023-03-30 03:42:42] local.INFO: array (
)  
[2023-03-30 03:42:42] local.INFO: transaction14  
[2023-03-30 03:42:42] local.INFO: first inquery phone = 103335565  
[2023-03-30 03:42:44] local.DEBUG: response querySubBalance  
[2023-03-30 03:42:44] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#8.23 GB#02-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 03:42:44] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '8.23 GB',
  4 => '02-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-30 03:42:44] local.DEBUG: print  before faction by provider price  
[2023-03-30 03:42:44] local.DEBUG: print  after faction by provider price  
[2023-03-30 03:42:44] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-30 03:42:44] local.DEBUG: print1  
[2023-03-30 03:42:44] local.DEBUG: print  2  
[2023-03-30 04:29:27] local.INFO: header  
[2023-03-30 04:29:27] local.INFO: header after fliter  
[2023-03-30 04:29:27] local.INFO: Body  after fliter  
[2023-03-30 04:29:27] local.INFO: array (
)  
[2023-03-30 04:29:27] local.INFO: transaction14  
[2023-03-30 04:29:27] local.INFO: first inquery phone = 103377914  
[2023-03-30 04:29:30] local.DEBUG: response querySubBalance  
[2023-03-30 04:29:30] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#24.86 GB#30-04-2023#0#0##500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 04:29:30] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '24.86 GB',
  4 => '30-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '500.00',
  9 => '4G 25',
)  
[2023-03-30 04:29:30] local.DEBUG: print  before faction by provider price  
[2023-03-30 04:29:30] local.DEBUG: print  after faction by provider price  
[2023-03-30 04:29:30] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-30 04:29:30] local.DEBUG: print1  
[2023-03-30 04:29:30] local.DEBUG: print  2  
[2023-03-30 04:39:59] local.INFO: header  
[2023-03-30 04:39:59] local.INFO: header after fliter  
[2023-03-30 04:39:59] local.INFO: Body  after fliter  
[2023-03-30 04:39:59] local.INFO: array (
)  
[2023-03-30 04:39:59] local.INFO: transaction14  
[2023-03-30 04:39:59] local.INFO: first inquery phone = 103335565  
[2023-03-30 04:40:06] local.DEBUG: response querySubBalance  
[2023-03-30 04:40:06] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#8.15 GB#02-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 04:40:06] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '8.15 GB',
  4 => '02-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-30 04:40:06] local.DEBUG: print  before faction by provider price  
[2023-03-30 04:40:06] local.DEBUG: print  after faction by provider price  
[2023-03-30 04:40:06] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-30 04:40:06] local.DEBUG: print1  
[2023-03-30 04:40:06] local.DEBUG: print  2  
[2023-03-30 04:55:15] local.INFO: header  
[2023-03-30 04:55:15] local.INFO: header after fliter  
[2023-03-30 04:55:15] local.INFO: Body  after fliter  
[2023-03-30 04:55:15] local.INFO: array (
)  
[2023-03-30 04:55:15] local.INFO: transaction14  
[2023-03-30 04:55:15] local.INFO: first inquery phone = 106400598  
[2023-03-30 04:55:18] local.DEBUG: response querySubBalance  
[2023-03-30 04:55:18] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#6.96 MB#21-04-2023#0#0##9500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 04:55:18] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '6.96 MB',
  4 => '21-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 250',
)  
[2023-03-30 04:55:18] local.DEBUG: print  before faction by provider price  
[2023-03-30 04:55:18] local.DEBUG: print  after faction by provider price  
[2023-03-30 04:55:18] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-30 04:55:18] local.DEBUG: print1  
[2023-03-30 04:55:18] local.DEBUG: print  2  
[2023-03-30 19:08:46] local.INFO: header  
[2023-03-30 19:08:46] local.INFO: header after fliter  
[2023-03-30 19:08:46] local.INFO: Body  after fliter  
[2023-03-30 19:08:46] local.INFO: array (
)  
[2023-03-30 19:08:46] local.INFO: transaction14  
[2023-03-30 19:08:46] local.INFO: first inquery phone = 103330734  
[2023-03-30 19:08:52] local.DEBUG: response querySubBalance  
[2023-03-30 19:08:52] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#166.42 KB#22-04-2023#0#0##1500.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 19:08:52] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '166.42 KB',
  4 => '22-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1500.00',
  9 => '4G 15',
)  
[2023-03-30 19:08:52] local.DEBUG: print  before faction by provider price  
[2023-03-30 19:08:52] local.DEBUG: print  after faction by provider price  
[2023-03-30 19:08:52] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-30 19:08:52] local.DEBUG: print1  
[2023-03-30 19:08:52] local.DEBUG: print  2  
[2023-03-30 19:08:59] local.INFO: header  
[2023-03-30 19:08:59] local.CRITICAL: ****************************1  
[2023-03-30 19:08:59] local.ALERT: reach here  
[2023-03-30 19:08:59] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2023-03-30 19:08:59] local.ERROR: المبلغ  
[2023-03-30 19:08:59] local.ERROR: 2,400.00  
[2023-03-30 19:08:59] local.ERROR: مبلغ وقدرة  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 3  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 3  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 3  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 3  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 3  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 3  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 3  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 3  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 3  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 2  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 2  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 2  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 2  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 2  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 2  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 2  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 1  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 1  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 1  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 1  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 1  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 2  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 10013  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 10013  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 10013  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 10013  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 1  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 3  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 3  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 40  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 40  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 40  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 40  
[2023-03-30 19:08:59] local.WARNING: 1  
[2023-03-30 19:08:59] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 200  
[2023-03-30 19:08:59] local.ALERT: 40  
[2023-03-30 19:08:59] local.CRITICAL: ****************************2  
[2023-03-30 19:08:59] local.CRITICAL: ****************************  
[2023-03-30 19:08:59] local.CRITICAL:   
[2023-03-30 19:08:59] local.CRITICAL: ****************************  
[2023-03-30 19:09:00] local.INFO: {
  "ClientBalanceResult": "979590.8100"
}  
[2023-03-30 19:09:00] local.INFO: array (
  'ClientBalanceResult' => '979590.8100',
)  
[2023-03-30 19:09:00] local.DEBUG: lattttef  
[2023-03-30 19:09:00] local.DEBUG: array (
  'ClientBalanceResult' => '979590.8100',
)  
[2023-03-30 19:09:00] local.INFO: transaction14  
[2023-03-30 19:09:00] local.INFO: first inquery phone = 103330734  
[2023-03-30 19:09:03] local.DEBUG: response querySubBalance  
[2023-03-30 19:09:03] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#166.42 KB#22-04-2023#0#0##1500.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 19:09:03] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '166.42 KB',
  4 => '22-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1500.00',
  9 => '4G 15',
)  
[2023-03-30 19:09:03] local.DEBUG: print  before faction by provider price  
[2023-03-30 19:09:03] local.DEBUG: print  after faction by provider price  
[2023-03-30 19:09:03] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-30 19:09:03] local.DEBUG: print1  
[2023-03-30 19:09:03] local.DEBUG: print  2  
[2023-03-30 19:09:03] local.INFO: transaction1  
[2023-03-30 19:09:03] local.INFO: transaction2  
[2023-03-30 19:09:03] local.INFO: transaction3  
[2023-03-30 19:09:03] local.INFO: transaction4  
[2023-03-30 19:09:03] local.INFO: transaction4  
[2023-03-30 19:09:03] local.INFO: transaction7  
[2023-03-30 19:09:03] local.DEBUG: array (
  'AMT' => 2400.0,
  'CType' => 0,
  'FID' => 85,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '103330734',
  'State' => 0,
  'lateflog' => '327013',
)  
[2023-03-30 19:09:03] local.INFO: transaction8  
[2023-03-30 19:09:03] local.INFO: transaction9  
[2023-03-30 19:09:03] local.INFO: transaction10  
[2023-03-30 19:09:03] local.INFO: transaction11  
[2023-03-30 19:09:03] local.INFO: 12  
[2023-03-30 19:09:03] local.INFO: transaction13  
[2023-03-30 19:09:03] local.INFO: transaction14  
[2023-03-30 19:09:03] local.INFO: transaction19  
[2023-03-30 19:09:03] local.INFO: transaction15  
[2023-03-30 19:09:03] local.INFO: transaction16  
[2023-03-30 19:09:03] local.INFO: 98#103330734#2400.00#0  
[2023-03-30 19:09:15] local.INFO: transaction18  
[2023-03-30 19:09:15] local.INFO: array (
  0 => 'OK',
  1 => '238,337.83',
  2 => 'NONE',
  3 => '61682782',
  4 => '2,400.00',
)  
[2023-03-30 19:16:34] local.INFO: header  
[2023-03-30 19:16:34] local.INFO: header after fliter  
[2023-03-30 19:16:34] local.INFO: Body  after fliter  
[2023-03-30 19:16:34] local.INFO: array (
)  
[2023-03-30 19:16:34] local.INFO: transaction14  
[2023-03-30 19:16:34] local.INFO: first inquery phone = 103330734  
[2023-03-30 19:16:37] local.DEBUG: response querySubBalance  
[2023-03-30 19:16:37] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#15.00 GB#30-04-2023#0#0##1500.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 19:16:37] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '15.00 GB',
  4 => '30-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '1500.00',
  9 => '4G 15',
)  
[2023-03-30 19:16:37] local.DEBUG: print  before faction by provider price  
[2023-03-30 19:16:37] local.DEBUG: print  after faction by provider price  
[2023-03-30 19:16:37] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-30 19:16:37] local.DEBUG: print1  
[2023-03-30 19:16:37] local.DEBUG: print  2  
[2023-03-30 21:02:22] local.INFO: header  
[2023-03-30 21:02:22] local.INFO: header after fliter  
[2023-03-30 21:02:22] local.INFO: Body  after fliter  
[2023-03-30 21:02:22] local.INFO: array (
)  
[2023-03-30 21:02:22] local.INFO: transaction14  
[2023-03-30 21:02:22] local.INFO: first inquery phone = 106400860  
[2023-03-30 21:02:25] local.DEBUG: response querySubBalance  
[2023-03-30 21:02:25] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#2,400.00#2,400.00#15.00 GB#30-04-2023#0#0##.00#4G 15</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 21:02:25] local.DEBUG: array (
  0 => 'OK',
  1 => '2,400.00',
  2 => '2,400.00',
  3 => '15.00 GB',
  4 => '30-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '.00',
  9 => '4G 15',
)  
[2023-03-30 21:02:25] local.DEBUG: print  before faction by provider price  
[2023-03-30 21:02:25] local.DEBUG: print  after faction by provider price  
[2023-03-30 21:02:25] local.DEBUG: فئة 15 جيجا 2400 ريال  
[2023-03-30 21:02:25] local.DEBUG: print1  
[2023-03-30 21:02:25] local.DEBUG: print  2  
[2023-03-30 21:03:07] local.INFO: header  
[2023-03-30 21:03:07] local.INFO: header after fliter  
[2023-03-30 21:03:07] local.INFO: Body  after fliter  
[2023-03-30 21:03:07] local.INFO: array (
)  
[2023-03-30 21:03:07] local.INFO: transaction14  
[2023-03-30 21:03:07] local.INFO: first inquery phone = 106400363  
[2023-03-30 21:03:09] local.DEBUG: response querySubBalance  
[2023-03-30 21:03:09] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#2.98 GB#30-04-2023#0#0##9500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 21:03:09] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '2.98 GB',
  4 => '30-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 25',
)  
[2023-03-30 21:03:09] local.DEBUG: print  before faction by provider price  
[2023-03-30 21:03:09] local.DEBUG: print  after faction by provider price  
[2023-03-30 21:03:09] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-30 21:03:09] local.DEBUG: print1  
[2023-03-30 21:03:09] local.DEBUG: print  2  
[2023-03-30 21:07:32] local.INFO: header  
[2023-03-30 21:07:32] local.INFO: header after fliter  
[2023-03-30 21:07:32] local.INFO: Body  after fliter  
[2023-03-30 21:07:32] local.INFO: array (
)  
[2023-03-30 21:07:32] local.INFO: transaction14  
[2023-03-30 21:07:32] local.INFO: first inquery phone = 106400363  
[2023-03-30 21:07:36] local.DEBUG: response querySubBalance  
[2023-03-30 21:07:36] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#2.84 GB#30-04-2023#0#0##9500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 21:07:36] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '2.84 GB',
  4 => '30-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9500.00',
  9 => '4G 25',
)  
[2023-03-30 21:07:36] local.DEBUG: print  before faction by provider price  
[2023-03-30 21:07:36] local.DEBUG: print  after faction by provider price  
[2023-03-30 21:07:36] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-30 21:07:36] local.DEBUG: print1  
[2023-03-30 21:07:36] local.DEBUG: print  2  
[2023-03-30 21:48:19] local.INFO: header  
[2023-03-30 21:48:19] local.INFO: header after fliter  
[2023-03-30 21:48:19] local.INFO: Body  after fliter  
[2023-03-30 21:48:19] local.INFO: array (
)  
[2023-03-30 21:48:19] local.INFO: transaction14  
[2023-03-30 21:48:19] local.INFO: first inquery phone = 103322059  
[2023-03-30 21:48:23] local.DEBUG: response querySubBalance  
[2023-03-30 21:48:23] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#26,000.00#26,000.00#207.88 GB#28-04-2023#0#0##8500.00#4G 250</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 21:48:23] local.DEBUG: array (
  0 => 'OK',
  1 => '26,000.00',
  2 => '26,000.00',
  3 => '207.88 GB',
  4 => '28-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '8500.00',
  9 => '4G 250',
)  
[2023-03-30 21:48:23] local.DEBUG: print  before faction by provider price  
[2023-03-30 21:48:24] local.DEBUG: print  after faction by provider price  
[2023-03-30 21:48:24] local.DEBUG: فئة 250 جيجا 26000 ريال  
[2023-03-30 21:48:24] local.DEBUG: print1  
[2023-03-30 21:48:24] local.DEBUG: print  2  
[2023-03-30 22:15:45] local.INFO: header  
[2023-03-30 22:15:45] local.INFO: header after fliter  
[2023-03-30 22:15:45] local.INFO: Body  after fliter  
[2023-03-30 22:15:45] local.INFO: array (
)  
[2023-03-30 22:15:45] local.INFO: transaction14  
[2023-03-30 22:15:45] local.INFO: first inquery phone = 103330817  
[2023-03-30 22:15:49] local.DEBUG: response querySubBalance  
[2023-03-30 22:15:49] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#8,000.00#8,000.00#40.13 GB#26-04-2023#0#0##9000.00#4G 60</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 22:15:49] local.DEBUG: array (
  0 => 'OK',
  1 => '8,000.00',
  2 => '8,000.00',
  3 => '40.13 GB',
  4 => '26-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '9000.00',
  9 => '4G 60',
)  
[2023-03-30 22:15:49] local.DEBUG: print  before faction by provider price  
[2023-03-30 22:15:49] local.DEBUG: print  after faction by provider price  
[2023-03-30 22:15:49] local.DEBUG: فئة 60 جيجا 8000 ريال  
[2023-03-30 22:15:49] local.DEBUG: print1  
[2023-03-30 22:15:49] local.DEBUG: print  2  
[2023-03-30 22:51:35] local.INFO: header  
[2023-03-30 22:51:35] local.INFO: header after fliter  
[2023-03-30 22:51:35] local.INFO: Body  after fliter  
[2023-03-30 22:51:35] local.INFO: array (
)  
[2023-03-30 22:51:35] local.INFO: transaction14  
[2023-03-30 22:51:35] local.INFO: first inquery phone = 103377914  
[2023-03-30 22:51:38] local.DEBUG: response querySubBalance  
[2023-03-30 22:51:38] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#4,000.00#4,000.00#24.86 GB#30-04-2023#0#0##500.00#4G 25</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 22:51:38] local.DEBUG: array (
  0 => 'OK',
  1 => '4,000.00',
  2 => '4,000.00',
  3 => '24.86 GB',
  4 => '30-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '500.00',
  9 => '4G 25',
)  
[2023-03-30 22:51:38] local.DEBUG: print  before faction by provider price  
[2023-03-30 22:51:38] local.DEBUG: print  after faction by provider price  
[2023-03-30 22:51:38] local.DEBUG: فئة 25 جيجا 4000 ريال  
[2023-03-30 22:51:38] local.DEBUG: print1  
[2023-03-30 22:51:38] local.DEBUG: print  2  
[2023-03-30 23:18:55] local.INFO: header  
[2023-03-30 23:18:55] local.INFO: header after fliter  
[2023-03-30 23:18:55] local.INFO: Body  after fliter  
[2023-03-30 23:18:55] local.INFO: array (
)  
[2023-03-30 23:18:55] local.INFO: transaction14  
[2023-03-30 23:18:55] local.INFO: first inquery phone = 103322192  
[2023-03-30 23:18:59] local.DEBUG: response querySubBalance  
[2023-03-30 23:18:59] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#46,000.00#46,000.00#162.00 GB#18-04-2023#0#0##5000.00#4G 500</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2023-03-30 23:18:59] local.DEBUG: array (
  0 => 'OK',
  1 => '46,000.00',
  2 => '46,000.00',
  3 => '162.00 GB',
  4 => '18-04-2023',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '5000.00',
  9 => '4G 500',
)  
[2023-03-30 23:18:59] local.DEBUG: print  before faction by provider price  
[2023-03-30 23:18:59] local.DEBUG: print  after faction by provider price  
[2023-03-30 23:18:59] local.DEBUG: فئة 500 جيجا 46000 ريال  
[2023-03-30 23:18:59] local.DEBUG: print1  
[2023-03-30 23:18:59] local.DEBUG: print  2  
