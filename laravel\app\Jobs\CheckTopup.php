<?php

namespace App\Jobs;

use App\Services\NetCoinService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\TwasulService;

use Illuminate\Support\Facades\Log;
class CheckTopup implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $topup;
    private $paymentOrOrder;
    private $journal;
    private $type;
    public function __construct($topupParam, $journalParam, $topupOrGame = 'TOPUP')
    {
        Log::info('__construct 0');
        if ($topupOrGame == 'GAME') {
            Log::info('__construct');
            $this->paymentOrOrder = $topupParam;
        } else {
            $this->topup = $topupParam;
        }
        $this->type = $topupOrGame;
        $this->journal = $journalParam;
        $this->queue = 'check-topup';
        $this->connection = 'database';
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('__construct 5');
        if ($this->type == 'GAME') {
           Log::info('__construct 3'); 
            $confimTopup = new TwasulService( $this->paymentOrOrder, $this->journal, 'GAME');
            $confimTopup->checkGame();
        } else {
            Log::info('__construct 4');

            if ($this->topup->ServiceID == 40 || $this->topup->ServiceID == 2 || $this->topup->ServiceID == 20033) {
                            Log::info('__construct TwasulService');

                $confimTopup = new TwasulService($this->topup, $this->journal, 'TOPUP');
            } else {
                            Log::info('__construct NetCoinService');

                $confimTopup = new NetCoinService($this->topup, $this->journal);
            }

            $confimTopup->check();
        }


        // $confimTopup=new NetCoinService($this->topup,$this->journal);
        // $confimTopup->check();
    }
}
