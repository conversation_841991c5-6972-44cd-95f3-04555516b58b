<?php
/**
 * نظام تسديد الهاتف والاتصالات
 * واجهة كاملة لإدارة عمليات الشحن والدفع
 */

// إعدادات أساسية
error_reporting(E_ALL);
ini_set('display_errors', 1);
date_default_timezone_set('Asia/Riyadh');

// تحميل متغيرات البيئة
function loadEnv($path) {
    if (!file_exists($path)) return [];
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $env = [];
    
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value, '"\'');
        $env[$key] = $value;
    }
    
    return $env;
}

$env = loadEnv('../.env');

// محاكاة بيانات الشبكات
$networks = [
    1 => ['name' => 'سبأفون', 'code' => 'sabafon', 'color' => '#ff6b35'],
    2 => ['name' => 'MTN', 'code' => 'mtn', 'color' => '#ffcc02'],
    40 => ['name' => 'يمن موبايل', 'code' => 'yemenmobile', 'color' => '#00a651'],
    20024 => ['name' => 'واي', 'code' => 'y', 'color' => '#e31e24']
];

// محاكاة فئات الشحن
$topupAmounts = [
    500, 1000, 2000, 3000, 5000, 10000, 15000, 20000
];

// محاكاة بيانات الألعاب والبطاقات
$gameCards = [
    ['name' => 'PUBG Mobile', 'amounts' => [60, 325, 660, 1800, 3850]],
    ['name' => 'Free Fire', 'amounts' => [100, 310, 520, 1060, 2180]],
    ['name' => 'Fortnite', 'amounts' => [1000, 2800, 5000, 7500, 13500]],
    ['name' => 'Steam Wallet', 'amounts' => [5, 10, 20, 50, 100]]
];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام تسديد الهاتف والاتصالات</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            font-size: 1.1em;
        }
        .services {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .service-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .service-card:hover {
            transform: translateY(-5px);
        }
        .service-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .service-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            color: white;
            margin-left: 15px;
        }
        .service-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }
        .network-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        .network-btn {
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
            font-weight: bold;
        }
        .network-btn:hover, .network-btn.active {
            border-color: #667eea;
            background: #f8f9ff;
        }
        .amount-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        .amount-btn {
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
            font-weight: bold;
        }
        .amount-btn:hover, .amount-btn.active {
            border-color: #28a745;
            background: #f8fff9;
            color: #28a745;
        }
        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .status-panel {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #e1e5e9;
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .status-pending {
            color: #ffc107;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .game-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        .game-card {
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }
        .game-card:hover, .game-card.active {
            border-color: #667eea;
            background: #f8f9ff;
        }
        .api-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #17a2b8;
        }
        .api-endpoint {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>📱 نظام تسديد الهاتف والاتصالات</h1>
            <p>شحن رصيد الهاتف، بطاقات الألعاب، والخدمات الرقمية</p>
        </div>

        <!-- Services -->
        <div class="services">
            <!-- خدمة شحن الرصيد -->
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon" style="background: #28a745;">📞</div>
                    <div class="service-title">شحن رصيد الهاتف</div>
                </div>

                <form id="topupForm">
                    <div class="form-group">
                        <label>رقم الهاتف</label>
                        <input type="tel" class="form-control" id="phoneNumber" placeholder="77xxxxxxx" maxlength="9">
                    </div>

                    <div class="form-group">
                        <label>اختر الشبكة</label>
                        <div class="network-grid">
                            <?php foreach ($networks as $id => $network): ?>
                                <div class="network-btn" data-network="<?php echo $id; ?>" style="border-color: <?php echo $network['color']; ?>;">
                                    <?php echo $network['name']; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>اختر المبلغ (ريال)</label>
                        <div class="amount-grid">
                            <?php foreach ($topupAmounts as $amount): ?>
                                <div class="amount-btn" data-amount="<?php echo $amount; ?>">
                                    <?php echo number_format($amount); ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <button type="submit" class="btn" id="topupBtn" disabled>
                        شحن الرصيد
                    </button>
                </form>
            </div>

            <!-- خدمة بطاقات الألعاب -->
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon" style="background: #e31e24;">🎮</div>
                    <div class="service-title">بطاقات الألعاب</div>
                </div>

                <form id="gameForm">
                    <div class="form-group">
                        <label>اختر اللعبة</label>
                        <div class="game-grid">
                            <?php foreach ($gameCards as $index => $game): ?>
                                <div class="game-card" data-game="<?php echo $index; ?>">
                                    <?php echo $game['name']; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>معرف اللاعب</label>
                        <input type="text" class="form-control" id="playerId" placeholder="أدخل معرف اللاعب">
                    </div>

                    <div class="form-group" id="gameAmounts" style="display: none;">
                        <label>اختر الفئة</label>
                        <div class="amount-grid" id="gameAmountGrid">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>

                    <button type="submit" class="btn" id="gameBtn" disabled>
                        شراء البطاقة
                    </button>
                </form>
            </div>
        </div>

        <!-- حالة العمليات -->
        <div class="status-panel">
            <h3>📊 حالة العمليات</h3>
            <div id="operationsStatus">
                <div class="status-item">
                    <span>خدمة الشحن</span>
                    <span class="status-success">متاحة ✅</span>
                </div>
                <div class="status-item">
                    <span>خدمة الألعاب</span>
                    <span class="status-success">متاحة ✅</span>
                </div>
                <div class="status-item">
                    <span>خدمة الدفع</span>
                    <span class="status-success">متاحة ✅</span>
                </div>
                <div class="status-item">
                    <span>آخر تحديث</span>
                    <span><?php echo date('Y-m-d H:i:s'); ?></span>
                </div>
            </div>
        </div>

        <!-- معلومات API -->
        <div class="api-info">
            <h3>🔗 API Endpoints</h3>
            <p><strong>شحن الرصيد:</strong></p>
            <div class="api-endpoint">POST /PaymentService/api/v1/ExecuteTopup</div>
            
            <p><strong>استعلام الرصيد:</strong></p>
            <div class="api-endpoint">GET /PaymentService/api/v1/Inquery/{services}/{array}/{phone}</div>
            
            <p><strong>بطاقات الألعاب:</strong></p>
            <div class="api-endpoint">POST /PaymentService/api/v1/execute</div>
            
            <p><strong>تحديث الدفع:</strong></p>
            <div class="api-endpoint">GET /web-hook-payment</div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let selectedNetwork = null;
        let selectedAmount = null;
        let selectedGame = null;
        let selectedGameAmount = null;

        // بيانات الألعاب
        const gameData = <?php echo json_encode($gameCards); ?>;

        // اختيار الشبكة
        document.querySelectorAll('.network-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.network-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                selectedNetwork = this.dataset.network;
                checkTopupForm();
            });
        });

        // اختيار مبلغ الشحن
        document.querySelectorAll('.amount-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.amount-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                selectedAmount = this.dataset.amount;
                checkTopupForm();
            });
        });

        // اختيار اللعبة
        document.querySelectorAll('.game-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.game-card').forEach(c => c.classList.remove('active'));
                this.classList.add('active');
                selectedGame = this.dataset.game;
                showGameAmounts(selectedGame);
                checkGameForm();
            });
        });

        // عرض فئات اللعبة
        function showGameAmounts(gameIndex) {
            const game = gameData[gameIndex];
            const container = document.getElementById('gameAmountGrid');
            const amountsDiv = document.getElementById('gameAmounts');
            
            container.innerHTML = '';
            game.amounts.forEach(amount => {
                const btn = document.createElement('div');
                btn.className = 'amount-btn';
                btn.dataset.amount = amount;
                btn.textContent = amount;
                btn.addEventListener('click', function() {
                    document.querySelectorAll('#gameAmountGrid .amount-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    selectedGameAmount = amount;
                    checkGameForm();
                });
                container.appendChild(btn);
            });
            
            amountsDiv.style.display = 'block';
        }

        // فحص نموذج الشحن
        function checkTopupForm() {
            const phone = document.getElementById('phoneNumber').value;
            const btn = document.getElementById('topupBtn');
            
            if (phone.length >= 9 && selectedNetwork && selectedAmount) {
                btn.disabled = false;
            } else {
                btn.disabled = true;
            }
        }

        // فحص نموذج الألعاب
        function checkGameForm() {
            const playerId = document.getElementById('playerId').value;
            const btn = document.getElementById('gameBtn');
            
            if (playerId.length > 0 && selectedGame !== null && selectedGameAmount) {
                btn.disabled = false;
            } else {
                btn.disabled = true;
            }
        }

        // مراقبة إدخال رقم الهاتف
        document.getElementById('phoneNumber').addEventListener('input', checkTopupForm);
        document.getElementById('playerId').addEventListener('input', checkGameForm);

        // إرسال نموذج الشحن
        document.getElementById('topupForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const phone = document.getElementById('phoneNumber').value;
            const data = {
                phone: phone,
                network: selectedNetwork,
                amount: selectedAmount
            };
            
            alert(`تم إرسال طلب الشحن:\nالرقم: ${phone}\nالشبكة: ${selectedNetwork}\nالمبلغ: ${selectedAmount} ريال`);
            
            // هنا يمكن إرسال البيانات للـ API
            console.log('Topup request:', data);
        });

        // إرسال نموذج الألعاب
        document.getElementById('gameForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const playerId = document.getElementById('playerId').value;
            const gameName = gameData[selectedGame].name;
            const data = {
                playerId: playerId,
                game: selectedGame,
                amount: selectedGameAmount
            };
            
            alert(`تم إرسال طلب البطاقة:\nاللعبة: ${gameName}\nمعرف اللاعب: ${playerId}\nالفئة: ${selectedGameAmount}`);
            
            // هنا يمكن إرسال البيانات للـ API
            console.log('Game card request:', data);
        });
    </script>
</body>
</html>
