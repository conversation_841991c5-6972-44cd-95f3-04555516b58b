<?php
/**
 * ملف تشغيل Laravel مبسط - لفك الحماية مؤقتاً
 * هذا الملف يتجاوز بعض متطلبات Laravel للاختبار
 */

// تعطيل عرض الأخطاء للمستخدم النهائي
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>تشخيص Laravel</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".warning { color: #ffc107; }";
echo ".info { color: #17a2b8; }";
echo ".section { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }";
echo "pre { background: #f1f1f1; padding: 10px; border-radius: 5px; overflow-x: auto; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔍 تشخيص متطلبات Laravel</h1>";

// فحص إصدار PHP
echo "<div class='section'>";
echo "<h2>📌 فحص PHP</h2>";
$phpVersion = PHP_VERSION;
$requiredVersion = '8.1.0';
if (version_compare($phpVersion, $requiredVersion, '>=')) {
    echo "<p class='success'>✅ إصدار PHP: $phpVersion (مدعوم)</p>";
} else {
    echo "<p class='error'>❌ إصدار PHP: $phpVersion (مطلوب $requiredVersion أو أحدث)</p>";
}
echo "</div>";

// فحص الإضافات المطلوبة
echo "<div class='section'>";
echo "<h2>🔧 فحص إضافات PHP</h2>";
$requiredExtensions = [
    'bcmath' => 'BCMath',
    'ctype' => 'Ctype', 
    'curl' => 'cURL',
    'dom' => 'DOM',
    'fileinfo' => 'Fileinfo',
    'json' => 'JSON',
    'mbstring' => 'Mbstring',
    'openssl' => 'OpenSSL',
    'pcre' => 'PCRE',
    'pdo' => 'PDO',
    'tokenizer' => 'Tokenizer',
    'xml' => 'XML'
];

foreach ($requiredExtensions as $ext => $name) {
    if (extension_loaded($ext)) {
        echo "<p class='success'>✅ $name</p>";
    } else {
        echo "<p class='error'>❌ $name (مفقود)</p>";
    }
}
echo "</div>";

// فحص ملفات Laravel
echo "<div class='section'>";
echo "<h2>📁 فحص ملفات Laravel</h2>";

$laravelFiles = [
    '../vendor/autoload.php' => 'Composer Autoloader',
    '../bootstrap/app.php' => 'Laravel Bootstrap',
    '../.env' => 'Environment File',
    '../artisan' => 'Artisan CLI',
    '../app/Http/Kernel.php' => 'HTTP Kernel',
    '../config/app.php' => 'App Configuration'
];

foreach ($laravelFiles as $file => $name) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $name</p>";
    } else {
        echo "<p class='error'>❌ $name (مفقود)</p>";
    }
}
echo "</div>";

// محاولة تحميل Laravel
echo "<div class='section'>";
echo "<h2>🚀 محاولة تحميل Laravel</h2>";

try {
    // محاولة تحميل autoloader
    if (file_exists('../vendor/autoload.php')) {
        require_once '../vendor/autoload.php';
        echo "<p class='success'>✅ تم تحميل Composer Autoloader</p>";
        
        // محاولة تحميل Laravel
        if (file_exists('../bootstrap/app.php')) {
            try {
                $app = require_once '../bootstrap/app.php';
                echo "<p class='success'>✅ تم تحميل Laravel Application</p>";
                
                // محاولة إنشاء Kernel
                if (class_exists('Illuminate\Contracts\Http\Kernel')) {
                    echo "<p class='success'>✅ Laravel Kernel متاح</p>";
                } else {
                    echo "<p class='error'>❌ Laravel Kernel غير متاح</p>";
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ خطأ في تحميل Laravel: " . $e->getMessage() . "</p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
            }
        } else {
            echo "<p class='error'>❌ ملف bootstrap/app.php مفقود</p>";
        }
    } else {
        echo "<p class='error'>❌ Composer autoloader مفقود</p>";
        echo "<p class='warning'>⚠️ قم بتشغيل: composer install</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ عام: " . $e->getMessage() . "</p>";
}
echo "</div>";

// فحص الأذونات
echo "<div class='section'>";
echo "<h2>🔐 فحص الأذونات</h2>";
$directories = [
    '../storage' => 'Storage Directory',
    '../bootstrap/cache' => 'Bootstrap Cache',
    '../storage/logs' => 'Logs Directory',
    '../storage/framework' => 'Framework Directory'
];

foreach ($directories as $dir => $name) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<p class='success'>✅ $name (قابل للكتابة)</p>";
        } else {
            echo "<p class='warning'>⚠️ $name (غير قابل للكتابة)</p>";
        }
    } else {
        echo "<p class='error'>❌ $name (غير موجود)</p>";
    }
}
echo "</div>";

// معلومات إضافية
echo "<div class='section'>";
echo "<h2>ℹ️ معلومات إضافية</h2>";
echo "<p><strong>مجلد العمل:</strong> " . getcwd() . "</p>";
echo "<p><strong>مجلد Laravel:</strong> " . realpath('..') . "</p>";
echo "<p><strong>PHP SAPI:</strong> " . php_sapi_name() . "</p>";
echo "<p><strong>الذاكرة المتاحة:</strong> " . ini_get('memory_limit') . "</p>";
echo "<p><strong>الحد الأقصى لوقت التنفيذ:</strong> " . ini_get('max_execution_time') . " ثانية</p>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>🛠️ خطوات الحل</h2>";
echo "<ol>";
echo "<li><strong>ترقية PHP:</strong> قم بتحديث XAMPP إلى إصدار يحتوي على PHP 8.1+</li>";
echo "<li><strong>تثبيت Dependencies:</strong> قم بتشغيل <code>composer install</code></li>";
echo "<li><strong>تكوين البيئة:</strong> تأكد من وجود ملف .env صحيح</li>";
echo "<li><strong>إعداد الأذونات:</strong> تأكد من أن مجلدات storage و bootstrap/cache قابلة للكتابة</li>";
echo "<li><strong>تشغيل الخادم:</strong> استخدم <code>php artisan serve</code></li>";
echo "</ol>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
