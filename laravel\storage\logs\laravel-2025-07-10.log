[2025-07-10 18:35:58] local.INFO: header  
[2025-07-10 18:35:59] local.INFO: header after fliter  
[2025-07-10 18:35:59] local.INFO: Body  after fliter  
[2025-07-10 18:35:59] local.INFO: array (
)  
[2025-07-10 18:35:59] local.INFO: transaction14  
[2025-07-10 18:35:59] local.INFO: first inquery phone = 107213306  
[2025-07-10 18:36:02] local.DEBUG: response querySubBalance  
[2025-07-10 18:36:02] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-10 18:36:02] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-10 18:36:02] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-10 18:36:02] local.DEBUG: print  before faction by provider price  
[2025-07-10 18:36:03] local.DEBUG: print  after faction by provider price  
[2025-07-10 18:36:06] local.INFO: header  
[2025-07-10 18:36:06] local.INFO: header after fliter  
[2025-07-10 18:36:06] local.INFO: Body  after fliter  
[2025-07-10 18:36:06] local.INFO: array (
)  
[2025-07-10 18:36:06] local.INFO: transaction14  
[2025-07-10 18:36:06] local.INFO: first inquery phone = 107213306  
[2025-07-10 18:36:06] local.DEBUG: response querySubBalance  
[2025-07-10 18:36:06] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-10 18:36:06] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-10 18:36:06] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-10 18:36:06] local.DEBUG: print  before faction by provider price  
[2025-07-10 18:36:06] local.DEBUG: print  after faction by provider price  
[2025-07-10 23:08:51] local.INFO: header  
[2025-07-10 23:08:51] local.INFO: header after fliter  
[2025-07-10 23:08:51] local.INFO: Body  after fliter  
[2025-07-10 23:08:51] local.INFO: array (
)  
[2025-07-10 23:08:51] local.INFO: transaction14  
[2025-07-10 23:08:51] local.INFO: first inquery phone = 107213306  
[2025-07-10 23:08:52] local.DEBUG: response querySubBalance  
[2025-07-10 23:08:52] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-10 23:08:52] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-10 23:08:52] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-10 23:08:52] local.DEBUG: print  before faction by provider price  
[2025-07-10 23:08:52] local.DEBUG: print  after faction by provider price  
