<?php
/**
 * تطبيق Laravel مبسط - يعمل بدون متطلبات معقدة
 * هذا حل مؤقت لتشغيل النظام مع PHP 8.0
 */

// إعدادات أساسية
error_reporting(E_ALL);
ini_set('display_errors', 1);
date_default_timezone_set('Asia/Riyadh');

// محاولة تحميل .env
function loadEnv($path) {
    if (!file_exists($path)) return [];
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $env = [];
    
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value, '"\'');
        $env[$key] = $value;
    }
    
    return $env;
}

// تحميل متغيرات البيئة
$env = loadEnv('../.env');

// إعداد قاعدة البيانات (اختياري)
$dbConfig = [
    'host' => $env['DB_HOST'] ?? 'localhost',
    'port' => $env['DB_PORT'] ?? '3306',
    'database' => $env['DB_DATABASE'] ?? '',
    'username' => $env['DB_USERNAME'] ?? '',
    'password' => $env['DB_PASSWORD'] ?? ''
];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $env['APP_NAME'] ?? 'Laravel App'; ?></title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        .success { border-left-color: #28a745; }
        .warning { border-left-color: #ffc107; }
        .error { border-left-color: #dc3545; }
        .info { border-left-color: #17a2b8; }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background 0.3s;
        }
        .btn:hover { background: #5a6fd8; }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-warning { background: #ffc107; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .code {
            background: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 <?php echo $env['APP_NAME'] ?? 'Laravel Application'; ?></h1>
            <p>تطبيق Laravel يعمل بنجاح مع PHP <?php echo PHP_VERSION; ?></p>
            <div>
                <span class="status-indicator status-online"></span>
                <span>الخادم متصل</span>
                <span class="status-indicator <?php echo !empty($env['DB_HOST']) ? 'status-warning' : 'status-offline'; ?>"></span>
                <span>قاعدة البيانات <?php echo !empty($env['DB_HOST']) ? 'معطلة مؤقتاً' : 'غير متصلة'; ?></span>
            </div>
        </div>

        <div class="content">
            <!-- معلومات النظام -->
            <div class="card info">
                <h2>📊 معلومات النظام</h2>
                <div class="grid">
                    <div>
                        <h3>معلومات PHP</h3>
                        <table>
                            <tr><td>الإصدار</td><td><?php echo PHP_VERSION; ?></td></tr>
                            <tr><td>SAPI</td><td><?php echo php_sapi_name(); ?></td></tr>
                            <tr><td>الذاكرة</td><td><?php echo ini_get('memory_limit'); ?></td></tr>
                            <tr><td>وقت التنفيذ</td><td><?php echo ini_get('max_execution_time'); ?>s</td></tr>
                        </table>
                    </div>
                    <div>
                        <h3>معلومات التطبيق</h3>
                        <table>
                            <tr><td>البيئة</td><td><?php echo $env['APP_ENV'] ?? 'production'; ?></td></tr>
                            <tr><td>التصحيح</td><td><?php echo ($env['APP_DEBUG'] ?? 'false') === 'true' ? 'مفعل' : 'معطل'; ?></td></tr>
                            <tr><td>الرابط</td><td><?php echo $env['APP_URL'] ?? 'http://localhost'; ?></td></tr>
                            <tr><td>المنطقة الزمنية</td><td><?php echo date_default_timezone_get(); ?></td></tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- حالة المكونات -->
            <div class="card">
                <h2>🔧 حالة المكونات</h2>
                <div class="grid">
                    <?php
                    $components = [
                        'PHP Extensions' => [
                            'cURL' => extension_loaded('curl'),
                            'JSON' => extension_loaded('json'),
                            'OpenSSL' => extension_loaded('openssl'),
                            'PDO' => extension_loaded('pdo'),
                            'Mbstring' => extension_loaded('mbstring'),
                            'XML' => extension_loaded('xml')
                        ],
                        'Laravel Files' => [
                            'Autoloader' => file_exists('../vendor/autoload.php'),
                            'Bootstrap' => file_exists('../bootstrap/app.php'),
                            'Environment' => file_exists('../.env'),
                            'Artisan' => file_exists('../artisan'),
                            'Routes' => file_exists('../routes/web.php')
                        ],
                        'Directories' => [
                            'Storage' => is_writable('../storage'),
                            'Cache' => is_writable('../bootstrap/cache'),
                            'Logs' => is_writable('../storage/logs'),
                            'Public' => is_writable('.')
                        ]
                    ];

                    foreach ($components as $category => $items): ?>
                        <div>
                            <h3><?php echo $category; ?></h3>
                            <table>
                                <?php foreach ($items as $name => $status): ?>
                                    <tr>
                                        <td><?php echo $name; ?></td>
                                        <td>
                                            <span class="status-indicator <?php echo $status ? 'status-online' : 'status-offline'; ?>"></span>
                                            <?php echo $status ? 'متاح' : 'غير متاح'; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </table>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- أدوات التطوير -->
            <div class="card success">
                <h2>🛠️ أدوات التطوير</h2>
                <p>يمكنك استخدام هذه الأدوات لإدارة التطبيق:</p>
                <div>
                    <a href="?action=phpinfo" class="btn">معلومات PHP</a>
                    <a href="?action=routes" class="btn">عرض المسارات</a>
                    <a href="?action=config" class="btn">عرض التكوين</a>
                    <a href="?action=logs" class="btn">عرض السجلات</a>
                </div>
            </div>

            <?php
            // معالجة الإجراءات
            if (isset($_GET['action'])) {
                echo "<div class='card'>";
                switch ($_GET['action']) {
                    case 'phpinfo':
                        echo "<h2>معلومات PHP</h2>";
                        echo "<div class='code'>";
                        ob_start();
                        phpinfo();
                        $phpinfo = ob_get_clean();
                        // استخراج الجزء المهم فقط
                        preg_match('%<body>(.*)</body>%siU', $phpinfo, $matches);
                        echo $matches[1] ?? 'غير متاح';
                        echo "</div>";
                        break;
                        
                    case 'routes':
                        echo "<h2>مسارات التطبيق</h2>";
                        if (file_exists('../routes/web.php')) {
                            echo "<div class='code'>";
                            echo htmlspecialchars(file_get_contents('../routes/web.php'));
                            echo "</div>";
                        } else {
                            echo "<p>ملف المسارات غير موجود</p>";
                        }
                        break;
                        
                    case 'config':
                        echo "<h2>تكوين البيئة</h2>";
                        echo "<div class='code'>";
                        foreach ($env as $key => $value) {
                            // إخفاء كلمات المرور
                            if (strpos($key, 'PASSWORD') !== false || strpos($key, 'SECRET') !== false) {
                                $value = str_repeat('*', strlen($value));
                            }
                            echo htmlspecialchars("$key = $value") . "\n";
                        }
                        echo "</div>";
                        break;
                        
                    case 'logs':
                        echo "<h2>سجلات النظام</h2>";
                        $logFile = '../storage/logs/laravel.log';
                        if (file_exists($logFile)) {
                            echo "<div class='code'>";
                            $logs = file_get_contents($logFile);
                            echo htmlspecialchars(substr($logs, -2000)); // آخر 2000 حرف
                            echo "</div>";
                        } else {
                            echo "<p>ملف السجلات غير موجود</p>";
                        }
                        break;
                }
                echo "</div>";
            }
            ?>

            <!-- تعليمات الترقية -->
            <div class="card warning">
                <h2>⚠️ ملاحظات مهمة</h2>
                <p>هذا تطبيق مبسط يعمل مع PHP 8.0. للحصول على الوظائف الكاملة:</p>
                <ol>
                    <li><strong>ترقية PHP:</strong> قم بتحديث XAMPP إلى إصدار يحتوي على PHP 8.1+</li>
                    <li><strong>تثبيت Composer:</strong> قم بتشغيل <code>composer install</code></li>
                    <li><strong>تكوين قاعدة البيانات:</strong> قم بإعداد اتصال قاعدة البيانات</li>
                    <li><strong>تشغيل Laravel:</strong> استخدم <code>php artisan serve</code></li>
                </ol>
            </div>

            <div class="card">
                <h2>📞 معلومات الاتصال</h2>
                <p>الوقت الحالي: <?php echo date('Y-m-d H:i:s'); ?></p>
                <p>عنوان IP: <?php echo $_SERVER['REMOTE_ADDR'] ?? 'غير معروف'; ?></p>
                <p>المتصفح: <?php echo $_SERVER['HTTP_USER_AGENT'] ?? 'غير معروف'; ?></p>
            </div>
        </div>
    </div>
</body>
</html>
