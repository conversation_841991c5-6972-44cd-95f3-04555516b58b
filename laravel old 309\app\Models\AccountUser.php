<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountUser extends Model
{
    use HasFactory;
    protected $table = 'AccountUser';
    public $timestamps = false;

    protected $fillable = [
        'ID',
        'RowVersion',
        'RoleID',
        'ParentID',
        'UserID',
        'PrimaryUser',
        'DeviceID',
        'CreatedBy',
        'BranchID',
        'CreatedTime',
        'AccountID'
    ];
    public function topups()
    {
        return $this->hasMany(Topup::class,'CreditorAccountID','ID');
    }
    public function userInfo()
    {
        return $this->hasMany(UserInfo::class,'ID','UserID');
    }
    public function userParentInfo()
    {
        return $this->hasMany(UserInfo::class,'ID','ParentID');
    }
    public function account()
    {
        return $this->hasMany(Account::class,'ID','AccountID');
    }
}
