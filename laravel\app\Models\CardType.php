<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CardType extends Model
{
    use HasFactory;

    protected $table = 'CardType';
    public $timestamps = false;

    protected $fillable = [
        'ID',
        'CardTypeID',
        'RowVersion',
        'Number',
        'Name',
        'Image',
        'Active',
        'Note',
        'Description',
        'Type',
        'AccountID',
        'BranchID',
        'CreatedBy',
        'CreatedTime',
    ];

    // public function branch()
    // {
    //     return $this->belongsTo(Branch::class, 'BranchID');
    // }

    public function account()
    {
        return $this->belongsTo(Account::class, 'AccountID');
    }

    public function createdByUser()
    {
        return $this->belongsTo(UserInfo::class, 'CreatedBy');
    }
}
