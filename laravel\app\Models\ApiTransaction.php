<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ApiTransaction extends Model
{
    use HasFactory;
    protected $table = 'ApiTransaction';
    public $timestamps = false;

    protected $fillable = [
        'ID',
        'Number',
        'UserID',
        'UserType',
        'Identifier',
        'TransactionID',
        'Channel',
        'VC',
        'SessionID',
        'SessionNo',
        'EntityName',
        'ApiType',
        'DeviceInfo',
        'UserAgent',
        'Host',
        'IpAddress',
        'Type',
        'Status',
        'Location',
        'Note',
        'CreatedTime'
    ];
}
