<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Agent extends Model
{
    use HasFactory;
    protected $table = 'Agent';

    public $timestamps = false;

    protected $fillable = [
        'ID',
        'RowVersion',
        'Number',
        'Name',
        'AccountID',
        'PhoneNumber',
        'ContactNumber',
        'Address',
        'Note',
        'Email',
        'CardType',
        'CardNumber',
        'CardIssuePlace',
        'CardIssueDate',
        'ImageName',
        'CreatedBy',
        'BranchID',
        'CreatedTime',
        'Type',
        'Status',
        'SyncAccountID',
        'RefNumber',
        'Extra',
    ];
}
