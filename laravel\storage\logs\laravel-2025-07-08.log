[2025-07-08 02:05:55] local.INFO: header  
[2025-07-08 02:05:55] local.INFO: header  
[2025-07-08 02:05:57] local.INFO: header after fliter  
[2025-07-08 02:05:57] local.INFO: Body  after fliter  
[2025-07-08 02:05:57] local.INFO: Body  after fliter  
[2025-07-08 02:05:57] local.INFO: array (
)  
[2025-07-08 02:05:57] local.INFO: array (
)  
[2025-07-08 02:05:57] local.INFO: transaction14  
[2025-07-08 02:05:57] local.INFO: first inquery phone = 103322192  
[2025-07-08 02:05:57] local.INFO: first inquery phone = 103322192  
[2025-07-08 02:06:00] local.DEBUG: response querySubBalance  
[2025-07-08 02:06:00] local.DEBUG: response querySubBalance  
[2025-07-08 02:06:00] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-08 02:06:00] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-08 02:06:00] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-08 02:06:00] local.DEBUG: print  before faction by provider price  
[2025-07-08 02:06:00] local.DEBUG: print  before faction by provider price  
[2025-07-08 02:06:02] local.DEBUG: print  after faction by provider price  
[2025-07-08 02:06:02] local.DEBUG: print  after faction by provider price  
[2025-07-08 19:17:20] local.INFO: header  
[2025-07-08 19:17:21] local.INFO: header after fliter  
[2025-07-08 19:17:21] local.INFO: Body  after fliter  
[2025-07-08 19:17:21] local.INFO: array (
)  
[2025-07-08 19:17:21] local.INFO: transaction14  
[2025-07-08 19:17:21] local.INFO: first inquery phone = 106443937  
[2025-07-08 19:17:23] local.DEBUG: response querySubBalance  
[2025-07-08 19:17:23] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-08 19:17:23] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-08 19:17:23] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-08 19:17:23] local.DEBUG: print  before faction by provider price  
[2025-07-08 19:17:24] local.DEBUG: print  after faction by provider price  
[2025-07-08 19:17:30] local.INFO: header  
[2025-07-08 19:17:30] local.CRITICAL: ****************************1  
[2025-07-08 19:17:30] local.ALERT: reach here  
[2025-07-08 19:17:30] local.ALERT: array (
  'AMT' => 2400.0,
  'CType' => 0,
  'FID' => 85,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '106443937',
  'State' => 0,
  'lateflog' => '587380',
)  
[2025-07-08 19:17:30] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2025-07-08 19:17:30] local.ERROR: المبلغ  
[2025-07-08 19:17:30] local.ERROR: 2400.00  
[2025-07-08 19:17:30] local.ERROR: مبلغ وقدرة  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 3  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 3  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 3  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 3  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 3  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 3  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 3  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 3  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 3  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 2  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 2  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 2  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 2  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 2  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 2  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 2  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 1  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 1  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 1  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 1  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 1  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 2  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 10013  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 10013  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 10013  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 10013  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 1  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 3  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 3  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 40  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 40  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 40  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 40  
[2025-07-08 19:17:30] local.WARNING: 1  
[2025-07-08 19:17:30] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 40  
[2025-07-08 19:17:30] local.ALERT: 200  
[2025-07-08 19:17:30] local.ALERT: 10013  
[2025-07-08 19:17:30] local.CRITICAL: ****************************  
[2025-07-08 19:17:30] local.CRITICAL:   
[2025-07-08 19:17:30] local.CRITICAL: ****************************  
[2025-07-08 19:17:30] local.CRITICAL: ****************************2  
[2025-07-08 19:17:30] local.INFO: checkUser 1  
[2025-07-08 19:17:31] local.INFO: {
  "ClientBalanceResult": "81472.1091"
}  
[2025-07-08 19:17:31] local.INFO: array (
  'ClientBalanceResult' => '81472.1091',
)  
[2025-07-08 19:17:31] local.DEBUG: lattttef  
[2025-07-08 19:17:31] local.DEBUG: array (
  'ClientBalanceResult' => '81472.1091',
)  
[2025-07-08 19:17:31] local.INFO: transaction14  
[2025-07-08 19:17:31] local.INFO: first inquery phone = 106443937  
[2025-07-08 19:17:31] local.DEBUG: response querySubBalance  
[2025-07-08 19:17:31] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-08 19:17:31] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-08 19:17:31] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-08 19:17:31] local.DEBUG: print  before faction by provider price  
[2025-07-08 19:17:31] local.DEBUG: print  after faction by provider price  
[2025-07-08 19:17:31] local.INFO: thisAttempt to read property "Name" on null  
[2025-07-08 19:18:00] local.INFO: header  
[2025-07-08 19:18:00] local.CRITICAL: ****************************1  
[2025-07-08 19:18:00] local.ALERT: reach here  
[2025-07-08 19:18:00] local.ALERT: array (
  'AMT' => 2400.0,
  'CType' => 0,
  'FID' => 85,
  'LType' => '1',
  'SID' => 200,
  'SNO' => '106443937',
  'State' => 0,
  'lateflog' => '587380',
)  
[2025-07-08 19:18:00] local.ERROR: array (
  0 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '2400.00',
  ),
  1 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألفين وأربعمائة  ر.ي.',
  ),
)  
[2025-07-08 19:18:00] local.ERROR: المبلغ  
[2025-07-08 19:18:00] local.ERROR: 2400.00  
[2025-07-08 19:18:00] local.ERROR: مبلغ وقدرة  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 3  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 3  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 3  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 3  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 3  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 3  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 3  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 3  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 3  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 2  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 2  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 2  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 2  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 2  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 2  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 2  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 1  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 1  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 1  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 1  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 1  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 2  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 10013  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 10013  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 10013  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 10013  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 1  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 3  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 3  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 40  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 40  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 40  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 40  
[2025-07-08 19:18:00] local.WARNING: 1  
[2025-07-08 19:18:00] local.WARNING: array (
  'ID' => 85,
  'Name' => 'فئة 15 جيجا 2400 ريال',
  'ServiceID' => 200,
  'Price' => 2400.0,
  'OrderNo' => 1,
  'Number' => 30,
  'CategoryID' => 1,
  'Description' => NULL,
  'Type' => NULL,
  'ProviderCode' => '30',
  'PersonnalPrice' => 2400.0,
)  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 40  
[2025-07-08 19:18:00] local.ALERT: 200  
[2025-07-08 19:18:00] local.ALERT: 10013  
[2025-07-08 19:18:00] local.CRITICAL: ****************************  
[2025-07-08 19:18:00] local.CRITICAL:   
[2025-07-08 19:18:00] local.CRITICAL: ****************************  
[2025-07-08 19:18:00] local.CRITICAL: ****************************2  
[2025-07-08 19:18:00] local.INFO: checkUser 1  
[2025-07-08 19:18:00] local.INFO: {
  "ClientBalanceResult": "81472.1091"
}  
[2025-07-08 19:18:00] local.INFO: array (
  'ClientBalanceResult' => '81472.1091',
)  
[2025-07-08 19:18:00] local.DEBUG: lattttef  
[2025-07-08 19:18:00] local.DEBUG: array (
  'ClientBalanceResult' => '81472.1091',
)  
[2025-07-08 19:18:00] local.INFO: transaction14  
[2025-07-08 19:18:00] local.INFO: first inquery phone = 106443937  
[2025-07-08 19:18:00] local.DEBUG: response querySubBalance  
[2025-07-08 19:18:00] local.DEBUG: <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><DoOperationResponse xmlns="http://tempuri.org/"><DoOperationResult>OK#0.00#0.00###0#0###</DoOperationResult></DoOperationResponse></s:Body></s:Envelope>  
[2025-07-08 19:18:00] local.DEBUG: OK#0.00#0.00###0#0###  
[2025-07-08 19:18:00] local.DEBUG: array (
  0 => 'OK',
  1 => '0.00',
  2 => '0.00',
  3 => '',
  4 => '',
  5 => '0',
  6 => '0',
  7 => '',
  8 => '',
  9 => '',
)  
[2025-07-08 19:18:00] local.DEBUG: print  before faction by provider price  
[2025-07-08 19:18:00] local.DEBUG: print  after faction by provider price  
[2025-07-08 19:18:00] local.INFO: thisAttempt to read property "Name" on null  
[2025-07-08 20:42:58] local.INFO: header  
[2025-07-08 20:42:58] local.INFO: header after fliter  
[2025-07-08 20:42:58] local.INFO: Body  after fliter  
[2025-07-08 20:42:58] local.INFO: array (
  'Amount' => 260.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-08 20:42:58] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-08 20:42:58] local.ERROR: الكمية  
[2025-07-08 20:42:58] local.ERROR: سعر الوحدة  
[2025-07-08 20:42:58] local.ERROR: المبلغ  
[2025-07-08 20:42:58] local.ERROR: 1.210  
[2025-07-08 20:42:58] local.INFO: 214.88  
[2025-07-08 20:42:58] local.ERROR: مبلغ وقدرة  
[2025-07-08 20:42:58] local.INFO: الكمية  
[2025-07-08 20:42:58] local.INFO: سعر الوحدة  
[2025-07-08 20:42:58] local.INFO: المبلغ  
[2025-07-08 20:42:58] local.INFO: مبلغ وقدرة  
[2025-07-08 20:42:58] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-08 20:42:58] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '214.88',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '260.00',
  ),
)  
[2025-07-08 20:42:58] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '214.88',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '260.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-08 20:43:07] local.INFO: header  
[2025-07-08 20:43:07] local.INFO: header after fliter  
[2025-07-08 20:43:07] local.INFO: Body  after fliter  
[2025-07-08 20:43:07] local.INFO: array (
  'Amount' => 270.0,
  'FactionID' => 0,
  'LType' => 0,
  'LineType' => '2',
  'NetworkID' => 5,
  'ServiceID' => 2,
  'State' => 0,
)  
[2025-07-08 20:43:07] local.INFO: {
  "Items": [
    {
      "Key": "الكمية",
      "Value": "1000"
    },
    {
      "Key": "سعر الوحدة",
      "Value": "1.21"
    },
    {
      "Key": "المبلغ",
      "Value": "1210.00"
    },
    {
      "Key": "مبلغ وقدرة",
      "Value": "ألف ومائتين وعشرة  ر.ي."
    }
  ],
  "Success": true,
  "Message": null,
  "UnitPrice": 0.0,
  "Quantity": 0.0,
  "Amount": 0.0,
  "Commission": 0.0
}  
[2025-07-08 20:43:07] local.ERROR: الكمية  
[2025-07-08 20:43:07] local.ERROR: سعر الوحدة  
[2025-07-08 20:43:07] local.ERROR: المبلغ  
[2025-07-08 20:43:07] local.ERROR: 1.210  
[2025-07-08 20:43:07] local.INFO: 223.14  
[2025-07-08 20:43:07] local.ERROR: مبلغ وقدرة  
[2025-07-08 20:43:07] local.INFO: الكمية  
[2025-07-08 20:43:07] local.INFO: سعر الوحدة  
[2025-07-08 20:43:07] local.INFO: المبلغ  
[2025-07-08 20:43:07] local.INFO: مبلغ وقدرة  
[2025-07-08 20:43:07] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '1000',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => '1.21',
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '1210.00',
  ),
  3 => 
  array (
    'Key' => 'مبلغ وقدرة',
    'Value' => 'ألف ومائتين وعشرة  ر.ي.',
  ),
)  
[2025-07-08 20:43:07] local.INFO: array (
  0 => 
  array (
    'Key' => 'الكمية',
    'Value' => '223.14',
  ),
  1 => 
  array (
    'Key' => 'سعر الوحدة',
    'Value' => 1.21,
  ),
  2 => 
  array (
    'Key' => 'المبلغ',
    'Value' => '270.00',
  ),
)  
[2025-07-08 20:43:07] local.INFO: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '223.14',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => 1.21,
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '270.00',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-08 20:43:10] local.INFO: header  
[2025-07-08 20:43:10] local.CRITICAL: ****************************1  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 3  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 3  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 3  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 3  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 3  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 3  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 3  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 3  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 3  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 1  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 1  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 1  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 1  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 1  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 10013  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 10013  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 10013  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 10013  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 1  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 3  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 3  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 40  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 40  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 40  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 40  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 200  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 200  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 200  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 200  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 200  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 200  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 40  
[2025-07-08 20:43:10] local.ALERT: 2  
[2025-07-08 20:43:10] local.ALERT: 10013  
[2025-07-08 20:43:10] local.CRITICAL: ****************************  
[2025-07-08 20:43:10] local.CRITICAL:   
[2025-07-08 20:43:10] local.CRITICAL: ****************************  
[2025-07-08 20:43:10] local.CRITICAL: ****************************2  
[2025-07-08 20:43:10] local.INFO: checkUser 1  
[2025-07-08 20:43:12] local.INFO: {
  "ClientBalanceResult": "97167.9681"
}  
[2025-07-08 20:43:12] local.INFO: checkUser 2  
[2025-07-08 20:43:12] local.INFO: array (
  'ClientBalanceResult' => '97167.9681',
)  
[2025-07-08 20:43:12] local.INFO: 270  
[2025-07-08 20:43:12] local.ALERT: reach here  
[2025-07-08 20:43:12] local.ERROR: array (
  'Items' => 
  array (
    0 => 
    array (
      'Key' => 'الكمية',
      'Value' => '1000',
    ),
    1 => 
    array (
      'Key' => 'سعر الوحدة',
      'Value' => '1.21',
    ),
    2 => 
    array (
      'Key' => 'المبلغ',
      'Value' => '1210.00',
    ),
    3 => 
    array (
      'Key' => 'مبلغ وقدرة',
      'Value' => 'ألف ومائتين وعشرة  ر.ي.',
    ),
  ),
  'Success' => true,
  'Message' => NULL,
  'UnitPrice' => 0.0,
  'Quantity' => 0.0,
  'Amount' => 0.0,
  'Commission' => 0.0,
)  
[2025-07-08 20:43:12] local.ERROR: الكمية  
[2025-07-08 20:43:12] local.ERROR: سعر الوحدة  
[2025-07-08 20:43:12] local.ERROR: المبلغ  
[2025-07-08 20:43:12] local.ERROR: 1.210  
[2025-07-08 20:43:12] local.INFO: 1.210  
[2025-07-08 20:43:12] local.INFO: 223.140  
[2025-07-08 20:43:12] local.ERROR: 270.00  
[2025-07-08 20:43:12] local.INFO: checkUser 3  
[2025-07-08 20:43:12] local.INFO: checkUser 3#  
[2025-07-08 20:43:12] local.INFO: checkUser 4  
[2025-07-08 20:43:12] local.INFO: checkUser 4#  
[2025-07-08 20:43:12] local.INFO: checkUser 5  
[2025-07-08 20:43:12] local.DEBUG: lattttef  
[2025-07-08 20:43:12] local.DEBUG: array (
  'ClientBalanceResult' => '97167.9681',
)  
[2025-07-08 20:43:12] local.INFO: transaction1  
[2025-07-08 20:43:12] local.INFO: transaction2  
[2025-07-08 20:43:12] local.INFO: transaction3  
[2025-07-08 20:43:12] local.INFO: transaction4  
[2025-07-08 20:43:12] local.INFO: transaction4  
[2025-07-08 20:43:12] local.INFO: transaction5  
[2025-07-08 20:43:12] local.INFO: transaction6  
[2025-07-08 20:43:12] local.INFO: transaction7  
[2025-07-08 20:43:12] local.DEBUG: array (
  'AMT' => 270.0,
  'CType' => 5,
  'FID' => 0,
  'LType' => '1',
  'SID' => 2,
  'SNO' => '*********',
  'State' => 0,
  'lateflog' => '561844',
  'mLtype' => '1',
  'LATEFnum' => '223.140',
)  
[2025-07-08 20:43:12] local.INFO: transaction8  
[2025-07-08 20:43:13] local.INFO: transaction9  
[2025-07-08 20:43:13] local.INFO: 270  
[2025-07-08 20:43:13] local.INFO: transaction10  
[2025-07-08 20:43:13] local.INFO: 223.140  
[2025-07-08 20:43:13] local.INFO: 270.00  
[2025-07-08 20:43:13] local.INFO: transaction11  
[2025-07-08 20:43:13] local.INFO: 121  
[2025-07-08 20:43:13] local.INFO: topup1270.00  
[2025-07-08 20:43:13] local.INFO: topup21.210  
[2025-07-08 20:43:13] local.INFO: topup3270.00  
[2025-07-08 20:43:13] local.INFO: topup4270.00  
[2025-07-08 20:43:13] local.INFO: topup5270  
[2025-07-08 20:43:13] local.INFO: topup60  
[2025-07-08 20:43:13] local.INFO: array (
  'Number' => ********,
  'ServiceID' => 2,
  'NetworkID' => NULL,
  'SubscriberNumber' => '*********',
  'Amount' => 270.0,
  'FactionID' => 0,
  'RegionID' => NULL,
  'LineType' => '1',
  'Date' => '2025-07-08 20:43:13',
  'Status' => 2,
  'Note' => NULL,
  'CreditorAccountID' => 519814,
  'CurrencyID' => 1,
  'DebitorAccountID' => '561844',
  'AgentID' => NULL,
  'RefNumber' => NULL,
  'TransactionID' => ********,
  'ProviderID' => 10031,
  'EntryID' => 7339127,
  'PaymentEntryID' => NULL,
  'Channel' => 2,
  'CreatedBy' => '558688',
  'BranchBy' => NULL,
  'CreatedTime' => '2025-07-08 20:43:13',
  'BranchID' => '1',
  'ProviderRM' => '',
  'ProviderPrice' => '270.00',
  'SubNote' => NULL,
  'Datestamb' => '********',
  'UniqueNo' => '********204313',
  'Quantity' => '223.140',
  'UnitPrice' => '1.210',
  'UnitCost' => 1.21,
  'CostAmount' => '270.00',
  'DifferentialAmount' => 0.0,
  'CommissionAmount' => 0.0,
  'Discount' => 0.0,
  'TotalCost' => '270.00',
  'TotalAmount' => 270.0,
  'Profits' => 0.0,
  'Method' => 2,
  'Type' => 0,
  'Class' => 0,
  'LType' => '1',
  'OperatorID' => 1,
  'AppTechApi' => 0,
  'BillNumber' => '200********',
  'BillState' => 0,
  'Debited' => 1,
  'ByChild' => 0,
  'IsDirect' => 1,
  'BundleName' => 'ام تي ان رصيد مفتوح',
  'BundleCode' => 270.0,
  'ExCode' => NULL,
  'TransNumber' => '********204313',
  'OperationID' => 0,
  'AccountID' => '561844',
  'State' => 0,
  'StateClass' => '',
  'Identifier' => 'a9e6dd5e8af64633',
  'AdminNote' => '',
  'AccountNote' => '',
  'Description' => NULL,
  'Responded' => 0,
  'RequestInfo' => '2#*********#270#0',
  'ResponseTime' => '2025-07-08 20:43:13',
  'ResponseStatus' => 0,
  'ExecutionPeroid' => '13',
  'FaildRequest' => 0,
  'FailedReason' => NULL,
  'FailedType' => 0,
  'Cured' => 0,
  'CuredBy' => NULL,
  'CuredInfo' => NULL,
  'InspectInfo' => NULL,
  'Flag' => 2,
  'Action' => 0,
  'QuotaionID' => 0,
  'SyncID' => 0,
)  
[2025-07-08 20:43:13] local.INFO: transaction13  
[2025-07-08 20:43:13] local.INFO: transaction14  
[2025-07-08 20:43:13] local.INFO: transaction19  
[2025-07-08 20:43:13] local.INFO: transaction19#.  
[2025-07-08 20:43:13] local.INFO: transaction19#.  
[2025-07-08 20:43:13] local.INFO: transaction19#  
[2025-07-08 20:43:13] local.INFO: transaction19##  
[2025-07-08 20:43:13] local.INFO: transaction15  
[2025-07-08 20:43:16] local.INFO: array (
  'resultCode' => '0',
  'resultDesc' => 'success',
  'price' => '270.00',
  'remainAmount' => ********,
  'mallrem' => -1218450,
  'transid' => '7339127',
  'ref_id' => 93308388,
)  
